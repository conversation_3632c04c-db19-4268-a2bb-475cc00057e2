# Code Style and Conventions

## Go Code Style
- **Go Version**: 1.23.2
- **Package Structure**: Module-based under `internal/modules/`
- **Import Paths**: Use full paths like `github.com/tranthanhloi/wn-api-v3/internal/modules/...`
- **Formatting**: Use `go fmt` (via `make fmt`)
- **Linting**: golangci-lint (via `make lint`)

## Naming Conventions
- **Files**: Snake_case (e.g., `auth_handler.go`, `email_verification_token.go`)
- **Packages**: Lowercase single word (e.g., `handlers`, `models`, `services`)
- **Types/Structs**: PascalCase (e.g., `Auth<PERSON>andler`, `TokenResponse`)
- **Interfaces**: PascalCase with descriptive names (e.g., `AuthService`, `TokenRepository`)
- **Constants**: PascalCase or UPPER_SNAKE_CASE for enums
- **Methods**: PascalCase for exported, camelCase for unexported

## Code Organization
```
internal/modules/{module}/
├── models/         # Domain models, DTOs, request/response types
├── repositories/   # Data access layer
├── services/       # Business logic
├── handlers/       # HTTP handlers
└── routes.go      # Route registration
```

## Database Conventions
- **Tables**: Module prefix + snake_case (e.g., `auth_tokens`, `user_profiles`)
- **ID Fields**: Use `INT UNSIGNED` (not BIGINT)
- **Timestamps**: `created_at`, `updated_at` with `DEFAULT CURRENT_TIMESTAMP`
- **Soft Delete**: Status-based (e.g., `status = 'deleted'`), NOT `deleted_at`
- **JSON Fields**: Use `JSON DEFAULT (JSON_OBJECT())`


## Error Handling
- Use structured errors with proper HTTP status codes
- Return meaningful error messages
- Log errors with context using the logger

## Testing Conventions
- Test files: `*_test.go` in same package
- Table-driven tests preferred
- Mock interfaces for dependencies
- Integration tests in `tests/integration/`

## Documentation
- Swagger annotations for API endpoints
- Godoc comments for exported functions/types
- README files for each module in `docs/modules/`

## Security Best Practices
- Never log sensitive data (passwords, tokens)
- Use parameterized queries for SQL
- Validate all inputs
- Implement rate limiting
- Use secure random generators for tokens

## Multi-Tenancy Rules
- All tenant-scoped tables must have `tenant_id` field
- Include tenant_id in unique constraints
- Foreign key to tenants table with CASCADE delete
- Always filter by tenant_id in queries

## Comments
- **IMPORTANT**: DO NOT ADD ANY COMMENTS unless explicitly asked
- Only add comments when user specifically requests them
- Focus on self-documenting code with clear naming