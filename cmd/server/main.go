// @title Blog API v3
// @version 3.0.0
// @description This is a comprehensive blog API with multi-tenant support.
// @termsOfService http://swagger.io/terms/

// @contact.name Tran Thanh Loi
// @contact.email <EMAIL>
// @contact.url https://github.com/tranthanhloi

// @license.name MIT
// @license.url http://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /

// @securityDefinitions.apikey Bearer
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.
package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"
	"github.com/tranthanhloi/wn-api-v3/internal/api"
	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	// Import docs for Swagger
	// _ "github.com/tranthanhloi/wn-api-v3/docs"
)

func main() {
	// Load .env file if it exists
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Warning: Failed to load .env file: %v", err)
		} else {
			log.Println("Loaded environment variables from .env file")
		}
	}

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger with beautiful formatting and file output
	loggerConfig := utils.LoggerConfig{
		Level:        cfg.Log.Level,
		Format:       "console", // Use custom console format for better readability
		Output:       nil,       // Use stdout + file
		ReportCaller: false,     // Disable caller info for cleaner logs
		// File logging configuration
		LogToFile:   true,
		LogFilePath: "./logs/app.log",
		MaxSize:     50,   // 50 MB per file
		MaxBackups:  10,   // Keep 10 backup files
		MaxAge:      30,   // Keep files for 30 days
		Compress:    true, // Compress old files
		DefaultFields: utils.Fields{
			"service": "blog-api-v3",
			"version": "3.0.0",
		},
	}
	logger := utils.NewLoggerWithConfig(loggerConfig)
	logger.Info("🚀 Starting Blog API Server with beautiful logs...")
	logger.WithFields(utils.Fields{
		"log_file": "./logs/app.log",
		"rotation": "50MB, 10 backups, 30 days",
	}).Info("📝 File logging enabled with rotation")

	// Initialize database
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Database,
		cfg.Database.Charset,
	)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		logger.WithError(err).Warn("Failed to connect to database, running in mock mode")
		db = nil
	} else {
		// Test connection
		if err := db.Ping(); err != nil {
			logger.WithError(err).Warn("Failed to ping database, running in mock mode")
			db = nil
		} else {
			logger.Info("✅ Database connection established")
		}
	}

	// Initialize validator
	if err := validator.Initialize(); err != nil {
		logger.WithError(err).Fatal("Failed to initialize validator")
	}
	v := validator.GetDefaultValidator()

	// Initialize API server
	server, err := api.NewServer(cfg, db, v, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize API server")
	}

	// Start blog post scheduler if enabled
	if cfg.Features.BlogScheduler.Enabled {
		go startBlogScheduler(server, cfg, logger)
	}

	// Start server in a goroutine
	go func() {
		addr := fmt.Sprintf(":%d", cfg.Server.Port)
		fullURL := fmt.Sprintf("http://localhost%s", addr)
		logger.WithField("url", fullURL).Info("🌐 Starting HTTP server - Click to open")

		if err := server.Start(addr); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Create a deadline to wait for
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		logger.WithError(err).Fatal("Server forced to shutdown")
	}

	logger.Info("Server exited")
}

// startBlogScheduler starts the blog post scheduler
func startBlogScheduler(server *api.Server, cfg *config.Config, logger utils.Logger) {
	ticker := time.NewTicker(cfg.Features.BlogScheduler.Interval)
	defer ticker.Stop()

	logger.WithFields(map[string]interface{}{
		"interval":   cfg.Features.BlogScheduler.Interval.String(),
		"batch_size": cfg.Features.BlogScheduler.BatchSize,
	}).Info("📅 Blog post scheduler started")

	for {
		select {
		case <-ticker.C:
			ctx := context.Background()
			if err := server.ProcessPendingBlogSchedules(ctx); err != nil {
				logger.WithError(err).Error("Failed to process blog schedules")
			}
		}
	}
}
