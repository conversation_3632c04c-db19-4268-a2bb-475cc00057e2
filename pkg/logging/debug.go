package logging

import (
	"context"
	"net/http"
	"runtime"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// DebugLogger implements debug logging features
type DebugLogger struct {
	logger Logger
	config *DebugConfig
}

// NewDebugLogger creates a new debug logger
func NewDebugLogger(logger Logger, config *DebugConfig) *DebugLogger {
	if config == nil {
		config = &DebugConfig{
			Enabled:             false,
			VerboseSQL:          false,
			RequestBodyLogging:  false,
			ResponseBodyLogging: false,
			SlowQueryThreshold:  100 * time.Millisecond,
			TraceSampling:       1.0,
		}
	}

	return &DebugLogger{
		logger: logger,
		config: config,
	}
}

// IsEnabled returns whether debug logging is enabled
func (d *DebugLogger) IsEnabled() bool {
	return d.config.Enabled
}

// LogSQLQuery logs SQL queries with performance metrics
func (d *DebugLogger) LogSQLQuery(ctx context.Context, query string, args []interface{}, duration time.Duration, err error) {
	if !d.config.Enabled || !d.config.VerboseSQL {
		return
	}

	fields := []Field{
		{Key: "query", Value: query},
		{Key: "args", Value: args},
		{Key: "duration", Value: duration},
		{Key: "duration_ms", Value: duration.Milliseconds()},
		{Key: "slow_query", Value: duration > d.config.SlowQueryThreshold},
	}

	if err != nil {
		fields = append(fields, Field{Key: "error", Value: err.Error()})
		d.logger.WithContext(ctx).Error("sql query failed", fields...)
	} else {
		level := "debug"
		if duration > d.config.SlowQueryThreshold {
			level = "warn"
		}

		if level == "warn" {
			d.logger.WithContext(ctx).Warn("slow sql query", fields...)
		} else {
			d.logger.WithContext(ctx).Debug("sql query executed", fields...)
		}
	}
}

// LogHTTPRequest logs detailed HTTP request information
func (d *DebugLogger) LogHTTPRequest(ctx context.Context, req *http.Request, body []byte) {
	if !d.config.Enabled || !d.config.RequestBodyLogging {
		return
	}

	fields := []Field{
		{Key: "method", Value: req.Method},
		{Key: "url", Value: req.URL.String()},
		{Key: "headers", Value: req.Header},
		{Key: "content_length", Value: req.ContentLength},
		{Key: "host", Value: req.Host},
		{Key: "remote_addr", Value: req.RemoteAddr},
		{Key: "user_agent", Value: req.UserAgent()},
		{Key: "referer", Value: req.Referer()},
	}

	if body != nil && len(body) > 0 {
		fields = append(fields, Field{Key: "body", Value: string(body)})
		fields = append(fields, Field{Key: "body_size", Value: len(body)})
	}

	d.logger.WithContext(ctx).Debug("http request details", fields...)
}

// LogHTTPResponse logs detailed HTTP response information
func (d *DebugLogger) LogHTTPResponse(ctx context.Context, status int, headers map[string][]string, body []byte, duration time.Duration) {
	if !d.config.Enabled || !d.config.ResponseBodyLogging {
		return
	}

	fields := []Field{
		{Key: "status", Value: status},
		{Key: "headers", Value: headers},
		{Key: "duration", Value: duration},
		{Key: "duration_ms", Value: duration.Milliseconds()},
	}

	if body != nil && len(body) > 0 {
		fields = append(fields, Field{Key: "body", Value: string(body)})
		fields = append(fields, Field{Key: "body_size", Value: len(body)})
	}

	d.logger.WithContext(ctx).Debug("http response details", fields...)
}

// LogMemoryStats logs current memory statistics
func (d *DebugLogger) LogMemoryStats(ctx context.Context) {
	if !d.config.Enabled {
		return
	}

	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	fields := []Field{
		{Key: "alloc_mb", Value: bToMb(m.Alloc)},
		{Key: "total_alloc_mb", Value: bToMb(m.TotalAlloc)},
		{Key: "sys_mb", Value: bToMb(m.Sys)},
		{Key: "num_gc", Value: m.NumGC},
		{Key: "num_goroutine", Value: runtime.NumGoroutine()},
		{Key: "heap_alloc_mb", Value: bToMb(m.HeapAlloc)},
		{Key: "heap_sys_mb", Value: bToMb(m.HeapSys)},
		{Key: "heap_idle_mb", Value: bToMb(m.HeapIdle)},
		{Key: "heap_inuse_mb", Value: bToMb(m.HeapInuse)},
		{Key: "heap_released_mb", Value: bToMb(m.HeapReleased)},
		{Key: "heap_objects", Value: m.HeapObjects},
		{Key: "stack_inuse_mb", Value: bToMb(m.StackInuse)},
		{Key: "stack_sys_mb", Value: bToMb(m.StackSys)},
		{Key: "gc_pause_ns", Value: m.PauseNs[(m.NumGC+255)%256]},
	}

	d.logger.WithContext(ctx).Debug("memory stats", fields...)
}

// LogGoroutineProfile logs goroutine information
func (d *DebugLogger) LogGoroutineProfile(ctx context.Context) {
	if !d.config.Enabled {
		return
	}

	numGoroutines := runtime.NumGoroutine()

	// Get stack traces if requested
	buf := make([]byte, 1<<16)
	stackLen := runtime.Stack(buf, true)

	fields := []Field{
		{Key: "num_goroutines", Value: numGoroutines},
		{Key: "stack_trace_size", Value: stackLen},
	}

	// Only include full stack trace if goroutine count is concerning
	if numGoroutines > 1000 {
		fields = append(fields, Field{Key: "stack_trace", Value: string(buf[:stackLen])})
		d.logger.WithContext(ctx).Warn("high goroutine count", fields...)
	} else {
		d.logger.WithContext(ctx).Debug("goroutine stats", fields...)
	}
}

// bToMb converts bytes to megabytes
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

// RequestTracer provides detailed request tracing
type RequestTracer struct {
	logger Logger
	config *DebugConfig
	traces sync.Map // map[string]*TraceInfo
}

// TraceInfo holds trace information
type TraceInfo struct {
	TraceID string
	Start   time.Time
	Events  []TraceEvent
	mu      sync.RWMutex
}

// TraceEvent represents a trace event
type TraceEvent struct {
	Timestamp time.Time
	Event     string
	Duration  time.Duration
	Fields    map[string]interface{}
}

// NewRequestTracer creates a new request tracer
func NewRequestTracer(logger Logger, config *DebugConfig) *RequestTracer {
	return &RequestTracer{
		logger: logger,
		config: config,
	}
}

// StartTrace starts a new trace
func (t *RequestTracer) StartTrace(ctx context.Context, operation string) context.Context {
	if !t.config.Enabled {
		return ctx
	}

	traceID := uuid.New().String()
	trace := &TraceInfo{
		TraceID: traceID,
		Start:   time.Now(),
		Events:  []TraceEvent{},
	}

	// Store trace
	t.traces.Store(traceID, trace)

	// Add initial event
	trace.AddEvent("trace_started", 0, map[string]interface{}{
		"operation": operation,
	})

	// Add to context
	ctx = context.WithValue(ctx, "trace_id", traceID)

	t.logger.WithContext(ctx).Debug("trace started",
		Field{Key: "trace_id", Value: traceID},
		Field{Key: "operation", Value: operation},
	)

	return ctx
}

// AddEvent adds an event to the current trace
func (t *RequestTracer) AddEvent(ctx context.Context, event string, fields map[string]interface{}) {
	if !t.config.Enabled {
		return
	}

	traceID, ok := ctx.Value("trace_id").(string)
	if !ok {
		return
	}

	traceInfo, exists := t.traces.Load(traceID)
	if !exists {
		return
	}

	trace := traceInfo.(*TraceInfo)
	duration := time.Since(trace.Start)

	trace.AddEvent(event, duration, fields)

	t.logger.WithContext(ctx).Debug("trace event",
		Field{Key: "trace_id", Value: traceID},
		Field{Key: "event", Value: event},
		Field{Key: "duration", Value: duration},
		Field{Key: "fields", Value: fields},
	)
}

// EndTrace ends a trace and logs the summary
func (t *RequestTracer) EndTrace(ctx context.Context) {
	if !t.config.Enabled {
		return
	}

	traceID, ok := ctx.Value("trace_id").(string)
	if !ok {
		return
	}

	traceInfo, exists := t.traces.Load(traceID)
	if !exists {
		return
	}

	trace := traceInfo.(*TraceInfo)
	totalDuration := time.Since(trace.Start)

	trace.AddEvent("trace_ended", totalDuration, nil)

	// Log trace summary
	t.logger.WithContext(ctx).Info("trace completed",
		Field{Key: "trace_id", Value: traceID},
		Field{Key: "total_duration", Value: totalDuration},
		Field{Key: "total_events", Value: len(trace.Events)},
		Field{Key: "events", Value: trace.Events},
	)

	// Clean up
	t.traces.Delete(traceID)
}

// AddEvent adds an event to the trace
func (t *TraceInfo) AddEvent(event string, duration time.Duration, fields map[string]interface{}) {
	t.mu.Lock()
	defer t.mu.Unlock()

	t.Events = append(t.Events, TraceEvent{
		Timestamp: time.Now(),
		Event:     event,
		Duration:  duration,
		Fields:    fields,
	})
}

// DebugEndpoints provides debug endpoints for development
type DebugEndpoints struct {
	logger Logger
	config *DebugConfig
}

// NewDebugEndpoints creates debug endpoints
func NewDebugEndpoints(logger Logger, config *DebugConfig) *DebugEndpoints {
	return &DebugEndpoints{
		logger: logger,
		config: config,
	}
}

// SetupDebugEndpoints sets up debug endpoints
func (d *DebugEndpoints) SetupDebugEndpoints(router *gin.Engine) {
	if !d.config.Enabled {
		return
	}

	debug := router.Group("/debug")

	// Add basic auth or other protection mechanism
	debug.Use(d.debugAuthMiddleware())

	// Memory stats endpoint
	debug.GET("/memory", d.memoryStatsHandler)

	// Goroutines endpoint
	debug.GET("/goroutines", d.goroutinesHandler)

	// Configuration endpoint
	debug.GET("/config", d.configHandler)

	// Logger stats endpoint
	debug.GET("/logger/stats", d.loggerStatsHandler)

	// Force GC endpoint
	debug.POST("/gc", d.forceGCHandler)

	// Health check endpoint
	debug.GET("/health", d.healthCheckHandler)
}

// debugAuthMiddleware provides basic authentication for debug endpoints
func (d *DebugEndpoints) debugAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// In production, this should use proper authentication
		// For debug mode, we can use a simple check
		if !d.config.Enabled {
			c.AbortWithStatusJSON(404, gin.H{"error": "Not found"})
			return
		}
		c.Next()
	}
}

// memoryStatsHandler handles memory stats requests
func (d *DebugEndpoints) memoryStatsHandler(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	stats := map[string]interface{}{
		"alloc_mb":         bToMb(m.Alloc),
		"total_alloc_mb":   bToMb(m.TotalAlloc),
		"sys_mb":           bToMb(m.Sys),
		"num_gc":           m.NumGC,
		"num_goroutine":    runtime.NumGoroutine(),
		"heap_alloc_mb":    bToMb(m.HeapAlloc),
		"heap_sys_mb":      bToMb(m.HeapSys),
		"heap_idle_mb":     bToMb(m.HeapIdle),
		"heap_inuse_mb":    bToMb(m.HeapInuse),
		"heap_released_mb": bToMb(m.HeapReleased),
		"heap_objects":     m.HeapObjects,
		"stack_inuse_mb":   bToMb(m.StackInuse),
		"stack_sys_mb":     bToMb(m.StackSys),
		"gc_pause_ns":      m.PauseNs[(m.NumGC+255)%256],
		"last_gc":          time.Unix(0, int64(m.LastGC)),
		"gc_cpu_fraction":  m.GCCPUFraction,
	}

	c.JSON(200, stats)
}

// goroutinesHandler handles goroutine information requests
func (d *DebugEndpoints) goroutinesHandler(c *gin.Context) {
	numGoroutines := runtime.NumGoroutine()

	response := map[string]interface{}{
		"num_goroutines": numGoroutines,
		"timestamp":      time.Now(),
	}

	// Include stack trace if requested
	if c.Query("stack") == "true" {
		buf := make([]byte, 1<<20) // 1MB buffer
		stackLen := runtime.Stack(buf, true)
		response["stack_trace"] = string(buf[:stackLen])
	}

	c.JSON(200, response)
}

// configHandler handles configuration requests
func (d *DebugEndpoints) configHandler(c *gin.Context) {
	config := map[string]interface{}{
		"debug_enabled":         d.config.Enabled,
		"verbose_sql":           d.config.VerboseSQL,
		"request_body_logging":  d.config.RequestBodyLogging,
		"response_body_logging": d.config.ResponseBodyLogging,
		"slow_query_threshold":  d.config.SlowQueryThreshold,
		"memory_profiling":      d.config.MemoryProfiling,
		"cpu_profiling":         d.config.CPUProfiling,
		"trace_sampling":        d.config.TraceSampling,
	}

	c.JSON(200, config)
}

// loggerStatsHandler handles logger statistics requests
func (d *DebugEndpoints) loggerStatsHandler(c *gin.Context) {
	// This would return actual logger statistics
	// For now, return basic info
	stats := map[string]interface{}{
		"timestamp": time.Now(),
		"status":    "active",
	}

	c.JSON(200, stats)
}

// forceGCHandler forces garbage collection
func (d *DebugEndpoints) forceGCHandler(c *gin.Context) {
	var before, after runtime.MemStats
	runtime.ReadMemStats(&before)

	runtime.GC()

	runtime.ReadMemStats(&after)

	result := map[string]interface{}{
		"before_alloc_mb": bToMb(before.Alloc),
		"after_alloc_mb":  bToMb(after.Alloc),
		"freed_mb":        bToMb(before.Alloc - after.Alloc),
		"timestamp":       time.Now(),
	}

	d.logger.Info("manual gc triggered",
		Field{Key: "before_alloc_mb", Value: bToMb(before.Alloc)},
		Field{Key: "after_alloc_mb", Value: bToMb(after.Alloc)},
		Field{Key: "freed_mb", Value: bToMb(before.Alloc - after.Alloc)},
	)

	c.JSON(200, result)
}

// healthCheckHandler provides health check endpoint
func (d *DebugEndpoints) healthCheckHandler(c *gin.Context) {
	health := map[string]interface{}{
		"status":     "healthy",
		"timestamp":  time.Now(),
		"uptime":     time.Since(startTime),
		"goroutines": runtime.NumGoroutine(),
		"memory_mb":  func() uint64 { var m runtime.MemStats; runtime.ReadMemStats(&m); return bToMb(m.Alloc) }(),
	}

	c.JSON(200, health)
}

// Package-level variable to track start time
var startTime = time.Now()

// SetupDebugMode configures debug mode for the application
func SetupDebugMode(router *gin.Engine, logger Logger, config *DebugConfig) {
	if !config.Enabled {
		return
	}

	// Setup debug endpoints
	endpoints := NewDebugEndpoints(logger, config)
	endpoints.SetupDebugEndpoints(router)

	// Log debug mode activation
	logger.Info("debug mode activated",
		Field{Key: "verbose_sql", Value: config.VerboseSQL},
		Field{Key: "request_body_logging", Value: config.RequestBodyLogging},
		Field{Key: "response_body_logging", Value: config.ResponseBodyLogging},
		Field{Key: "slow_query_threshold", Value: config.SlowQueryThreshold},
	)
}
