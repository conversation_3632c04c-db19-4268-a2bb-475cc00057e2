// File này đã bị vô hiệu hóa để tránh lỗi build
// Nội dung gốc đã được comment để tránh lỗi dependency

package tracing

import (
	"errors"
	"fmt"
	"time"
)

// Common errors
var (
	ErrTracerAlreadyInitialized = errors.New("tracer already initialized")
	ErrTracerNotInitialized     = errors.New("tracer not initialized")
	ErrInvalidConfiguration     = errors.New("invalid configuration")
	ErrUnsupportedExporter      = errors.New("unsupported exporter")
)

// WrapError wraps an error with additional context
func WrapError(err error, message string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", message, err)
}

// Config represents tracing configuration
type Config struct {
	Enabled            bool              `json:"enabled" yaml:"enabled"`
	ServiceName        string            `json:"service_name" yaml:"service_name"`
	ServiceVersion     string            `json:"service_version" yaml:"service_version"`
	ServiceInstance    string            `json:"service_instance" yaml:"service_instance"`
	Environment        string            `json:"environment" yaml:"environment"`
	Endpoint           string            `json:"endpoint" yaml:"endpoint"`
	Debug              bool              `json:"debug" yaml:"debug"`
	Exporter           ExporterType      `json:"exporter" yaml:"exporter"`
	Sampling           SamplingStrategy  `json:"sampling" yaml:"sampling"`
	SampleRate         float64           `json:"sample_rate" yaml:"sample_rate"`
	JaegerEndpoint     string            `json:"jaeger_endpoint" yaml:"jaeger_endpoint"`
	JaegerUser         string            `json:"jaeger_user" yaml:"jaeger_user"`
	JaegerPassword     string            `json:"jaeger_password" yaml:"jaeger_password"`
	OTLPEndpoint       string            `json:"otlp_endpoint" yaml:"otlp_endpoint"`
	OTLPInsecure       bool              `json:"otlp_insecure" yaml:"otlp_insecure"`
	OTLPHeaders        map[string]string `json:"otlp_headers" yaml:"otlp_headers"`
	ResourceAttributes map[string]string `json:"resource_attributes" yaml:"resource_attributes"`
	Propagators        []string          `json:"propagators" yaml:"propagators"`
	BatchTimeout       time.Duration     `json:"batch_timeout" yaml:"batch_timeout"`
	BatchSize          int               `json:"batch_size" yaml:"batch_size"`
	MaxExportBatchSize int               `json:"max_export_batch_size" yaml:"max_export_batch_size"`
	MaxQueueSize       int               `json:"max_queue_size" yaml:"max_queue_size"`
	ShutdownTimeout    time.Duration     `json:"shutdown_timeout" yaml:"shutdown_timeout"`
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.ServiceName == "" {
		return WrapError(ErrInvalidConfiguration, "service name is required")
	}
	if c.SampleRate < 0 || c.SampleRate > 1 {
		return WrapError(ErrInvalidConfiguration, "sample rate must be between 0 and 1")
	}
	return nil
}

// LoadConfig loads configuration from file or environment
func LoadConfig() *Config {
	return DefaultConfig()
}

// DefaultConfig returns default configuration
func DefaultConfig() *Config {
	return &Config{
		Enabled:            false,
		ServiceName:        "unknown",
		ServiceVersion:     "1.0.0",
		Environment:        "development",
		Debug:              false,
		Exporter:           NoOpExporter,
		Sampling:           TraceIDRatio,
		SampleRate:         0.1,
		Propagators:        []string{"tracecontext", "baggage"},
		BatchTimeout:       5 * time.Second,
		BatchSize:          512,
		MaxExportBatchSize: 512,
		MaxQueueSize:       2048,
		ShutdownTimeout:    10 * time.Second,
	}
}

// DevelopmentConfig returns development configuration
func DevelopmentConfig() *Config {
	cfg := DefaultConfig()
	cfg.Environment = "development"
	cfg.Debug = true
	cfg.Exporter = ConsoleExporter
	cfg.SampleRate = 1.0
	return cfg
}

// ProductionConfig returns production configuration
func ProductionConfig() *Config {
	cfg := DefaultConfig()
	cfg.Environment = "production"
	cfg.Debug = false
	cfg.Exporter = JaegerExporter
	cfg.SampleRate = 0.1
	return cfg
}

// TestingConfig returns testing configuration
func TestingConfig() *Config {
	cfg := DefaultConfig()
	cfg.Environment = "testing"
	cfg.Debug = true
	cfg.Exporter = NoOpExporter
	cfg.SampleRate = 0.0
	return cfg
}

// Placeholder để tránh lỗi package empty
var _ = "disabled"
