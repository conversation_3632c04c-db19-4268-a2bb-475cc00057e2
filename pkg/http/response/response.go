package response

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// ============================================================================
// Core Response Types
// ============================================================================

// Status contains response status information
type Status struct {
	Code      int      `json:"code"`
	Message   string   `json:"message"`
	Success   bool     `json:"success"`
	ErrorCode string   `json:"error_code,omitempty"`
	Path      string   `json:"path"`
	Timestamp string   `json:"timestamp"`
	Details   []Detail `json:"details,omitempty"`
}

// Meta contains metadata for paginated responses
type Meta struct {
	HasNext    bool   `json:"has_next"`
	NextCursor string `json:"next_cursor"`
}

// Response represents the standard API response format
type Response struct {
	Status Status      `json:"status"`
	Data   interface{} `json:"data,omitempty"`
}

type ResponseMeta struct {
	Status Status      `json:"status"`
	Data   interface{} `json:"data,omitempty"`
	Meta   *Meta       `json:"meta,omitempty"`
}

// Detail represents error details with optional field and code
type Detail struct {
	Field   string `json:"field,omitempty"`   // For validation errors
	Code    string `json:"code,omitempty"`    // Error code for programmatic handling
	Message string `json:"message"`           // Human readable message
}

// ResponseWriter provides helper methods for HTTP responses
type ResponseWriter struct {
	http.ResponseWriter
	written bool
}

// NewResponse wraps an http.ResponseWriter with helper methods
func NewResponse(w http.ResponseWriter) *ResponseWriter {
	return &ResponseWriter{ResponseWriter: w}
}

// JSON sends a JSON response with the given status code
func (r *ResponseWriter) JSON(status int, data interface{}) error {
	r.Header().Set("Content-Type", "application/json; charset=utf-8")
	r.WriteHeader(status)
	r.written = true

	if data == nil {
		return nil
	}

	encoder := json.NewEncoder(r)
	encoder.SetIndent("", "  ")
	return encoder.Encode(data)
}

// Success sends a success response
func (r *ResponseWriter) Success(data interface{}) error {
	response := Response{
		Status: Status{
			Code:      http.StatusOK,
			Message:   "Success",
			Success:   true,
			Path:      "",
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
	}
	return r.JSON(http.StatusOK, response)
}

// SuccessWithPath sends a success response with path
func (r *ResponseWriter) SuccessWithPath(data interface{}, path string) error {
	response := Response{
		Status: Status{
			Code:      http.StatusOK,
			Message:   "Success",
			Success:   true,
			Path:      path,
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
	}
	return r.JSON(http.StatusOK, response)
}

// Created sends a created response
func (r *ResponseWriter) Created(data interface{}) error {
	response := Response{
		Status: Status{
			Code:      http.StatusCreated,
			Message:   "Created",
			Success:   true,
			Path:      "",
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
	}
	return r.JSON(http.StatusCreated, response)
}

// NoContent sends a no content response
func (r *ResponseWriter) NoContent() error {
	r.WriteHeader(http.StatusNoContent)
	r.written = true
	return nil
}

// Error sends an error response
func (r *ResponseWriter) Error(status int, message string) error {
	response := Response{
		Status: Status{
			Code:      status,
			Message:   message,
			Success:   false,
			ErrorCode: getErrorCode(status),
			Path:      "",
			Timestamp: time.Now().Format(time.RFC3339),
		},
	}
	return r.JSON(status, response)
}

// ErrorWithPath sends an error response with path
func (r *ResponseWriter) ErrorWithPath(status int, message string, path string) error {
	response := Response{
		Status: Status{
			Code:      status,
			Message:   message,
			Success:   false,
			ErrorCode: getErrorCode(status),
			Path:      path,
			Timestamp: time.Now().Format(time.RFC3339),
		},
	}
	return r.JSON(status, response)
}

// ErrorWithDetails sends an error response with additional details
func (r *ResponseWriter) ErrorWithDetails(status int, message string, details []Detail) error {
	response := Response{
		Status: Status{
			Code:      status,
			Message:   message,
			Success:   false,
			ErrorCode: getErrorCode(status),
			Path:      "",
			Timestamp: time.Now().Format(time.RFC3339),
			Details:   details,
		},
	}
	return r.JSON(status, response)
}

// ErrorWithDetailsAndPath sends an error response with additional details and path
func (r *ResponseWriter) ErrorWithDetailsAndPath(status int, message string, details []Detail, path string) error {
	response := Response{
		Status: Status{
			Code:      status,
			Message:   message,
			Success:   false,
			ErrorCode: getErrorCode(status),
			Path:      path,
			Timestamp: time.Now().Format(time.RFC3339),
			Details:   details,
		},
	}
	return r.JSON(status, response)
}

// BadRequest sends a bad request error response
func (r *ResponseWriter) BadRequest(message string) error {
	return r.Error(http.StatusBadRequest, message)
}

// Unauthorized sends an unauthorized error response
func (r *ResponseWriter) Unauthorized(message string) error {
	if message == "" {
		message = "Unauthorized"
	}
	return r.Error(http.StatusUnauthorized, message)
}

// Forbidden sends a forbidden error response
func (r *ResponseWriter) Forbidden(message string) error {
	if message == "" {
		message = "Forbidden"
	}
	return r.Error(http.StatusForbidden, message)
}

// NotFound sends a not found error response
func (r *ResponseWriter) NotFound(message string) error {
	if message == "" {
		message = "Not Found"
	}
	return r.Error(http.StatusNotFound, message)
}

// InternalServerError sends an internal server error response
func (r *ResponseWriter) InternalServerError(message string) error {
	if message == "" {
		message = "Internal Server Error"
	}
	return r.Error(http.StatusInternalServerError, message)
}

// ValidationError sends a validation error response
func (r *ResponseWriter) ValidationError(errors map[string]string) error {
	// Convert map[string]string to []Detail format
	var details []Detail
	for field, message := range errors {
		details = append(details, Detail{
			Field:   field,
			Message: message,
		})
	}

	return r.ErrorWithDetails(
		http.StatusUnprocessableEntity,
		"Validation failed",
		details,
	)
}

// CursorPaginated sends a cursor-based paginated response
func (r *ResponseWriter) CursorPaginated(data interface{}, meta pagination.CursorResponse) error {
	response := ResponseMeta{
		Status: Status{
			Code:      http.StatusOK,
			Message:   "Success",
			Success:   true,
			Path:      "",
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
		Meta: &Meta{
			HasNext:    meta.HasNext,
			NextCursor: meta.NextCursor,
		},
	}
	return r.JSON(http.StatusOK, response)
}

// ============================================================================
// Standalone Helper Functions
// ============================================================================

// Standalone helper functions for direct use with http.ResponseWriter
func Success(w http.ResponseWriter, data interface{}) {
	NewResponse(w).Success(data)
}

func Created(w http.ResponseWriter, data interface{}) {
	NewResponse(w).Created(data)
}

func BadRequest(w http.ResponseWriter, message string, details ...string) {
	r := NewResponse(w)
	if len(details) > 0 {
		detailsArray := []Detail{{Message: details[0]}}
		r.ErrorWithDetails(http.StatusBadRequest, message, detailsArray)
	} else {
		r.BadRequest(message)
	}
}

func Unauthorized(w http.ResponseWriter, message string) {
	NewResponse(w).Unauthorized(message)
}

func Forbidden(w http.ResponseWriter, message string) {
	NewResponse(w).Forbidden(message)
}

func NotFound(w http.ResponseWriter, message string) {
	NewResponse(w).NotFound(message)
}

func InternalServerError(w http.ResponseWriter, message string, errorCode ...string) {
	NewResponse(w).InternalServerError(message)
}

func InternalError(w http.ResponseWriter, message string, details ...string) {
	r := NewResponse(w)
	if len(details) > 0 {
		detailsArray := []Detail{{Message: details[0]}}
		r.ErrorWithDetails(http.StatusInternalServerError, message, detailsArray)
	} else {
		r.InternalServerError(message)
	}
}

func TooManyRequests(w http.ResponseWriter, message string) {
	NewResponse(w).Error(http.StatusTooManyRequests, message)
}

func ServiceUnavailable(w http.ResponseWriter, message string) {
	NewResponse(w).Error(http.StatusServiceUnavailable, message)
}

func NoContent(w http.ResponseWriter) {
	NewResponse(w).NoContent()
}

func OK(w http.ResponseWriter, data interface{}) {
	NewResponse(w).Success(data)
}

func Conflict(w http.ResponseWriter, message string) {
	NewResponse(w).Error(http.StatusConflict, message)
}

func ValidationError(w http.ResponseWriter, errors interface{}) {
	r := NewResponse(w)
	switch v := errors.(type) {
	case map[string]string:
		r.ValidationError(v)
	case error:
		// Convert error to map format
		errorMap := map[string]string{"error": v.Error()}
		r.ValidationError(errorMap)
	default:
		// Fallback
		errorMap := map[string]string{"error": "Validation failed"}
		r.ValidationError(errorMap)
	}
}

// CursorPaginated sends a cursor-based paginated response
func CursorPaginated(w http.ResponseWriter, data interface{}, meta pagination.CursorResponse) {
	NewResponse(w).CursorPaginated(data, meta)
}

// ============================================================================
// Context-Aware Response Functions (with Path extraction)
// ============================================================================

// extractPath safely extracts path from gin.Context
func extractPath(c *gin.Context) string {
	if c == nil || c.Request == nil {
		return ""
	}
	return c.Request.URL.Path
}

// convertToDetails converts various types to []Detail format
func convertToDetails(input interface{}) []Detail {
	switch v := input.(type) {
	case []Detail:
		return v
	case map[string]string:
		var details []Detail
		for field, message := range v {
			details = append(details, Detail{
				Field:   field,
				Message: message,
			})
		}
		return details
	case error:
		return []Detail{{Message: v.Error()}}
	case string:
		return []Detail{{Message: v}}
	case map[string]interface{}:
		var details []Detail
		for key, value := range v {
			details = append(details, Detail{
				Code:    key,
				Message: fmt.Sprintf("%v", value),
			})
		}
		return details
	default:
		return []Detail{{Message: fmt.Sprintf("%v", v)}}
	}
}

// createDetailWithCode creates a Detail with code and message
func createDetailWithCode(code, message string) Detail {
	return Detail{
		Code:    code,
		Message: message,
	}
}

// createDetailWithField creates a Detail with field and message (for validation)
func createDetailWithField(field, message string) Detail {
	return Detail{
		Field:   field,
		Message: message,
	}
}

// SuccessWithContext sends a success response with request path
func SuccessWithContext(c *gin.Context, data interface{}) {
	response := Response{
		Status: Status{
			Code:      http.StatusOK,
			Message:   "Success",
			Success:   true,
			Path:      extractPath(c),
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
	}
	NewResponse(c.Writer).JSON(http.StatusOK, response)
}

// CreatedWithContext sends a created response with request path
func CreatedWithContext(c *gin.Context, data interface{}) {
	response := Response{
		Status: Status{
			Code:      http.StatusCreated,
			Message:   "Created",
			Success:   true,
			Path:      extractPath(c),
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
	}
	NewResponse(c.Writer).JSON(http.StatusCreated, response)
}

// ErrorWithContext sends an error response with request path
func ErrorWithContext(c *gin.Context, status int, message string) {
	response := Response{
		Status: Status{
			Code:      status,
			Message:   message,
			Success:   false,
			ErrorCode: getErrorCode(status),
			Path:      extractPath(c),
			Timestamp: time.Now().Format(time.RFC3339),
		},
	}
	NewResponse(c.Writer).JSON(status, response)
}

// ErrorWithContextAndDetails sends an error response with request path and details
func ErrorWithContextAndDetails(c *gin.Context, status int, message string, details []Detail) {
	response := Response{
		Status: Status{
			Code:      status,
			Message:   message,
			Success:   false,
			ErrorCode: getErrorCode(status),
			Path:      extractPath(c),
			Timestamp: time.Now().Format(time.RFC3339),
			Details:   details,
		},
	}
	NewResponse(c.Writer).JSON(status, response)
}

// BadRequestWithContext sends a bad request error with request path
func BadRequestWithContext(c *gin.Context, message string, details ...interface{}) {
	if len(details) > 0 {
		ErrorWithContextAndDetails(c, http.StatusBadRequest, message, convertToDetails(details[0]))
	} else {
		ErrorWithContext(c, http.StatusBadRequest, message)
	}
}

// UnauthorizedWithContext sends an unauthorized error with request path
func UnauthorizedWithContext(c *gin.Context, message string) {
	if message == "" {
		message = "Unauthorized"
	}
	ErrorWithContext(c, http.StatusUnauthorized, message)
}

// ForbiddenWithContext sends a forbidden error with request path
func ForbiddenWithContext(c *gin.Context, message string) {
	if message == "" {
		message = "Forbidden"
	}
	ErrorWithContext(c, http.StatusForbidden, message)
}

// NotFoundWithContext sends a not found error with request path
func NotFoundWithContext(c *gin.Context, message string) {
	if message == "" {
		message = "Not Found"
	}
	ErrorWithContext(c, http.StatusNotFound, message)
}

// ConflictWithContext sends a conflict error with request path
func ConflictWithContext(c *gin.Context, message string, details ...interface{}) {
	if len(details) > 0 {
		ErrorWithContextAndDetails(c, http.StatusConflict, message, convertToDetails(details[0]))
	} else {
		ErrorWithContext(c, http.StatusConflict, message)
	}
}

// InternalServerErrorWithContext sends an internal server error with request path
func InternalServerErrorWithContext(c *gin.Context, message string, details ...interface{}) {
	if message == "" {
		message = "Internal Server Error"
	}
	if len(details) > 0 {
		ErrorWithContextAndDetails(c, http.StatusInternalServerError, message, convertToDetails(details[0]))
	} else {
		ErrorWithContext(c, http.StatusInternalServerError, message)
	}
}

// ValidationErrorWithContext sends a validation error with request path
func ValidationErrorWithContext(c *gin.Context, errors interface{}) {
	details := convertToDetails(errors)
	ErrorWithContextAndDetails(c, http.StatusUnprocessableEntity, "Validation failed", details)
}

// TooManyRequestsWithContext sends a rate limit error with request path
func TooManyRequestsWithContext(c *gin.Context, message string) {
	ErrorWithContext(c, http.StatusTooManyRequests, message)
}

// ServiceUnavailableWithContext sends a service unavailable error with request path
func ServiceUnavailableWithContext(c *gin.Context, message string) {
	ErrorWithContext(c, http.StatusServiceUnavailable, message)
}

// CursorPaginatedWithContext sends a cursor-based paginated response with request path
func CursorPaginatedWithContext(c *gin.Context, data interface{}, meta pagination.CursorResponse) {
	response := ResponseMeta{
		Status: Status{
			Code:      http.StatusOK,
			Message:   "Success",
			Success:   true,
			Path:      extractPath(c),
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
		Meta: &Meta{
			HasNext:    meta.HasNext,
			NextCursor: meta.NextCursor,
		},
	}
	NewResponse(c.Writer).JSON(http.StatusOK, response)
}

// ============================================================================
// Helper Functions
// ============================================================================

// getErrorCode returns an error code based on HTTP status
func getErrorCode(status int) string {
	switch status {
	case http.StatusBadRequest:
		return "BAD_REQUEST"
	case http.StatusUnauthorized:
		return "UNAUTHORIZED"
	case http.StatusForbidden:
		return "FORBIDDEN"
	case http.StatusNotFound:
		return "NOT_FOUND"
	case http.StatusMethodNotAllowed:
		return "METHOD_NOT_ALLOWED"
	case http.StatusConflict:
		return "CONFLICT"
	case http.StatusUnprocessableEntity:
		return "VALIDATION_ERROR"
	case http.StatusTooManyRequests:
		return "TOO_MANY_REQUESTS"
	case http.StatusInternalServerError:
		return "INTERNAL_ERROR"
	case http.StatusServiceUnavailable:
		return "SERVICE_UNAVAILABLE"
	default:
		return "ERROR"
	}
}
