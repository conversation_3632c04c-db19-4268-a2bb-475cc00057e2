package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

// JWT context keys
const (
	JWTClaimsKey = "jwt_claims"
	UserIDKey    = "user_id"
	TenantIDKey  = "tenant_id"
	WebsiteIDKey = "website_id"
)

// JWTAuthMiddleware creates a JWT authentication middleware
func JWTAuthMiddleware(jwtService services.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			response.Unauthorized(c.Writer, "Missing authorization header")
			c.Abort()
			return
		}

		// Check Bearer scheme
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			response.Unauthorized(c.Writer, "Invalid authorization header format")
			c.Abort()
			return
		}

		tokenString := parts[1]

		// Validate token
		claims, err := jwtService.ValidateAccessToken(tokenString)
		if err != nil {
			var message string
			var statusCode int = http.StatusUnauthorized

			switch err {
			case services.ErrTokenExpired:
				message = "Token expired"
			case services.ErrTokenNotValidYet:
				message = "Token not valid yet"
			case services.ErrTokenBlacklisted:
				message = "Token has been revoked"
			case services.ErrInvalidTokenType:
				message = "Invalid token type"
			case services.ErrMissingTenantID:
				message = "Invalid token: missing tenant information"
			case services.ErrMissingUserID:
				message = "Invalid token: missing user information"
			default:
				message = "Invalid token"
			}

			response.NewResponse(c.Writer).Error(statusCode, message)
			c.Abort()
			return
		}

		// Set claims in context
		c.Set(JWTClaimsKey, claims)
		c.Set(UserIDKey, claims.UserID)
		//c.Set(TenantIDKey, claims.CurrentTenantID)
		//c.Set(WebsiteIDKey, claims.WebsiteID)

		c.Next()
	}
}

// OptionalJWTAuthMiddleware creates an optional JWT authentication middleware
// It validates the token if present but doesn't require it
func OptionalJWTAuthMiddleware(jwtService services.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// No token, continue without authentication
			c.Next()
			return
		}

		// Check Bearer scheme
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			// Invalid format, continue without authentication
			c.Next()
			return
		}

		tokenString := parts[1]

		// Validate token
		claims, err := jwtService.ValidateAccessToken(tokenString)
		if err == nil {
			// Valid token, set claims in context
			c.Set(JWTClaimsKey, claims)
			c.Set(UserIDKey, claims.UserID)
			//c.Set(TenantIDKey, claims.CurrentTenantID)
			//c.Set(WebsiteIDKey, claims.WebsiteID)
		}

		c.Next()
	}
}

// RequireScopes creates a middleware that requires specific JWT scopes
func RequireScopes(scopes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get(JWTClaimsKey)
		if !exists {
			response.Unauthorized(c.Writer, "Authentication required")
			c.Abort()
			return
		}

		jwtClaims, ok := claims.(*models.JWTClaims)
		if !ok {
			response.InternalServerError(c.Writer, "Invalid claims format")
			c.Abort()
			return
		}

		// Check if user has all required scopes
		for _, scope := range scopes {
			if !jwtClaims.HasScopeJWT(scope) {
				details := []response.Detail{{
					Code:    "INSUFFICIENT_SCOPE",
					Message: "Required scope: " + scope,
				}}
				response.NewResponse(c.Writer).ErrorWithDetails(http.StatusForbidden, "Insufficient permissions", details)
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// GetJWTClaims extracts JWT claims from context
func GetJWTClaims(c *gin.Context) (*models.JWTClaims, bool) {
	claims, exists := c.Get(JWTClaimsKey)
	if !exists {
		return nil, false
	}

	jwtClaims, ok := claims.(*models.JWTClaims)
	return jwtClaims, ok
}

// GetUserID extracts user ID from context

// GetUserRole extracts user role from context
func GetUserRole(c *gin.Context) string {
	// First check if role is stored directly in context
	if role, exists := c.Get("user_role"); exists {
		if roleStr, ok := role.(string); ok {
			return roleStr
		}
	}

	// TODO: Implement proper role retrieval from RBAC system
	// For now, return a default role or empty string
	return ""
}

// Simplified helper functions for common use cases

// GetUserID returns user ID from context (returns 0 if not found)
func GetUserID(c *gin.Context) uint {
	userID, ok := GetUserIDWithCheck(c)
	if !ok {
		return 0
	}
	return userID
}

// GetUserIDWithCheck extracts user ID from context with existence check
func GetUserIDWithCheck(c *gin.Context) (uint, bool) {
	userID, exists := c.Get(UserIDKey)
	if !exists {
		return 0, false
	}

	id, ok := userID.(uint)
	return id, ok
}

// GetTenantID returns tenant ID from context (returns 0 if not found)
func GetTenantID(c *gin.Context) uint {
	tenantID, ok := GetTenantIDFromJWT(c)
	if !ok {
		return 0
	}
	return tenantID
}

// GetTenantIDFromJWT extracts tenant ID from JWT context
func GetTenantIDFromJWT(c *gin.Context) (uint, bool) {
	tenantID, exists := c.Get(TenantIDKey)
	if !exists {
		return 0, false
	}

	id, ok := tenantID.(uint)
	return id, ok
}

// GetWebsiteID extracts website ID from context
func GetWebsiteID(c *gin.Context) (uint, bool) {
	websiteID, exists := c.Get(WebsiteIDKey)
	if !exists {
		return 0, false
	}

	id, ok := websiteID.(uint)
	return id, ok
}

// RequireRoleMiddleware creates a middleware that requires specific roles (string-based)
// This is a temporary implementation that accepts string roles for compatibility
func RequireRoleMiddleware(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get(JWTClaimsKey)
		if !exists {
			response.Unauthorized(c.Writer, "Authentication required")
			c.Abort()
			return
		}

		jwtClaims, ok := claims.(*models.JWTClaims)
		if !ok {
			response.InternalServerError(c.Writer, "Invalid claims format")
			c.Abort()
			return
		}

		// TODO: Implement proper role checking via tenant membership lookup
		// For now, allow all authenticated users since roles are no longer in JWT
		// This should be replaced with proper tenant membership role checking
		_ = jwtClaims // Use the variable to avoid unused variable error
		_ = roles     // Use roles to avoid unused variable error

		// TEMPORARY: Allow all authenticated users
		// In a real implementation, you would:
		// 1. Get user's tenant memberships from database
		// 2. Check if any membership has the required role
		// 3. Return 403 if user doesn't have required role

		c.Next()
	}
}
