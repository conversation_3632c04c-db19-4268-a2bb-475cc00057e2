package middleware

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SessionManager handles session-related middleware operations
type SessionManager struct {
	sessionRepo repositories.SessionRepository
	logger      utils.Logger
}

// NewSessionManager creates a new session manager
func NewSessionManager(sessionRepo repositories.SessionRepository, logger utils.Logger) *SessionManager {
	return &SessionManager{
		sessionRepo: sessionRepo,
		logger:      logger,
	}
}

// SessionValidationMiddleware validates session from JWT claims
func (sm *SessionManager) SessionValidationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get JWT claims
		claims, exists := c.Get(JWTClaimsKey)
		if !exists {
			c.Next()
			return
		}

		jwtClaims, ok := claims.(*models.JWTClaims)
		if !ok || jwtClaims.SessionID == nil {
			c.Next()
			return
		}

		// Validate session
		session, err := sm.sessionRepo.GetByID(c.Request.Context(), *jwtClaims.SessionID)
		if err != nil {
			sm.logger.WithError(err).Error("Failed to get session")
			response.InternalServerError(c.Writer, "Session validation failed")
			c.Abort()
			return
		}

		// Check if session exists and is valid
		if session == nil {
			response.Unauthorized(c.Writer, "Session not found")
			c.Abort()
			return
		}

		// Check if session is expired
		if session.IsExpired() {
			response.Unauthorized(c.Writer, "Session expired")
			c.Abort()
			return
		}

		// Check if session is revoked
		if session.IsRevoked {
			response.Unauthorized(c.Writer, "Session revoked")
			c.Abort()
			return
		}

		// Check if session belongs to the user
		if session.UserID != jwtClaims.UserID {
			response.Unauthorized(c.Writer, "Session mismatch")
			c.Abort()
			return
		}

		// Update last used time
		session.UpdateLastUsed()
		if err := sm.sessionRepo.Update(c.Request.Context(), session); err != nil {
			sm.logger.WithError(err).Error("Failed to update session last used time")
			// Don't fail the request, just log the error
		}

		// Store session in context
		c.Set("session", session)
		c.Next()
	}
}

// SessionCleanupMiddleware periodically cleans up expired sessions
func (sm *SessionManager) SessionCleanupMiddleware() gin.HandlerFunc {
	// Start cleanup goroutine
	go sm.periodicCleanup()

	return func(c *gin.Context) {
		c.Next()
	}
}

// periodicCleanup runs periodic cleanup of expired sessions
func (sm *SessionManager) periodicCleanup() {
	ticker := time.NewTicker(30 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		// Clean up expired sessions
		ctx := context.Background()
		deletedCount, err := sm.sessionRepo.CleanupExpiredSessions(ctx)
		if err != nil {
			sm.logger.WithError(err).Error("Failed to cleanup expired sessions")
		} else if deletedCount > 0 {
			sm.logger.WithField("deleted_count", deletedCount).Info("Cleaned up expired sessions")
		}
	}
}

// RequireActiveSessionMiddleware ensures the request has an active session
func RequireActiveSessionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		session, exists := c.Get("session")
		if !exists {
			response.Unauthorized(c.Writer, "Active session required")
			c.Abort()
			return
		}

		sessionObj, ok := session.(*models.Session)
		if !ok || !sessionObj.IsActive() {
			response.Unauthorized(c.Writer, "Invalid or inactive session")
			c.Abort()
			return
		}

		c.Next()
	}
}

// DeviceTrackingMiddleware tracks device information for sessions
func (sm *SessionManager) DeviceTrackingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get session from context
		session, exists := c.Get("session")
		if !exists {
			c.Next()
			return
		}

		sessionObj, ok := session.(*models.Session)
		if !ok {
			c.Next()
			return
		}

		// Check if device info has changed
		currentIP := c.ClientIP()
		currentUserAgent := c.Request.UserAgent()

		// Update device info if changed
		updated := false
		if sessionObj.IPAddress == nil || *sessionObj.IPAddress != currentIP {
			sessionObj.IPAddress = &currentIP
			updated = true
		}

		// For security, we might want to invalidate session if device changes significantly
		// For now, we'll just log it
		if updated {
			// Log potential security concern
			sm.logger.WithFields(map[string]interface{}{
				"session_id": sessionObj.ID,
				"user_id":    sessionObj.UserID,
				"old_ip":     sessionObj.IPAddress,
				"new_ip":     currentIP,
				"user_agent": currentUserAgent,
			}).Warn("Session IP address changed")
		}

		c.Next()
	}
}

// SessionExtensionMiddleware automatically extends session expiration on activity
func (sm *SessionManager) SessionExtensionMiddleware(extensionDuration time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get session from context
		session, exists := c.Get("session")
		if !exists {
			c.Next()
			return
		}

		sessionObj, ok := session.(*models.Session)
		if !ok {
			c.Next()
			return
		}

		// Check if session needs extension
		timeUntilExpiry := time.Until(sessionObj.ExpiresAt)
		if timeUntilExpiry < extensionDuration/2 {
			// Extend session
			newExpiry := time.Now().Add(extensionDuration)
			err := sm.sessionRepo.ExtendExpiration(c.Request.Context(), sessionObj.ID, newExpiry)
			if err != nil {
				sm.logger.WithError(err).Error("Failed to extend session")
			} else {
				sessionObj.ExpiresAt = newExpiry
				sm.logger.WithFields(map[string]interface{}{
					"session_id": sessionObj.ID,
					"user_id":    sessionObj.UserID,
					"new_expiry": newExpiry,
				}).Info("Extended session expiration")
			}
		}

		c.Next()
	}
}

// ConcurrentSessionLimitMiddleware limits concurrent sessions per user
func (sm *SessionManager) ConcurrentSessionLimitMiddleware(maxConcurrentSessions int) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from JWT claims
		claims, exists := c.Get(JWTClaimsKey)
		if !exists {
			c.Next()
			return
		}

		jwtClaims, ok := claims.(*models.JWTClaims)
		if !ok {
			c.Next()
			return
		}

		// Get active sessions for user
		sessions, err := sm.sessionRepo.GetActiveSessionsByUser(c.Request.Context(), jwtClaims.UserID)
		if err != nil {
			sm.logger.WithError(err).Error("Failed to get user sessions")
			c.Next()
			return
		}

		// Check if limit exceeded
		if len(sessions) > maxConcurrentSessions {
			// Revoke oldest sessions
			for i := 0; i < len(sessions)-maxConcurrentSessions; i++ {
				err := sm.sessionRepo.InvalidateSession(c.Request.Context(), sessions[i].ID)
				if err != nil {
					sm.logger.WithError(err).Error("Failed to invalidate old session")
				}
			}

			sm.logger.WithFields(map[string]interface{}{
				"user_id":          jwtClaims.UserID,
				"active_sessions":  len(sessions),
				"max_sessions":     maxConcurrentSessions,
				"revoked_sessions": len(sessions) - maxConcurrentSessions,
			}).Info("Revoked old sessions due to concurrent session limit")
		}

		c.Next()
	}
}

// GetSessionFromContext retrieves session from Gin context
func GetSessionFromContext(c *gin.Context) (*models.Session, bool) {
	session, exists := c.Get("session")
	if !exists {
		return nil, false
	}

	sessionObj, ok := session.(*models.Session)
	return sessionObj, ok
}

// GetSessionInfo returns session information for responses
func GetSessionInfo(c *gin.Context) map[string]interface{} {
	session, exists := GetSessionFromContext(c)
	if !exists {
		return nil
	}

	return map[string]interface{}{
		"id":           session.ID,
		"device_type":  session.DeviceType,
		"device_name":  session.DeviceName,
		"ip_address":   session.IPAddress,
		"last_used_at": session.LastUsedAt,
		"expires_at":   session.ExpiresAt,
		"is_current":   true, // This is the current session
	}
}
