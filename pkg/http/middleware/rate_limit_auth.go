package middleware

import (
	"crypto/md5"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"golang.org/x/time/rate"
)

// AuthEndpointLimiter manages rate limiting for different auth endpoints
type AuthEndpointLimiter struct {
	limiters map[string]*endpointLimiter
	mu       sync.RWMutex
	logger   utils.Logger
}

// endpointLimiter handles rate limiting for a specific endpoint
type endpointLimiter struct {
	clients map[string]*clientLimiter
	mu      sync.RWMutex
	config  EndpointLimitConfig
}

// clientLimiter tracks rate limiting for a specific client
type clientLimiter struct {
	limiter  *rate.Limiter
	lastSeen time.Time
}

// EndpointLimitConfig defines rate limiting configuration for an endpoint
type EndpointLimitConfig struct {
	MaxRequests    int           // Maximum requests per window
	WindowDuration time.Duration // Time window duration
	BurstSize      int           // Burst size (optional)
}

// DefaultAuthLimitConfigs provides default rate limiting configurations
var DefaultAuthLimitConfigs = map[string]EndpointLimitConfig{
	"/auth/login": {
		MaxRequests:    5, // 5 login attempts
		WindowDuration: 15 * time.Minute,
		BurstSize:      2, // Allow 2 burst requests
	},
	"/auth/register": {
		MaxRequests:    3, // 3 registration attempts
		WindowDuration: 60 * time.Minute,
		BurstSize:      1,
	},
	"/auth/forgot-password": {
		MaxRequests:    3, // 3 password reset requests
		WindowDuration: 60 * time.Minute,
		BurstSize:      1,
	},
	"/auth/reset-password": {
		MaxRequests:    5, // 5 password reset attempts
		WindowDuration: 30 * time.Minute,
		BurstSize:      1,
	},
	"/auth/verify-email": {
		MaxRequests:    10, // 10 email verification attempts
		WindowDuration: 60 * time.Minute,
		BurstSize:      3,
	},
	"/auth/refresh": {
		MaxRequests:    30, // 30 token refresh attempts
		WindowDuration: 60 * time.Minute,
		BurstSize:      10,
	},
	"/auth/logout": {
		MaxRequests:    20, // 20 logout attempts
		WindowDuration: 60 * time.Minute,
		BurstSize:      5,
	},
	"/auth/2fa/enable": {
		MaxRequests:    5, // 5 2FA enable attempts
		WindowDuration: 60 * time.Minute,
		BurstSize:      1,
	},
	"/auth/2fa/verify": {
		MaxRequests:    10, // 10 2FA verification attempts
		WindowDuration: 15 * time.Minute,
		BurstSize:      3,
	},
}

// NewAuthEndpointLimiter creates a new auth endpoint limiter
func NewAuthEndpointLimiter(configs map[string]EndpointLimitConfig, logger utils.Logger) *AuthEndpointLimiter {
	limiter := &AuthEndpointLimiter{
		limiters: make(map[string]*endpointLimiter),
		logger:   logger,
	}

	// Initialize limiters for each endpoint
	for endpoint, config := range configs {
		limiter.limiters[endpoint] = &endpointLimiter{
			clients: make(map[string]*clientLimiter),
			config:  config,
		}
	}

	// Start cleanup goroutine
	go limiter.cleanupExpiredClients()

	return limiter
}

// cleanupExpiredClients removes expired client limiters
func (a *AuthEndpointLimiter) cleanupExpiredClients() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		a.mu.RLock()
		for _, limiter := range a.limiters {
			limiter.mu.Lock()
			for clientID, client := range limiter.clients {
				if time.Since(client.lastSeen) > limiter.config.WindowDuration*2 {
					delete(limiter.clients, clientID)
				}
			}
			limiter.mu.Unlock()
		}
		a.mu.RUnlock()
	}
}

// checkLimit checks if a client is within rate limits for an endpoint
func (a *AuthEndpointLimiter) checkLimit(endpoint, clientID string) (bool, time.Duration) {
	a.mu.RLock()
	limiter, exists := a.limiters[endpoint]
	a.mu.RUnlock()

	if !exists {
		// No rate limit configured for this endpoint
		return true, 0
	}

	limiter.mu.Lock()
	client, exists := limiter.clients[clientID]
	if !exists {
		client = &clientLimiter{
			limiter:  rate.NewLimiter(rate.Every(limiter.config.WindowDuration/time.Duration(limiter.config.MaxRequests)), limiter.config.BurstSize),
			lastSeen: time.Now(),
		}
		limiter.clients[clientID] = client
	}
	client.lastSeen = time.Now()
	limiter.mu.Unlock()

	if client.limiter.Allow() {
		return true, 0
	}

	// Calculate how long until next request is allowed
	reservation := client.limiter.Reserve()
	if reservation.OK() {
		delay := reservation.Delay()
		reservation.Cancel()
		return false, delay
	}

	return false, limiter.config.WindowDuration
}

// AuthRateLimitingMiddleware creates a rate limiting middleware for auth endpoints
func AuthRateLimitingMiddleware(logger utils.Logger) gin.HandlerFunc {
	limiter := NewAuthEndpointLimiter(DefaultAuthLimitConfigs, logger)

	return func(c *gin.Context) {
		endpoint := c.Request.URL.Path
		clientID := getClientIdentifier(c)

		allowed, retryAfter := limiter.checkLimit(endpoint, clientID)
		if !allowed {
			// Add rate limit headers
			c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", DefaultAuthLimitConfigs[endpoint].MaxRequests))
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", time.Now().Add(retryAfter).Unix()))
			c.Header("Retry-After", fmt.Sprintf("%.0f", retryAfter.Seconds()))

			// Log rate limit exceeded
			logger.WithFields(map[string]interface{}{
				"endpoint":    endpoint,
				"client_id":   clientID,
				"client_ip":   c.ClientIP(),
				"user_agent":  c.Request.UserAgent(),
				"retry_after": retryAfter.String(),
			}).Warn("Rate limit exceeded for auth endpoint")

			details := []response.Detail{{
				Code:    "RATE_LIMIT_EXCEEDED",
				Message: fmt.Sprintf("Retry after %.0f seconds", retryAfter.Seconds()),
			}}
			response.NewResponse(c.Writer).ErrorWithDetails(http.StatusTooManyRequests, "Rate limit exceeded. Please try again later.", details)
			c.Abort()
			return
		}

		c.Next()
	}
}

// getClientIdentifier creates a unique identifier for rate limiting
func getClientIdentifier(c *gin.Context) string {
	// Use IP address as primary identifier
	clientIP := c.ClientIP()

	// For authenticated requests, also consider user ID
	if userID, exists := c.Get(UserIDKey); exists {
		if uid, ok := userID.(uint); ok {
			return fmt.Sprintf("%s:user:%d", clientIP, uid)
		}
	}

	// For unauthenticated requests, use IP + User-Agent hash
	userAgent := c.Request.UserAgent()
	if userAgent != "" {
		return fmt.Sprintf("%s:ua:%x", clientIP, md5.Sum([]byte(userAgent)))
	}

	return clientIP
}

// IPBasedRateLimitMiddleware implements simple IP-based rate limiting
func IPBasedRateLimitMiddleware(requestsPerSecond float64, burstSize int) gin.HandlerFunc {
	var (
		mu      sync.RWMutex
		clients = make(map[string]*clientLimiter)
	)

	// Cleanup expired clients
	go func() {
		ticker := time.NewTicker(time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			mu.Lock()
			for ip, client := range clients {
				if time.Since(client.lastSeen) > time.Hour {
					delete(clients, ip)
				}
			}
			mu.Unlock()
		}
	}()

	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		mu.Lock()
		client, exists := clients[clientIP]
		if !exists {
			client = &clientLimiter{
				limiter:  rate.NewLimiter(rate.Limit(requestsPerSecond), burstSize),
				lastSeen: time.Now(),
			}
			clients[clientIP] = client
		}
		client.lastSeen = time.Now()
		mu.Unlock()

		if !client.limiter.Allow() {
			response.TooManyRequests(c.Writer, "Rate limit exceeded")
			c.Abort()
			return
		}

		c.Next()
	}
}

// AdaptiveRateLimitMiddleware implements adaptive rate limiting based on response times
func AdaptiveRateLimitMiddleware(baseRate float64, maxRate float64, targetLatency time.Duration) gin.HandlerFunc {
	var (
		mu           sync.RWMutex
		clients      = make(map[string]*clientLimiter)
		currentRate  = baseRate
		avgLatency   time.Duration
		requestCount int64
	)

	// Adjust rate based on average latency
	go func() {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()
		for range ticker.C {
			mu.Lock()
			if requestCount > 0 {
				if avgLatency > targetLatency {
					// Decrease rate if latency is too high
					currentRate = currentRate * 0.9
					if currentRate < baseRate/4 {
						currentRate = baseRate / 4
					}
				} else {
					// Increase rate if latency is acceptable
					currentRate = currentRate * 1.1
					if currentRate > maxRate {
						currentRate = maxRate
					}
				}
			}
			requestCount = 0
			avgLatency = 0
			mu.Unlock()
		}
	}()

	return func(c *gin.Context) {
		start := time.Now()
		clientIP := c.ClientIP()

		mu.Lock()
		client, exists := clients[clientIP]
		if !exists {
			client = &clientLimiter{
				limiter:  rate.NewLimiter(rate.Limit(currentRate), int(currentRate*2)),
				lastSeen: time.Now(),
			}
			clients[clientIP] = client
		}
		client.lastSeen = time.Now()
		mu.Unlock()

		if !client.limiter.Allow() {
			response.TooManyRequests(c.Writer, "Rate limit exceeded")
			c.Abort()
			return
		}

		c.Next()

		// Update latency metrics
		duration := time.Since(start)
		mu.Lock()
		if requestCount == 0 {
			avgLatency = duration
		} else {
			avgLatency = (avgLatency*time.Duration(requestCount) + duration) / time.Duration(requestCount+1)
		}
		requestCount++
		mu.Unlock()
	}
}
