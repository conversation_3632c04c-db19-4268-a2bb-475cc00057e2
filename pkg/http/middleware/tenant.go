package middleware

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"gorm.io/gorm"
)

const (
	TenantContextKey   contextKey = "tenant"
	TenantIDHeader     string     = "X-Tenant-ID"
	TenantDomainHeader string     = "X-Tenant-Domain"
)

// GetUserFromContext extracts user information from JWT claims
func GetUserFromContext(c *gin.Context) (*authModels.JWTClaims, bool) {
	claims, exists := c.Get(JWTClaimsKey)
	if !exists {
		return nil, false
	}

	jwtClaims, ok := claims.(*authModels.JWTClaims)
	if !ok {
		return nil, false
	}

	return jwtClaims, true
}

// CheckUserTenantAccess checks if a user has access to a specific tenant via cached service
func CheckUserTenantAccess(ctx context.Context, userID, tenantID uint, membershipService interface{}) bool {
	// Type assert to cached service interface
	if service, ok := membershipService.(interface {
		IsUserActiveInTenant(ctx context.Context, userID, tenantID uint) (bool, error)
	}); ok {
		isActive, err := service.IsUserActiveInTenant(ctx, userID, tenantID)
		if err != nil {
			// Log error but don't block - this is a security check
			return false
		}
		return isActive
	}

	// Fallback - if interface doesn't match, deny access
	return false
}

// ValidateResourceBelongsToTenant validates that a resource belongs to the specified tenant
func ValidateResourceBelongsToTenant(ctx context.Context, resourceType, resourceID string, tenantID uint) error {
	// This is a placeholder for resource validation
	// In a real implementation, this would query the specific resource service
	// to verify that the resource belongs to the tenant

	// For now, we'll implement basic validation patterns
	switch resourceType {
	case "user", "users":
		// Users should be validated against tenant membership
		return validateUserResourceAccess(ctx, resourceID, tenantID)
	case "post", "posts", "blog_posts":
		// Blog posts should be validated against tenant ownership
		return validateBlogResourceAccess(ctx, resourceID, tenantID)
	case "setting", "settings":
		// Settings should be validated against tenant ownership
		return validateSettingsResourceAccess(ctx, resourceID, tenantID)
	default:
		// For unknown resource types, we'll allow but log
		// In production, this should be more restrictive
		return nil
	}
}

// Helper functions for resource validation
func validateUserResourceAccess(ctx context.Context, resourceID string, tenantID uint) error {
	// In a real implementation, this would check if the user belongs to the tenant
	// For now, we'll just validate that the resource ID is valid
	if resourceID == "" {
		return errors.New("invalid user resource ID")
	}
	return nil
}

func validateBlogResourceAccess(ctx context.Context, resourceID string, tenantID uint) error {
	// In a real implementation, this would check if the blog post belongs to the tenant
	if resourceID == "" {
		return errors.New("invalid blog resource ID")
	}
	return nil
}

func validateSettingsResourceAccess(ctx context.Context, resourceID string, tenantID uint) error {
	// Settings are tenant-scoped by default
	if resourceID == "" {
		return errors.New("invalid settings resource ID")
	}
	return nil
}

// TenantIsolationMiddleware ensures requests are scoped to a specific tenant
func TenantIsolationMiddleware(tenantService services.TenantService) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Try to get tenant from URL parameter first
		tenantIDStr := c.Param("tenantId")
		if tenantIDStr == "" {
			tenantIDStr = c.Param("tenant_id")
		}

		var tenantID uint
		var err error

		// If not in URL, check headers
		if tenantIDStr == "" {
			// Check X-Tenant-ID header
			if headerID := c.GetHeader(TenantIDHeader); headerID != "" {
				id, err := strconv.ParseUint(headerID, 10, 32)
				if err != nil {
					response.BadRequest(c.Writer, "Invalid tenant ID in header", err.Error())
					c.Abort()
					return
				}
				tenantID = uint(id)
			} else if domain := c.GetHeader(TenantDomainHeader); domain != "" {
				// Check X-Tenant-Domain header
				tenant, err := tenantService.GetByDomain(c.Request.Context(), domain)
				if err != nil {
					if errors.Is(err, gorm.ErrRecordNotFound) {
						response.NotFound(c.Writer, "Tenant not found")
						c.Abort()
						return
					}
					response.InternalError(c.Writer, "Failed to retrieve tenant", err.Error())
					c.Abort()
					return
				}
				tenantID = tenant.ID
			} else {
				// Try to extract from subdomain
				tenantID = extractTenantFromSubdomain(c.Request, tenantService)
				if tenantID == 0 {
					response.BadRequest(c.Writer, "Missing tenant identification", "")
					c.Abort()
					return
				}
			}
		} else {
			// Parse tenant ID from URL
			id, err := strconv.ParseUint(tenantIDStr, 10, 32)
			if err != nil {
				response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
				c.Abort()
				return
			}
			tenantID = uint(id)
		}

		// Verify tenant exists and is active
		tenant, err := tenantService.GetByID(c.Request.Context(), tenantID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				response.NotFound(c.Writer, "Tenant not found")
				c.Abort()
				return
			}
			response.InternalError(c.Writer, "Failed to retrieve tenant", err.Error())
			c.Abort()
			return
		}

		// Check if tenant is active
		if !tenant.IsActive() {
			response.Forbidden(c.Writer, "Tenant is not active")
			c.Abort()
			return
		}

		// Add tenant to context
		c.Set(string(TenantContextKey), tenant)
		c.Set("tenant_id", tenant.ID)

		// Also add to request context for non-Gin code
		ctx := context.WithValue(c.Request.Context(), TenantContextKey, tenant)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	})
}

// OptionalTenantMiddleware adds tenant to context if available but doesn't require it
func OptionalTenantMiddleware(tenantService services.TenantService) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Try to get tenant from various sources
		tenantIDStr := c.Param("tenantId")
		if tenantIDStr == "" {
			tenantIDStr = c.Param("tenant_id")
		}

		var tenantID uint

		if tenantIDStr != "" {
			// Parse tenant ID from URL
			if id, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
				tenantID = uint(id)
			}
		} else if headerID := c.GetHeader(TenantIDHeader); headerID != "" {
			// Check X-Tenant-ID header
			if id, err := strconv.ParseUint(headerID, 10, 32); err == nil {
				tenantID = uint(id)
			}
		} else if domain := c.GetHeader(TenantDomainHeader); domain != "" {
			// Check X-Tenant-Domain header
			if tenant, err := tenantService.GetByDomain(c.Request.Context(), domain); err == nil {
				tenantID = tenant.ID
			}
		} else {
			// Try to extract from subdomain
			tenantID = extractTenantFromSubdomain(c.Request, tenantService)
		}

		// If we found a tenant ID, add it to context
		if tenantID > 0 {
			if tenant, err := tenantService.GetByID(c.Request.Context(), tenantID); err == nil && tenant.IsActive() {
				c.Set(string(TenantContextKey), tenant)
				c.Set("tenant_id", tenant.ID)
				// Also add to request context for non-Gin code
				ctx := context.WithValue(c.Request.Context(), TenantContextKey, tenant)
				c.Request = c.Request.WithContext(ctx)
			}
		}

		c.Next()
	})
}

// extractTenantFromSubdomain attempts to extract tenant from subdomain
func extractTenantFromSubdomain(r *http.Request, tenantService services.TenantService) uint {
	host := r.Host

	// Remove port if present
	if idx := strings.Index(host, ":"); idx != -1 {
		host = host[:idx]
	}

	// Split by dots
	parts := strings.Split(host, ".")

	// Need at least subdomain.domain.tld
	if len(parts) < 3 {
		return 0
	}

	// First part is the subdomain
	subdomain := parts[0]

	// Skip common non-tenant subdomains
	if subdomain == "www" || subdomain == "api" || subdomain == "admin" {
		return 0
	}

	// Try to find tenant by domain
	tenant, err := tenantService.GetByDomain(r.Context(), subdomain)
	if err != nil {
		return 0
	}

	return tenant.ID
}

// GetTenantFromContext retrieves the tenant from the request context
func GetTenantFromContext(ctx context.Context) (*models.Tenant, bool) {
	tenant, ok := ctx.Value(TenantContextKey).(*models.Tenant)
	return tenant, ok
}

// GetTenantFromGinContext retrieves the tenant from the gin context
func GetTenantFromGinContext(c *gin.Context) (*models.Tenant, bool) {
	value, exists := c.Get(string(TenantContextKey))
	if !exists {
		return nil, false
	}
	tenant, ok := value.(*models.Tenant)
	return tenant, ok
}

// ValidateTenantAccess validates tenant ownership/access for a user (when auth is implemented)
func ValidateTenantAccess(c *gin.Context, userID uint, requiredPermission string) error {
	tenant, ok := GetTenantFromGinContext(c)
	if !ok {
		return errors.New("no tenant context")
	}

	// TODO: Implement when user/auth module is ready
	// For now, just check if tenant exists and is active
	if tenant.ID == 0 || !tenant.IsActive() {
		return errors.New("invalid or inactive tenant")
	}

	// TODO: Check user permissions for this tenant
	// hasAccess := CheckUserTenantPermission(userID, tenant.ID, requiredPermission)
	// if !hasAccess {
	//     return errors.New("insufficient permissions")
	// }

	return nil
}

// PreventCrossTenantAccess middleware prevents cross-tenant data access
func PreventCrossTenantAccess() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		tenant, ok := GetTenantFromGinContext(c)
		if !ok {
			response.Forbidden(c.Writer, "No tenant context")
			c.Abort()
			return
		}

		// Add tenant ID to all database queries through context
		c.Set("tenant_id_filter", tenant.ID)
		c.Next()
	})
}

// TenantResourceOwnershipMiddleware validates resource ownership within tenant
func TenantResourceOwnershipMiddleware(resourceType string) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		tenant, ok := GetTenantFromGinContext(c)
		if !ok {
			response.Forbidden(c.Writer, "No tenant context")
			c.Abort()
			return
		}

		// Extract resource ID from URL params
		resourceID := c.Param("id")
		if resourceID == "" {
			resourceID = c.Param(resourceType + "_id")
		}

		if resourceID == "" {
			response.BadRequest(c.Writer, "Resource ID required", "")
			c.Abort()
			return
		}

		// Validate resource belongs to tenant
		if err := ValidateResourceBelongsToTenant(c.Request.Context(), resourceType, resourceID, tenant.ID); err != nil {
			response.Forbidden(c.Writer, fmt.Sprintf("Resource access denied: %s", err.Error()))
			c.Abort()
			return
		}

		// Set context for downstream handlers
		c.Set("resource_type", resourceType)
		c.Set("resource_id", resourceID)
		c.Set("tenant_scope", tenant.ID)

		c.Next()
	})
}

// MultiTenantDatabaseScope adds tenant scoping to database queries
func MultiTenantDatabaseScope() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		tenant, ok := GetTenantFromGinContext(c)
		if !ok {
			// For routes that don't require tenant, continue without scoping
			c.Next()
			return
		}

		// Add tenant scoping context for database operations
		scopeCtx := context.WithValue(c.Request.Context(), "tenant_scope", tenant.ID)
		c.Request = c.Request.WithContext(scopeCtx)

		c.Next()
	})
}

// GetTenantScopeFromContext retrieves tenant scope from context for database queries
func GetTenantScopeFromContext(ctx context.Context) (uint, bool) {
	if tenantID, ok := ctx.Value("tenant_scope").(uint); ok {
		return tenantID, true
	}
	return 0, false
}

// RequireTenantAccess creates a middleware that checks if the user has access to the tenant
func RequireTenantAccess(permission string) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Get tenant from context
		tenant, ok := GetTenantFromGinContext(c)
		if !ok {
			response.Forbidden(c.Writer, "No tenant context")
			return
		}
		_ = tenant // Use the variable to avoid unused variable error

		// Get user from context (authenticate user from JWT)
		user, ok := GetUserFromContext(c)
		if !ok {
			response.Unauthorized(c.Writer, "Authentication required")
			c.Abort()
			return
		}

		// TODO: Implement proper tenant access checking with membership repository
		// For now, allow all authenticated users access to any tenant they specify
		// This should be replaced with proper tenant membership validation
		_ = user // Use the variable to avoid unused variable error

		// In the new architecture, tenant access should be validated via:
		// hasAccess := CheckUserTenantAccess(c.Request.Context(), user.UserID, tenant.ID, membershipRepo)
		// if !hasAccess {
		//     response.Forbidden(c.Writer, "Access denied: Insufficient permissions for this tenant")
		//     c.Abort()
		//     return
		// }

		// Store user context for downstream handlers
		c.Set("user", user)
		c.Set("user_id", user.UserID)

		c.Next()
	})
}

// TenantResourceLimitMiddleware checks if tenant has reached resource limits
func TenantResourceLimitMiddleware(resourceType string, tenantService services.TenantService) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Get tenant from context
		tenant, ok := GetTenantFromGinContext(c)
		if !ok {
			response.Forbidden(c.Writer, "No tenant context")
			return
		}

		// Check resource limits based on type
		canProceed, err := tenantService.CheckResourceLimit(c.Request.Context(), tenant.ID, resourceType)
		if err != nil {
			response.InternalError(c.Writer, "Failed to check resource limits", err.Error())
			return
		}

		if !canProceed {
			response.Forbidden(c.Writer, "Resource limit exceeded")
			return
		}

		c.Next()
	})
}
