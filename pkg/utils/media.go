package utils

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// Media utilities for processing images and videos

// MediaType represents the type of media
type MediaType string

const (
	MediaTypeImage    MediaType = "image"
	MediaTypeVideo    MediaType = "video"
	MediaTypeAudio    MediaType = "audio"
	MediaTypeDocument MediaType = "document"
	MediaTypeUnknown  MediaType = "unknown"
)

// ImageFormat represents supported image formats
type ImageFormat string

const (
	ImageFormatJPEG ImageFormat = "jpeg"
	ImageFormatPNG  ImageFormat = "png"
	ImageFormatWebP ImageFormat = "webp"
	ImageFormatGIF  ImageFormat = "gif"
	ImageFormatSVG  ImageFormat = "svg"
	ImageFormatBMP  ImageFormat = "bmp"
	ImageFormatTIFF ImageFormat = "tiff"
	ImageFormatAVIF ImageFormat = "avif"
)

// VideoFormat represents supported video formats
type VideoFormat string

const (
	VideoFormatMP4  VideoFormat = "mp4"
	VideoFormatWebM VideoFormat = "webm"
	VideoFormatOGG  VideoFormat = "ogg"
	VideoFormatAVI  VideoFormat = "avi"
	VideoFormatMOV  VideoFormat = "mov"
	VideoFormatWMV  VideoFormat = "wmv"
	VideoFormatFLV  VideoFormat = "flv"
	VideoFormatMKV  VideoFormat = "mkv"
)

// MediaInfo represents media file information
type MediaInfo struct {
	Filename    string    `json:"filename"`
	Extension   string    `json:"extension"`
	Type        MediaType `json:"type"`
	MimeType    string    `json:"mime_type"`
	Size        int64     `json:"size"`
	Width       int       `json:"width,omitempty"`
	Height      int       `json:"height,omitempty"`
	Duration    int       `json:"duration,omitempty"` // in seconds
	AspectRatio string    `json:"aspect_ratio,omitempty"`
	IsOptimized bool      `json:"is_optimized"`
}

// ImageVariant represents different sizes of an image
type ImageVariant struct {
	Name     string `json:"name"`
	Filename string `json:"filename"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	Size     int64  `json:"size"`
	Quality  int    `json:"quality,omitempty"`
	Format   string `json:"format"`
	IsWebP   bool   `json:"is_webp"`
	IsRetina bool   `json:"is_retina"`
}

// Common image sizes for responsive design
var StandardImageSizes = map[string]struct{ Width, Height int }{
	"thumbnail": {150, 150},
	"small":     {300, 300},
	"medium":    {600, 600},
	"large":     {1200, 1200},
	"xlarge":    {1920, 1920},
	"avatar":    {200, 200},
	"cover":     {1200, 630},  // For social media covers
	"hero":      {1920, 1080}, // For hero images
}

// GetMediaType determines the media type from filename
func GetMediaType(filename string) MediaType {
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg", ".bmp", ".tiff", ".avif":
		return MediaTypeImage
	case ".mp4", ".webm", ".avi", ".mov", ".wmv", ".flv", ".mkv":
		return MediaTypeVideo
	case ".mp3", ".wav", ".ogg", ".aac", ".flac", ".m4a":
		return MediaTypeAudio
	case ".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt":
		return MediaTypeDocument
	default:
		return MediaTypeUnknown
	}
}

// GetMimeType returns the MIME type based on file extension
func GetMimeType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))

	mimeTypes := map[string]string{
		// Images
		".jpg":  "image/jpeg",
		".jpeg": "image/jpeg",
		".png":  "image/png",
		".gif":  "image/gif",
		".webp": "image/webp",
		".svg":  "image/svg+xml",
		".bmp":  "image/bmp",
		".tiff": "image/tiff",
		".avif": "image/avif",

		// Videos
		".mp4":  "video/mp4",
		".webm": "video/webm",
		".ogg":  "video/ogg",
		".avi":  "video/x-msvideo",
		".mov":  "video/quicktime",
		".wmv":  "video/x-ms-wmv",
		".flv":  "video/x-flv",
		".mkv":  "video/x-matroska",

		// Audio
		".mp3":  "audio/mpeg",
		".wav":  "audio/wav",
		".aac":  "audio/aac",
		".flac": "audio/flac",
		".m4a":  "audio/mp4",

		// Documents
		".pdf":  "application/pdf",
		".doc":  "application/msword",
		".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		".txt":  "text/plain",
		".rtf":  "application/rtf",
		".odt":  "application/vnd.oasis.opendocument.text",
	}

	if mimeType, exists := mimeTypes[ext]; exists {
		return mimeType
	}

	return "application/octet-stream"
}

// IsImageFile checks if a file is an image
func IsImageFile(filename string) bool {
	return GetMediaType(filename) == MediaTypeImage
}

// IsVideoFile checks if a file is a video
func IsVideoFile(filename string) bool {
	return GetMediaType(filename) == MediaTypeVideo
}

// IsAudioFile checks if a file is an audio file
func IsAudioFile(filename string) bool {
	return GetMediaType(filename) == MediaTypeAudio
}

// IsDocumentFile checks if a file is a document
func IsDocumentFile(filename string) bool {
	return GetMediaType(filename) == MediaTypeDocument
}

// GetImageFormat returns the image format from filename
func GetImageFormat(filename string) ImageFormat {
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".jpg", ".jpeg":
		return ImageFormatJPEG
	case ".png":
		return ImageFormatPNG
	case ".webp":
		return ImageFormatWebP
	case ".gif":
		return ImageFormatGIF
	case ".svg":
		return ImageFormatSVG
	case ".bmp":
		return ImageFormatBMP
	case ".tiff":
		return ImageFormatTIFF
	case ".avif":
		return ImageFormatAVIF
	default:
		return ImageFormatJPEG // Default fallback
	}
}

// GetVideoFormat returns the video format from filename
func GetVideoFormat(filename string) VideoFormat {
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".mp4":
		return VideoFormatMP4
	case ".webm":
		return VideoFormatWebM
	case ".ogg":
		return VideoFormatOGG
	case ".avi":
		return VideoFormatAVI
	case ".mov":
		return VideoFormatMOV
	case ".wmv":
		return VideoFormatWMV
	case ".flv":
		return VideoFormatFLV
	case ".mkv":
		return VideoFormatMKV
	default:
		return VideoFormatMP4 // Default fallback
	}
}

// GenerateImageVariantFilename generates filename for image variant
func GenerateImageVariantFilename(originalFilename, variant string, width, height int) string {
	ext := filepath.Ext(originalFilename)
	base := strings.TrimSuffix(originalFilename, ext)

	if variant != "" {
		return fmt.Sprintf("%s_%s%s", base, variant, ext)
	}

	return fmt.Sprintf("%s_%dx%d%s", base, width, height, ext)
}

// GenerateWebPFilename generates WebP filename from original
func GenerateWebPFilename(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	base := strings.TrimSuffix(originalFilename, ext)
	return base + ".webp"
}

// GenerateAVIFFilename generates AVIF filename from original
func GenerateAVIFFilename(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	base := strings.TrimSuffix(originalFilename, ext)
	return base + ".avif"
}

// CalculateAspectRatio calculates aspect ratio from width and height
func CalculateAspectRatio(width, height int) string {
	if width == 0 || height == 0 {
		return "unknown"
	}

	// Find GCD to simplify ratio
	gcd := func(a, b int) int {
		for b != 0 {
			a, b = b, a%b
		}
		return a
	}

	divisor := gcd(width, height)
	ratioW := width / divisor
	ratioH := height / divisor

	// Common aspect ratios
	switch {
	case ratioW == 1 && ratioH == 1:
		return "1:1"
	case ratioW == 4 && ratioH == 3:
		return "4:3"
	case ratioW == 3 && ratioH == 4:
		return "3:4"
	case ratioW == 16 && ratioH == 9:
		return "16:9"
	case ratioW == 9 && ratioH == 16:
		return "9:16"
	case ratioW == 3 && ratioH == 2:
		return "3:2"
	case ratioW == 2 && ratioH == 3:
		return "2:3"
	case ratioW == 21 && ratioH == 9:
		return "21:9"
	default:
		return fmt.Sprintf("%d:%d", ratioW, ratioH)
	}
}

// IsValidImageSize checks if image dimensions are valid
func IsValidImageSize(width, height int) bool {
	// Minimum size: 1x1 pixel
	// Maximum size: 10000x10000 pixels (adjustable)
	return width >= 1 && height >= 1 && width <= 10000 && height <= 10000
}

// IsValidImageFormat checks if image format is supported
func IsValidImageFormat(format string) bool {
	supportedFormats := []string{"jpeg", "jpg", "png", "webp", "gif", "svg", "bmp", "tiff", "avif"}
	format = strings.ToLower(format)

	for _, supported := range supportedFormats {
		if format == supported {
			return true
		}
	}

	return false
}

// SanitizeFilename sanitizes filename for safe storage
func SanitizeFilename(filename string) string {
	// Separate name and extension
	ext := filepath.Ext(filename)
	name := strings.TrimSuffix(filename, ext)

	// Remove path separators
	name = strings.ReplaceAll(name, "/", "_")
	name = strings.ReplaceAll(name, "\\", "_")

	// Remove or replace special characters
	reg := regexp.MustCompile(`[<>:"|?*]`)
	name = reg.ReplaceAllString(name, "_")

	// Replace spaces with underscores
	name = strings.ReplaceAll(name, " ", "_")

	// Remove multiple underscores
	reg = regexp.MustCompile(`_+`)
	name = reg.ReplaceAllString(name, "_")

	// Remove leading/trailing underscores
	name = strings.Trim(name, "_")

	return name + ext
}

// GenerateUniqueFilename generates unique filename with timestamp
func GenerateUniqueFilename(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	base := strings.TrimSuffix(originalFilename, ext)
	base = SanitizeFilename(base)

	// Add timestamp to make it unique
	timestamp := fmt.Sprintf("%d", GetCurrentTimestamp())

	return fmt.Sprintf("%s_%s%s", base, timestamp, ext)
}

// GetCurrentTimestamp returns current unix timestamp
func GetCurrentTimestamp() int64 {
	return time.Now().Unix()
}

// ValidateFileSize checks if file size is within limits
func ValidateFileSize(size int64, mediaType MediaType) bool {
	limits := map[MediaType]int64{
		MediaTypeImage:    10 * 1024 * 1024,  // 10MB
		MediaTypeVideo:    100 * 1024 * 1024, // 100MB
		MediaTypeAudio:    50 * 1024 * 1024,  // 50MB
		MediaTypeDocument: 20 * 1024 * 1024,  // 20MB
	}

	if limit, exists := limits[mediaType]; exists {
		return size <= limit
	}

	return size <= 10*1024*1024 // Default 10MB
}

// FormatFileSize formats file size in human-readable format
func FormatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}

	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}

	units := []string{"KB", "MB", "GB", "TB", "PB"}
	return fmt.Sprintf("%.1f %s", float64(size)/float64(div), units[exp])
}

// GenerateImageSrcSet generates srcset attribute for responsive images
func GenerateImageSrcSet(baseURL, filename string, variants []ImageVariant) string {
	var srcSet []string

	for _, variant := range variants {
		src := fmt.Sprintf("%s/%s", strings.TrimSuffix(baseURL, "/"), variant.Filename)
		srcSet = append(srcSet, fmt.Sprintf("%s %dw", src, variant.Width))
	}

	return strings.Join(srcSet, ", ")
}

// GenerateImageSizes generates sizes attribute for responsive images
func GenerateImageSizes(breakpoints []string) string {
	if len(breakpoints) == 0 {
		return "(max-width: 768px) 100vw, 50vw"
	}

	return strings.Join(breakpoints, ", ")
}

// OptimizeImageFilename generates SEO-friendly image filename
func OptimizeImageFilename(originalFilename, title, alt string) string {
	var filename string

	if title != "" {
		filename = Slugify(title)
	} else if alt != "" {
		filename = Slugify(alt)
	} else {
		// Use original filename without extension
		ext := filepath.Ext(originalFilename)
		filename = strings.TrimSuffix(originalFilename, ext)
		filename = Slugify(filename)
	}

	// Add original extension
	ext := filepath.Ext(originalFilename)
	return filename + ext
}

// String methods for enums
func (mt MediaType) String() string {
	return string(mt)
}

func (if_ ImageFormat) String() string {
	return string(if_)
}

func (vf VideoFormat) String() string {
	return string(vf)
}

// GetFileTypeFromMimeType returns the file type based on MIME type
func GetFileTypeFromMimeType(mimeType string) string {
	mimeType = strings.ToLower(mimeType)
	
	switch {
	case strings.HasPrefix(mimeType, "image/"):
		return "image"
	case strings.HasPrefix(mimeType, "video/"):
		return "video"
	case strings.HasPrefix(mimeType, "audio/"):
		return "audio"
	case strings.HasPrefix(mimeType, "application/pdf"), 
		 strings.HasPrefix(mimeType, "application/msword"),
		 strings.HasPrefix(mimeType, "application/vnd.openxmlformats-officedocument"),
		 strings.HasPrefix(mimeType, "text/"):
		return "document"
	case strings.HasPrefix(mimeType, "application/zip"),
		 strings.HasPrefix(mimeType, "application/x-tar"),
		 strings.HasPrefix(mimeType, "application/x-gzip"),
		 strings.HasPrefix(mimeType, "application/x-7z-compressed"):
		return "archive"
	default:
		return "other"
	}
}

// CalculateFileHash calculates SHA256 hash of a file
func CalculateFileHash(reader io.Reader) (string, error) {
	hash := sha256.New()
	if _, err := io.Copy(hash, reader); err != nil {
		return "", err
	}
	return hex.EncodeToString(hash.Sum(nil)), nil
}

// GenerateSlug generates a URL-friendly slug from a string
func GenerateSlug(s string) string {
	return Slugify(s)
}
