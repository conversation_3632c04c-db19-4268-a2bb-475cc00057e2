package validator

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"

	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/vi"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	entranslations "github.com/go-playground/validator/v10/translations/en"
	vitranslations "github.com/go-playground/validator/v10/translations/vi"
)

// PlaygroundValidator implements the Validator interface using go-playground/validator
type PlaygroundValidator struct {
	validate     *validator.Validate
	uni          *ut.UniversalTranslator
	trans        ut.Translator
	translations map[string]map[string]string
	mu           sync.RWMutex
}

// NewValidator creates a new validator instance
func NewValidator(opts ...Options) (Validator, error) {
	options := &Options{
		TagName:         "validate",
		DefaultLocale:   "en",
		RequiredDefault: false,
	}

	if len(opts) > 0 {
		options = &opts[0]
	}

	v := validator.New()
	v.SetTag<PERSON>ame(options.TagName)

	// Set up translators
	enLocale := en.New()
	viLocale := vi.New()
	uni := ut.New(enLocale, enLocale, viLocale)

	trans, found := uni.GetTranslator(options.DefaultLocale)
	if !found {
		return nil, fmt.Errorf("translator not found for locale: %s", options.DefaultLocale)
	}

	// Register default translations
	switch options.DefaultLocale {
	case "en":
		if err := entranslations.RegisterDefaultTranslations(v, trans); err != nil {
			return nil, fmt.Errorf("failed to register en translations: %w", err)
		}
	case "vi":
		if err := vitranslations.RegisterDefaultTranslations(v, trans); err != nil {
			return nil, fmt.Errorf("failed to register vi translations: %w", err)
		}
	}

	// Set up tag name function for JSON tags
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	pv := &PlaygroundValidator{
		validate:     v,
		uni:          uni,
		trans:        trans,
		translations: make(map[string]map[string]string),
	}

	// Register custom translations if provided
	if options.CustomTranslations != nil {
		for locale, translations := range options.CustomTranslations {
			for tag, translation := range translations {
				if err := pv.RegisterTranslation(tag, locale, translation); err != nil {
					return nil, fmt.Errorf("failed to register custom translation: %w", err)
				}
			}
		}
	}

	// Register common custom validators
	if err := pv.registerCommonValidators(); err != nil {
		return nil, fmt.Errorf("failed to register common validators: %w", err)
	}

	return pv, nil
}

// GetUnderlyingValidator returns the underlying go-playground validator instance
func (pv *PlaygroundValidator) GetUnderlyingValidator() *validator.Validate {
	return pv.validate
}

// Validate validates a struct and returns validation errors
func (pv *PlaygroundValidator) Validate(ctx context.Context, v interface{}) error {
	if err := pv.validate.StructCtx(ctx, v); err != nil {
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			return pv.formatValidationErrors(validationErrors)
		}
		return err
	}
	return nil
}

// ValidateField validates a single field
func (pv *PlaygroundValidator) ValidateField(ctx context.Context, field interface{}, tag string) error {
	if err := pv.validate.VarCtx(ctx, field, tag); err != nil {
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			return pv.formatValidationErrors(validationErrors)
		}
		return err
	}
	return nil
}

// RegisterValidation registers a custom validation function
func (pv *PlaygroundValidator) RegisterValidation(tag string, fn ValidationFunc) error {
	return pv.validate.RegisterValidation(tag, func(fl validator.FieldLevel) bool {
		return fn(context.Background(), &fieldLevel{fl: fl})
	})
}

// RegisterTagNameFunc registers a function to get alternate names for struct fields
func (pv *PlaygroundValidator) RegisterTagNameFunc(fn TagNameFunc) {
	pv.validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		return fn(fld)
	})
}

// RegisterTranslation registers a translation for a validation tag
func (pv *PlaygroundValidator) RegisterTranslation(tag string, locale string, translation string) error {
	pv.mu.Lock()
	defer pv.mu.Unlock()

	trans, found := pv.uni.GetTranslator(locale)
	if !found {
		return fmt.Errorf("translator not found for locale: %s", locale)
	}

	if pv.translations[locale] == nil {
		pv.translations[locale] = make(map[string]string)
	}
	pv.translations[locale][tag] = translation

	return pv.validate.RegisterTranslation(tag, trans, func(ut ut.Translator) error {
		return ut.Add(tag, translation, true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T(tag, fe.Field(), fe.Param())
		return t
	})
}

// SetLocale sets the locale for error messages
func (pv *PlaygroundValidator) SetLocale(locale string) error {
	pv.mu.Lock()
	defer pv.mu.Unlock()

	trans, found := pv.uni.GetTranslator(locale)
	if !found {
		return fmt.Errorf("translator not found for locale: %s", locale)
	}

	pv.trans = trans

	// Re-register translations based on locale
	switch locale {
	case "en":
		if err := entranslations.RegisterDefaultTranslations(pv.validate, trans); err != nil {
			return fmt.Errorf("failed to register en translations: %w", err)
		}
	case "vi":
		// Vietnamese translations may have conflicts, ignore errors for now
		vitranslations.RegisterDefaultTranslations(pv.validate, trans)
	}

	// Re-register custom translations for this locale
	if translations, ok := pv.translations[locale]; ok {
		for tag, translation := range translations {
			pv.validate.RegisterTranslation(tag, trans, func(ut ut.Translator) error {
				return ut.Add(tag, translation, true)
			}, func(ut ut.Translator, fe validator.FieldError) string {
				t, _ := ut.T(tag, fe.Field(), fe.Param())
				return t
			})
		}
	}

	return nil
}

// formatValidationErrors formats validation errors into our custom type
func (pv *PlaygroundValidator) formatValidationErrors(errs validator.ValidationErrors) error {
	var validationErrors ValidationErrors

	for _, err := range errs {
		validationErrors = append(validationErrors, &validationError{
			field:   err.Field(),
			tag:     err.Tag(),
			value:   err.Value(),
			param:   err.Param(),
			message: err.Translate(pv.trans),
		})
	}

	return validationErrors
}

// registerCommonValidators registers commonly used custom validators
func (pv *PlaygroundValidator) registerCommonValidators() error {
	// Phone number validator (Vietnam)
	if err := pv.RegisterValidation("vnphone", func(ctx context.Context, fl FieldLevel) bool {
		phoneValue := fl.Field()
		phone, ok := phoneValue.(string)
		if !ok {
			return false
		}
		return IsVietnamesePhone(phone)
	}); err != nil {
		return err
	}

	// Vietnam ID card validator
	if err := pv.RegisterValidation("vnidcard", func(ctx context.Context, fl FieldLevel) bool {
		idValue := fl.Field()
		id, ok := idValue.(string)
		if !ok {
			return false
		}
		return IsVietnameseIDCard(id)
	}); err != nil {
		return err
	}

	// Register translations for custom validators
	translations := map[string]map[string]string{
		"en": {
			"vnphone":  "{0} must be a valid Vietnamese phone number",
			"vnidcard": "{0} must be a valid Vietnamese ID card number",
		},
		"vi": {
			"vnphone":  "{0} phải là số điện thoại Việt Nam hợp lệ",
			"vnidcard": "{0} phải là số CMND/CCCD Việt Nam hợp lệ",
		},
	}

	for locale, trans := range translations {
		for tag, translation := range trans {
			if err := pv.RegisterTranslation(tag, locale, translation); err != nil {
				return err
			}
		}
	}

	return nil
}

// fieldLevel wraps validator.FieldLevel to implement our FieldLevel interface
type fieldLevel struct {
	fl validator.FieldLevel
}

func (f *fieldLevel) Field() interface{} {
	return f.fl.Field().Interface()
}

func (f *fieldLevel) FieldName() string {
	return f.fl.FieldName()
}

func (f *fieldLevel) StructFieldName() string {
	return f.fl.StructFieldName()
}

func (f *fieldLevel) Param() string {
	return f.fl.Param()
}

func (f *fieldLevel) GetTag() string {
	// go-playground/validator doesn't expose the tag directly
	// We'll return empty string for now
	return ""
}

func (f *fieldLevel) ExtractType() (interface{}, bool) {
	field := f.fl.Field()
	if field.IsValid() {
		return field.Type(), true
	}
	return nil, false
}

// validationError implements ValidationError interface
type validationError struct {
	field   string
	tag     string
	value   interface{}
	param   string
	message string
}

func (ve *validationError) Error() string {
	return ve.message
}

func (ve *validationError) Field() string {
	return ve.field
}

func (ve *validationError) Tag() string {
	return ve.tag
}

func (ve *validationError) Value() interface{} {
	return ve.value
}

func (ve *validationError) Param() string {
	return ve.param
}

func (ve *validationError) Message() string {
	return ve.message
}

func (ve *validationError) Translate(translator Translator) string {
	// For now, return the message as-is since it's already translated
	return ve.message
}
