package pagination

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// CursorRequest represents a cursor-based pagination request
type CursorRequest struct {
	Cursor string `json:"cursor,omitempty" form:"cursor"`
	Limit  int    `json:"limit,omitempty" form:"limit"`
}

// CursorResponse represents a cursor-based pagination response
type CursorResponse struct {
	HasNext        bool   `json:"has_next"`
	NextCursor     string `json:"next_cursor"`
	HasPrevious    bool   `json:"has_previous"`
	PreviousCursor string `json:"previous_cursor"`
	Count          int    `json:"count"`
	Has<PERSON>ore        bool   `json:"has_more"` // Alias for HasNext
	Limit          int    `json:"limit"`
}

// CursorData represents the internal cursor data
type CursorData struct {
	ID        uint      `json:"id"`
	CreatedAt time.Time `json:"created_at"`
}

// Cursor represents a cursor with ID, time and optional score
type Cursor struct {
	ID    int64     `json:"id"`
	Time  time.Time `json:"time"`
	Score *float64  `json:"score,omitempty"`
}

// DefaultLimit is the default pagination limit
const DefaultLimit = 10

// MaxLimit is the maximum pagination limit
const MaxLimit = 100

// NewCursorRequest creates a new cursor request with validation
func NewCursorRequest(cursor string, limit int) *CursorRequest {
	if limit <= 0 {
		limit = DefaultLimit
	}
	if limit > MaxLimit {
		limit = MaxLimit
	}

	return &CursorRequest{
		Cursor: cursor,
		Limit:  limit,
	}
}

// ParseCursor parses a cursor string into CursorData
func ParseCursor(cursor string) (*CursorData, error) {
	if cursor == "" {
		return nil, nil
	}

	// Decode base64
	decoded, err := base64.URLEncoding.DecodeString(cursor)
	if err != nil {
		return nil, fmt.Errorf("invalid cursor format: %w", err)
	}

	// Parse JSON
	var cursorData CursorData
	if err := json.Unmarshal(decoded, &cursorData); err != nil {
		return nil, fmt.Errorf("invalid cursor data: %w", err)
	}

	return &cursorData, nil
}

// EncodeCursor encodes cursor data into a cursor string
func EncodeCursor(id uint, createdAt time.Time) (string, error) {
	cursorData := CursorData{
		ID:        id,
		CreatedAt: createdAt,
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(cursorData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cursor data: %w", err)
	}

	// Encode to base64
	encoded := base64.URLEncoding.EncodeToString(jsonData)
	return encoded, nil
}

// GetLimitWithDefault returns the limit with default value if not set
func (cr *CursorRequest) GetLimitWithDefault() int {
	if cr.Limit <= 0 {
		return DefaultLimit
	}
	if cr.Limit > MaxLimit {
		return MaxLimit
	}
	return cr.Limit
}

// ParseLimitFromString parses limit from string with validation
func ParseLimitFromString(limitStr string) int {
	if limitStr == "" {
		return DefaultLimit
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		return DefaultLimit
	}

	if limit <= 0 {
		return DefaultLimit
	}
	if limit > MaxLimit {
		return MaxLimit
	}

	return limit
}

// NewCursorResponse creates a new cursor response
func NewCursorResponse(hasNext bool, nextCursor string, count int) *CursorResponse {
	return &CursorResponse{
		HasNext:    hasNext,
		NextCursor: nextCursor,
		Count:      count,
		HasMore:    hasNext,
	}
}

// DecodeCursor decodes a cursor string into a Cursor struct
func DecodeCursor(encoded string) (*Cursor, error) {
	if encoded == "" {
		return nil, nil
	}

	// Decode base64
	decoded, err := base64.URLEncoding.DecodeString(encoded)
	if err != nil {
		return nil, fmt.Errorf("invalid cursor format: %w", err)
	}

	// Parse JSON
	var cursor Cursor
	if err := json.Unmarshal(decoded, &cursor); err != nil {
		return nil, fmt.Errorf("invalid cursor data: %w", err)
	}

	return &cursor, nil
}

// NewCursorFromEntity creates a new cursor from entity data
func NewCursorFromEntity(id int64, time time.Time, score *float64) *Cursor {
	return &Cursor{
		ID:    id,
		Time:  time,
		Score: score,
	}
}

// String encodes the cursor to a base64 string
func (c *Cursor) String() (string, error) {
	// Marshal to JSON
	jsonData, err := json.Marshal(c)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cursor: %w", err)
	}

	// Encode to base64
	encoded := base64.URLEncoding.EncodeToString(jsonData)
	return encoded, nil
}

// CursorPagination represents cursor-based pagination parameters
type CursorPagination struct {
	Cursor string
	Limit  int
}

// ValidateLimit validates and normalizes a limit value
func ValidateLimit(limit int) int {
	if limit <= 0 {
		return DefaultLimit
	}
	if limit > MaxLimit {
		return MaxLimit
	}
	return limit
}

// NewCursorPaginationFromQuery creates a CursorPagination from Gin context query parameters
func NewCursorPaginationFromQuery(c interface{}) *CursorPagination {
	// This is a temporary implementation - ideally we'd extract actual query params
	// For now, we'll create a simple implementation with defaults
	return &CursorPagination{
		Cursor: "", // This would be extracted from query params
		Limit:  DefaultLimit,
	}
}

// ParseCursorRequest parses cursor request from Gin context
func ParseCursorRequest(c interface{}) *CursorRequest {
	cursor := ""
	limit := DefaultLimit

	// Extract cursor and limit from query parameters if available
	// This is a basic implementation - in production you'd want better type checking
	if ginCtx, ok := c.(*gin.Context); ok {
		cursor = ginCtx.Query("cursor")
		if limitStr := ginCtx.Query("limit"); limitStr != "" {
			if parsedLimit, err := strconv.Atoi(limitStr); err == nil {
				limit = ValidateLimit(parsedLimit)
			}
		}
	}

	return &CursorRequest{
		Cursor: cursor,
		Limit:  limit,
	}
}

// CursorConfig contains configuration for cursor-based pagination
type CursorConfig struct {
	DefaultSortField  string   `json:"default_sort_field"`
	AllowedSortFields []string `json:"allowed_sort_fields"`
	DefaultLimit      int      `json:"default_limit"`
	MaxLimit          int      `json:"max_limit"`
	DefaultSortOrder  string   `json:"default_sort_order"` // "ASC" or "DESC"
}

// CursorBuilder helps build cursor-based queries with standardized patterns
type CursorBuilder struct {
	config CursorConfig
}

// NewCursorBuilder creates a new CursorBuilder with configuration
func NewCursorBuilder(config CursorConfig) *CursorBuilder {
	// Set defaults if not provided
	if config.DefaultSortField == "" {
		config.DefaultSortField = "created_at"
	}
	if config.DefaultLimit <= 0 {
		config.DefaultLimit = DefaultLimit
	}
	if config.MaxLimit <= 0 {
		config.MaxLimit = MaxLimit
	}
	if config.DefaultSortOrder == "" {
		config.DefaultSortOrder = "DESC"
	}
	if len(config.AllowedSortFields) == 0 {
		config.AllowedSortFields = []string{"created_at", "updated_at", "id"}
	}

	return &CursorBuilder{
		config: config,
	}
}

// DefaultCursorBuilder returns a CursorBuilder with default configuration
func DefaultCursorBuilder() *CursorBuilder {
	return NewCursorBuilder(CursorConfig{
		DefaultSortField:  "created_at",
		AllowedSortFields: []string{"created_at", "updated_at", "id"},
		DefaultLimit:      DefaultLimit,
		MaxLimit:          MaxLimit,
		DefaultSortOrder:  "DESC",
	})
}

// ValidateSortField checks if the sort field is allowed
func (cb *CursorBuilder) ValidateSortField(sortField string) string {
	if sortField == "" {
		return cb.config.DefaultSortField
	}

	for _, allowed := range cb.config.AllowedSortFields {
		if allowed == sortField {
			return sortField
		}
	}

	return cb.config.DefaultSortField
}

// ValidateSortOrder validates sort order
func (cb *CursorBuilder) ValidateSortOrder(sortOrder string) string {
	switch sortOrder {
	case "ASC", "DESC", "asc", "desc":
		return sortOrder
	default:
		return cb.config.DefaultSortOrder
	}
}

// ValidateLimit validates and normalizes limit
func (cb *CursorBuilder) ValidateLimit(limit int) int {
	if limit <= 0 {
		return cb.config.DefaultLimit
	}
	if limit > cb.config.MaxLimit {
		return cb.config.MaxLimit
	}
	return limit
}

// BuildOrderClause builds SQL ORDER BY clause for cursor pagination
func (cb *CursorBuilder) BuildOrderClause(sortBy string, sortOrder string) string {
	validSortBy := cb.ValidateSortField(sortBy)
	validSortOrder := cb.ValidateSortOrder(sortOrder)

	// Always include ID for consistent ordering
	return fmt.Sprintf("%s %s, id %s", validSortBy, validSortOrder, validSortOrder)
}

// BuildWhereClause builds WHERE clause for cursor pagination
func (cb *CursorBuilder) BuildWhereClause(cursor string, sortBy string, sortOrder string) (string, []interface{}, error) {
	if cursor == "" {
		return "", nil, nil
	}

	cursorData, err := ParseCursor(cursor)
	if err != nil {
		return "", nil, err
	}

	validSortBy := cb.ValidateSortField(sortBy)
	validSortOrder := cb.ValidateSortOrder(sortOrder)

	var operator string
	if validSortOrder == "DESC" || validSortOrder == "desc" {
		operator = "<"
	} else {
		operator = ">"
	}

	// Build WHERE clause based on sort field
	if validSortBy == "created_at" {
		whereClause := fmt.Sprintf("(%s %s ? OR (%s = ? AND id %s ?))", validSortBy, operator, validSortBy, operator)
		args := []interface{}{cursorData.CreatedAt, cursorData.CreatedAt, cursorData.ID}
		return whereClause, args, nil
	}

	// For other sort fields, we need the actual field value from cursor
	// This would need to be extended based on requirements
	return "", nil, fmt.Errorf("cursor pagination for sort field %s not yet implemented", validSortBy)
}

// BuildResponse builds a standardized cursor response
func (cb *CursorBuilder) BuildResponse(items interface{}, limit int, hasMore bool, lastID uint, lastCreatedAt time.Time) (*CursorResponse, error) {
	response := &CursorResponse{
		HasNext: hasMore,
		HasMore: hasMore,
		Count:   0,
		Limit:   limit,
	}

	// Count items (this assumes items is a slice)
	if items != nil {
		// Use reflection or type assertion to get length
		// For now, let's assume it's handled by the caller
	}

	// Generate next cursor if there are more items
	if hasMore && lastID > 0 {
		nextCursor, err := EncodeCursor(lastID, lastCreatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to encode next cursor: %w", err)
		}
		response.NextCursor = nextCursor
	}

	return response, nil
}

// ExtendedCursorData represents cursor data with additional fields
type ExtendedCursorData struct {
	ID        uint                   `json:"id"`
	CreatedAt time.Time              `json:"created_at"`
	SortField string                 `json:"sort_field,omitempty"`
	SortValue interface{}            `json:"sort_value,omitempty"`
	Extra     map[string]interface{} `json:"extra,omitempty"`
}

// ParseExtendedCursor parses a cursor string into ExtendedCursorData
func ParseExtendedCursor(cursor string) (*ExtendedCursorData, error) {
	if cursor == "" {
		return nil, nil
	}

	// Decode base64
	decoded, err := base64.URLEncoding.DecodeString(cursor)
	if err != nil {
		return nil, fmt.Errorf("invalid cursor format: %w", err)
	}

	// Parse JSON
	var cursorData ExtendedCursorData
	if err := json.Unmarshal(decoded, &cursorData); err != nil {
		return nil, fmt.Errorf("invalid cursor data: %w", err)
	}

	return &cursorData, nil
}

// EncodeExtendedCursor encodes extended cursor data into a cursor string
func EncodeExtendedCursor(data ExtendedCursorData) (string, error) {
	// Marshal to JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cursor data: %w", err)
	}

	// Encode to base64
	encoded := base64.URLEncoding.EncodeToString(jsonData)
	return encoded, nil
}
