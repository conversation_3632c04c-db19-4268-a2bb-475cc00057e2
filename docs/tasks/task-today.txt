internal/modules/blog/handlers/blog_post_rating_handler.go.bak
internal/modules/blog/handlers/export_handler.go.bak
internal/modules/blog/routes_disabled

docs/features/meilisearch-integration.md
meilisearch
- setup docker meilisearch
- sync blog: full docs
- test meilisearch 

trello
- thêm website_id
- sửa migration, dto, service ...



Module
- review lại hệ thống cần tenant_id và website_id


Rbac:
- Init khi có teannt mới
- Các permissions global share như thế nào

Phân trang 
./docs/module-structure.md


website_template_categories
Xóa
    meta_title VARCHAR(255),
    meta_description TEXT,


website_pages
Xóa
        meta_title VARCHAR(255) NULL,
    meta_description TEXT NULL,
    meta_keywords TEXT NULL,


blog_post_revisions
Xóa
    meta_title VARCHAR(255),
    meta_description TEXT,

BlogCategory
Xóa
    meta_title
    meta_keywords


config/monitoring/metrics.yaml
configs/minio-example.yaml