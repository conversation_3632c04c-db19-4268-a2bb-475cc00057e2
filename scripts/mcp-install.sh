npm install -g mcp-chrome-bridge

uvx --from git+https://github.com/oraios/serena serena-mcp-server


npx ccusage@latest
ccusage blocks --live



npx @agent-infra/mcp-server-browser@latest
npx @agentdeskai/browser-tools-server@latest

uvx --from git+https://github.com/oraios/serena index-project


uvx --from git+https://github.com/oraios/serena serena-mcp-server --context ide-assistant --transport sse --port 9121