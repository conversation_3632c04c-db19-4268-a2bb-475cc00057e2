package services

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/repositories/mysql"
	mysqlDriver "gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// AIChatServiceIntegrationTestSuite is the test suite for AI chat service integration tests
type AIChatServiceIntegrationTestSuite struct {
	suite.Suite
	db      *gorm.DB
	service AIChatService
	ctx     context.Context
}

// SetupSuite sets up the test suite
func (suite *AIChatServiceIntegrationTestSuite) SetupSuite() {
	// Skip if DB_HOST is not set (CI environment without database)
	if os.Getenv("DB_HOST") == "" {
		suite.T().Skip("Skipping integration tests: DB_HOST not set")
	}

	// Setup database connection
	dsn := os.Getenv("TEST_DB_DSN")
	if dsn == "" {
		dsn = "root:password@tcp(localhost:3306)/wn_api_v3_test?charset=utf8mb4&parseTime=True&loc=Local"
	}

	var err error
	suite.db, err = gorm.Open(mysqlDriver.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(suite.T(), err)

	// Auto-migrate tables for testing
	err = suite.db.AutoMigrate(
		&models.AIChatSession{},
		&models.AIChatMessage{},
	)
	require.NoError(suite.T(), err)

	// Create service
	chatRepo := mysql.NewAIChatRepository(suite.db)
	mockLogger := NewMockLogger()
	config := &AIChatConfig{
		MaxSessionsPerUser:    100,
		MaxMessagesPerSession: 1000,
		MaxMessageLength:      10000,
		MaxTokensPerMessage:   4000,
		MaxTokensPerSession:   50000,
		ContextWindowTokens:   8000,
		SessionTimeoutHours:   24,
		EnableContentFilter:   false, // Disable for testing
		EnablePIIDetection:    false, // Disable for testing
		EnableRateLimiting:    false, // Disable for testing
		DefaultSystemPrompt:   "You are a helpful AI assistant.",
	}

	suite.service = NewAIChatService(chatRepo, mockLogger, config)
	suite.ctx = context.Background()
}

// TearDownSuite tears down the test suite
func (suite *AIChatServiceIntegrationTestSuite) TearDownSuite() {
	if suite.db != nil {
		// Clean up test data
		suite.db.Exec("DELETE FROM ai_chat_messages")
		suite.db.Exec("DELETE FROM ai_chat_sessions")

		// Close database connection
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// SetupTest sets up each test
func (suite *AIChatServiceIntegrationTestSuite) SetupTest() {
	// Clean up test data before each test
	suite.db.Exec("DELETE FROM ai_chat_messages")
	suite.db.Exec("DELETE FROM ai_chat_sessions")
}

// TestCreateAndGetSession tests creating and retrieving a session
func (suite *AIChatServiceIntegrationTestSuite) TestCreateAndGetSession() {
	userID := uint(1)
	req := &models.CreateChatSessionRequest{
		TenantID:     1,
		UserID:       &userID,
		Title:        "Integration Test Session",
		ModelID:      1,
		SystemPrompt: stringPtr("You are a helpful assistant."),
		Context:      models.JSONMap{"test": "value"},
	}

	// Create session
	result, err := suite.service.CreateSession(suite.ctx, req)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), req.TenantID, result.TenantID)
	assert.Equal(suite.T(), req.Title, result.Title)
	assert.Equal(suite.T(), req.ModelID, result.ModelID)
	assert.Equal(suite.T(), models.ChatSessionStatusActive, result.Status)
	assert.NotZero(suite.T(), result.ID)

	// Get session
	getResponse, err := suite.service.GetSession(suite.ctx, result.ID, req.TenantID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), result.ID, getResponse.ID)
	assert.Equal(suite.T(), result.Title, getResponse.Title)
	assert.Equal(suite.T(), result.TenantID, getResponse.TenantID)
}

// TestSessionLifecycle tests the full lifecycle of a session
func (suite *AIChatServiceIntegrationTestSuite) TestSessionLifecycle() {
	userID := uint(1)
	tenantID := uint(1)

	// Create session
	createReq := &models.CreateChatSessionRequest{
		TenantID: tenantID,
		UserID:   &userID,
		Title:    "Lifecycle Test Session",
		ModelID:  1,
	}

	session, err := suite.service.CreateSession(suite.ctx, createReq)
	require.NoError(suite.T(), err)

	// Update session
	updateReq := &models.UpdateChatSessionRequest{
		Title:   "Updated Lifecycle Test Session",
		Status:  models.ChatSessionStatusArchived,
		Context: models.JSONMap{"updated": "value"},
	}

	updatedSession, err := suite.service.UpdateSession(suite.ctx, session.ID, updateReq, tenantID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), updateReq.Title, updatedSession.Title)
	assert.Equal(suite.T(), updateReq.Status, updatedSession.Status)

	// Delete session
	err = suite.service.DeleteSession(suite.ctx, session.ID, tenantID)
	require.NoError(suite.T(), err)

	// Verify session is deleted (should return error)
	_, err = suite.service.GetSession(suite.ctx, session.ID, tenantID)
	assert.Error(suite.T(), err)
}

// TestMessageLifecycle tests the full lifecycle of messages
func (suite *AIChatServiceIntegrationTestSuite) TestMessageLifecycle() {
	userID := uint(1)
	tenantID := uint(1)

	// Create session first
	sessionReq := &models.CreateChatSessionRequest{
		TenantID: tenantID,
		UserID:   &userID,
		Title:    "Message Test Session",
		ModelID:  1,
	}

	session, err := suite.service.CreateSession(suite.ctx, sessionReq)
	require.NoError(suite.T(), err)

	// Create message
	messageReq := &models.CreateChatMessageRequest{
		SessionID:  session.ID,
		Role:       models.ChatMessageRoleUser,
		Content:    "Hello, this is a test message!",
		Metadata:   models.JSONMap{"test": "metadata"},
		TokensUsed: 15,
	}

	message, err := suite.service.CreateMessage(suite.ctx, messageReq, tenantID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), messageReq.SessionID, message.SessionID)
	assert.Equal(suite.T(), messageReq.Role, message.Role)
	assert.Equal(suite.T(), messageReq.Content, message.Content)
	assert.Equal(suite.T(), messageReq.TokensUsed, message.TokensUsed)

	// Get message
	getMessage, err := suite.service.GetMessage(suite.ctx, message.ID, tenantID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), message.ID, getMessage.ID)
	assert.Equal(suite.T(), message.Content, getMessage.Content)

	// Update message
	updateReq := &models.UpdateChatMessageRequest{
		Content:    "Updated test message content",
		TokensUsed: 20,
	}

	updatedMessage, err := suite.service.UpdateMessage(suite.ctx, message.ID, updateReq, tenantID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), updateReq.Content, updatedMessage.Content)
	assert.Equal(suite.T(), updateReq.TokensUsed, updatedMessage.TokensUsed)

	// List messages
	filter := models.ChatMessageFilter{
		Page:     1,
		PageSize: 10,
	}

	listResponse, err := suite.service.ListMessages(suite.ctx, session.ID, tenantID, filter)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(1), listResponse.TotalCount)
	assert.Len(suite.T(), listResponse.Messages, 1)
	assert.Equal(suite.T(), updatedMessage.ID, listResponse.Messages[0].ID)

	// Delete message
	err = suite.service.DeleteMessage(suite.ctx, message.ID, tenantID)
	require.NoError(suite.T(), err)

	// Verify message is deleted
	_, err = suite.service.GetMessage(suite.ctx, message.ID, tenantID)
	assert.Error(suite.T(), err)
}

// TestConversationHistory tests conversation history functionality
func (suite *AIChatServiceIntegrationTestSuite) TestConversationHistory() {
	userID := uint(1)
	tenantID := uint(1)

	// Create session
	sessionReq := &models.CreateChatSessionRequest{
		TenantID: tenantID,
		UserID:   &userID,
		Title:    "Conversation Test Session",
		ModelID:  1,
	}

	session, err := suite.service.CreateSession(suite.ctx, sessionReq)
	require.NoError(suite.T(), err)

	// Create multiple messages
	messages := []models.CreateChatMessageRequest{
		{
			SessionID:  session.ID,
			Role:       models.ChatMessageRoleUser,
			Content:    "Hello, how are you?",
			TokensUsed: 10,
		},
		{
			SessionID:  session.ID,
			Role:       models.ChatMessageRoleAssistant,
			Content:    "I'm doing well, thank you! How can I help you today?",
			TokensUsed: 15,
		},
		{
			SessionID:  session.ID,
			Role:       models.ChatMessageRoleUser,
			Content:    "Can you help me with a coding problem?",
			TokensUsed: 12,
		},
	}

	for _, msgReq := range messages {
		_, err := suite.service.CreateMessage(suite.ctx, &msgReq, tenantID)
		require.NoError(suite.T(), err)
		time.Sleep(10 * time.Millisecond) // Ensure different timestamps
	}

	// Get conversation history
	conversation, err := suite.service.GetConversationHistory(suite.ctx, session.ID, tenantID, 10)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), session.ID, conversation.SessionID)
	assert.Equal(suite.T(), session.Title, conversation.SessionTitle)
	assert.Len(suite.T(), conversation.Messages, 3)
	assert.Equal(suite.T(), 37, conversation.TotalTokens) // 10 + 15 + 12

	// Verify message order (should be chronological)
	assert.Equal(suite.T(), models.ChatMessageRoleUser, conversation.Messages[0].Role)
	assert.Equal(suite.T(), models.ChatMessageRoleAssistant, conversation.Messages[1].Role)
	assert.Equal(suite.T(), models.ChatMessageRoleUser, conversation.Messages[2].Role)
}

// TestBulkOperations tests bulk operations functionality
func (suite *AIChatServiceIntegrationTestSuite) TestBulkOperations() {
	userID := uint(1)
	tenantID := uint(1)

	// Create session
	sessionReq := &models.CreateChatSessionRequest{
		TenantID: tenantID,
		UserID:   &userID,
		Title:    "Bulk Test Session",
		ModelID:  1,
	}

	session, err := suite.service.CreateSession(suite.ctx, sessionReq)
	require.NoError(suite.T(), err)

	// Test bulk create messages
	bulkReq := &models.BulkCreateChatMessagesRequest{
		SessionID: session.ID,
		Messages: []models.CreateChatMessageRequest{
			{
				SessionID:  session.ID,
				Role:       models.ChatMessageRoleUser,
				Content:    "First message",
				TokensUsed: 5,
			},
			{
				SessionID:  session.ID,
				Role:       models.ChatMessageRoleAssistant,
				Content:    "Second message",
				TokensUsed: 8,
			},
			{
				SessionID:  session.ID,
				Role:       models.ChatMessageRoleUser,
				Content:    "Third message",
				TokensUsed: 6,
			},
		},
	}

	bulkResponse, err := suite.service.BulkCreateMessages(suite.ctx, bulkReq, tenantID)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), bulkResponse, 3)

	// Verify messages were created
	filter := models.ChatMessageFilter{
		Page:     1,
		PageSize: 10,
	}

	listResponse, err := suite.service.ListMessages(suite.ctx, session.ID, tenantID, filter)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(3), listResponse.TotalCount)

	// Test bulk delete messages
	messageIDs := make([]uint, len(bulkResponse))
	for i, msg := range bulkResponse {
		messageIDs[i] = msg.ID
	}

	err = suite.service.BulkDeleteMessages(suite.ctx, messageIDs, tenantID)
	require.NoError(suite.T(), err)

	// Verify messages were deleted
	listResponse, err = suite.service.ListMessages(suite.ctx, session.ID, tenantID, filter)
	require.NoError(t, err)
	assert.Equal(t, int64(0), listResponse.TotalCount)
}

// TestTenantIsolation tests tenant isolation
func (suite *AIChatServiceIntegrationTestSuite) TestTenantIsolation() {
	tenant1ID := uint(1)
	tenant2ID := uint(2)
	userID := uint(1)

	// Create session for tenant 1
	sessionReq1 := &models.CreateChatSessionRequest{
		TenantID: tenant1ID,
		UserID:   &userID,
		Title:    "Tenant 1 Session",
		ModelID:  1,
	}

	session1, err := suite.service.CreateSession(suite.ctx, sessionReq1)
	require.NoError(suite.T(), err)

	// Create session for tenant 2
	sessionReq2 := &models.CreateChatSessionRequest{
		TenantID: tenant2ID,
		UserID:   &userID,
		Title:    "Tenant 2 Session",
		ModelID:  1,
	}

	session2, err := suite.service.CreateSession(suite.ctx, sessionReq2)
	require.NoError(suite.T(), err)

	// Tenant 1 should not be able to access tenant 2's session
	_, err = suite.service.GetSession(suite.ctx, session2.ID, tenant1ID)
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), ErrUnauthorizedAccess, err)

	// Tenant 2 should not be able to access tenant 1's session
	_, err = suite.service.GetSession(suite.ctx, session1.ID, tenant2ID)
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), ErrUnauthorizedAccess, err)

	// Each tenant should be able to access their own session
	_, err = suite.service.GetSession(suite.ctx, session1.ID, tenant1ID)
	require.NoError(suite.T(), err)

	_, err = suite.service.GetSession(suite.ctx, session2.ID, tenant2ID)
	require.NoError(suite.T(), err)
}

// TestAIChatServiceIntegration runs the integration test suite
func TestAIChatServiceIntegration(t *testing.T) {
	suite.Run(t, new(AIChatServiceIntegrationTestSuite))
}
