package services

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// Test suite for AI request service
type AIRequestServiceTestSuite struct {
	suite.Suite
	service         AIRequestService
	mockRepo        *MockAIRequestRepository
	mockLogger      *MockLogger
	mockRateLimiter *MockRateLimiter
	mockPIIDetector *MockPIIDetector
	mockCostCalc    *MockCostCalculator
	mockSecurity    *MockSecurityService
	mockQueue       *MockQueueService
	ctx             context.Context
}

// SetupTest sets up the test suite
func (suite *AIRequestServiceTestSuite) SetupTest() {
	suite.mockRepo = new(MockAIRequestRepository)
	suite.mockLogger = NewMockLogger()
	suite.mockRateLimiter = new(MockRateLimiter)
	suite.mockPIIDetector = new(MockPIIDetector)
	suite.mockCostCalc = new(MockCostCalculator)
	suite.mockSecurity = new(MockSecurityService)
	suite.mockQueue = new(MockQueueService)
	suite.ctx = context.Background()

	suite.service = NewAIRequestService(
		suite.mockRepo,
		suite.mockLogger,
		suite.mockRateLimiter,
		suite.mockPIIDetector,
		suite.mockCostCalc,
		suite.mockSecurity,
		suite.mockQueue,
	)
}

// TestCreateRequest tests creating an AI request
func (suite *AIRequestServiceTestSuite) TestCreateRequest() {
	// Test successful creation
	input := CreateAIRequestInput{
		TenantID:    1,
		WebsiteID:   uintPtr(1),
		UserID:      uintPtr(1),
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
		Metadata:    models.JSONMap{"key": "value"},
	}

	// Mock rate limiter
	suite.mockRateLimiter.On("CheckLimit", mock.Anything, mock.Anything).Return(&RateLimitResult{
		Exceeded: false,
	}, nil)

	// Mock security service
	suite.mockSecurity.On("SanitizePrompt", mock.Anything, input.PromptText).Return(input.PromptText, nil)

	// Mock PII detector
	suite.mockPIIDetector.On("DetectPII", mock.Anything, input.PromptText).Return(PIIDetectionResult{
		HasPII: false,
	}, nil)

	// Mock repository
	suite.mockRepo.On("Create", mock.Anything, mock.AnythingOfType("*models.AIRequest")).Return(nil).Run(func(args mock.Arguments) {
		req := args.Get(1).(*models.AIRequest)
		req.ID = 1
	})

	// Mock logger
	suite.mockLogger.On("WithContext", mock.Anything).Return(suite.mockLogger)
	suite.mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

	// Execute test
	result, err := suite.service.CreateRequest(suite.ctx, input)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), uint(1), result.ID)
	assert.Equal(suite.T(), input.TenantID, result.TenantID)
	assert.Equal(suite.T(), input.RequestType, result.RequestType)
	assert.Equal(suite.T(), input.PromptText, result.PromptText)
	assert.Equal(suite.T(), models.AIRequestStatusPending, result.Status)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockRateLimiter.AssertExpectations(suite.T())
	suite.mockSecurity.AssertExpectations(suite.T())
	suite.mockPIIDetector.AssertExpectations(suite.T())
}

// TestCreateRequestWithRateLimit tests creating a request when rate limit is exceeded
func (suite *AIRequestServiceTestSuite) TestCreateRequestWithRateLimit() {
	input := CreateAIRequestInput{
		TenantID:    1,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
	}

	// Mock rate limiter to return exceeded
	suite.mockRateLimiter.On("CheckLimit", mock.Anything, mock.Anything).Return(&RateLimitResult{
		Exceeded: true,
		Message:  "Rate limit exceeded",
	}, nil)

	// Execute test
	result, err := suite.service.CreateRequest(suite.ctx, input)

	// Assertions
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "rate limit exceeded")

	// Verify mocks
	suite.mockRateLimiter.AssertExpectations(suite.T())
}

// TestCreateRequestWithPII tests creating a request with PII detection
func (suite *AIRequestServiceTestSuite) TestCreateRequestWithPII() {
	input := CreateAIRequestInput{
		TenantID:    1,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "My <NAME_EMAIL>",
	}

	// Mock rate limiter
	suite.mockRateLimiter.On("CheckLimit", mock.Anything, mock.Anything).Return(&RateLimitResult{
		Exceeded: false,
	}, nil)

	// Mock security service
	suite.mockSecurity.On("SanitizePrompt", mock.Anything, input.PromptText).Return(input.PromptText, nil)

	// Mock PII detector to detect PII
	suite.mockPIIDetector.On("DetectPII", mock.Anything, input.PromptText).Return(PIIDetectionResult{
		HasPII:       true,
		Types:        []string{"email"},
		RedactedText: "My email is [REDACTED]",
	}, nil)

	// Mock repository
	suite.mockRepo.On("Create", mock.Anything, mock.AnythingOfType("*models.AIRequest")).Return(nil).Run(func(args mock.Arguments) {
		req := args.Get(1).(*models.AIRequest)
		req.ID = 1
		// Verify PII metadata was added
		assert.True(suite.T(), req.Metadata["pii_detected"].(bool))
		assert.Equal(suite.T(), []string{"email"}, req.Metadata["pii_types"])
	})

	// Mock logger
	suite.mockLogger.On("WithContext", mock.Anything).Return(suite.mockLogger)
	suite.mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

	// Execute test
	result, err := suite.service.CreateRequest(suite.ctx, input)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockRateLimiter.AssertExpectations(suite.T())
	suite.mockSecurity.AssertExpectations(suite.T())
	suite.mockPIIDetector.AssertExpectations(suite.T())
}

// TestCreateRequestValidation tests input validation
func (suite *AIRequestServiceTestSuite) TestCreateRequestValidation() {
	// Test missing tenant ID
	input := CreateAIRequestInput{
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
	}

	result, err := suite.service.CreateRequest(suite.ctx, input)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "tenant_id is required")

	// Test missing model ID
	input = CreateAIRequestInput{
		TenantID:    1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
	}

	result, err = suite.service.CreateRequest(suite.ctx, input)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "model_id is required")

	// Test missing request type
	input = CreateAIRequestInput{
		TenantID:   1,
		ModelID:    1,
		PromptText: "Test prompt",
	}

	result, err = suite.service.CreateRequest(suite.ctx, input)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "request_type is required")

	// Test missing prompt text
	input = CreateAIRequestInput{
		TenantID:    1,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
	}

	result, err = suite.service.CreateRequest(suite.ctx, input)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "prompt_text is required")

	// Test prompt too long
	input = CreateAIRequestInput{
		TenantID:    1,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  string(make([]byte, 10001)),
	}

	result, err = suite.service.CreateRequest(suite.ctx, input)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "prompt_text must be less than 10000 characters")
}

// TestGetRequest tests retrieving an AI request
func (suite *AIRequestServiceTestSuite) TestGetRequest() {
	tenantID := uint(1)
	requestID := uint(1)

	// Mock repository access check
	suite.mockRepo.On("TenantHasAccess", mock.Anything, tenantID, requestID).Return(true, nil)

	// Mock repository get
	expectedRequest := &models.AIRequest{
		ID:          requestID,
		TenantID:    tenantID,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
		Status:      models.AIRequestStatusPending,
	}
	suite.mockRepo.On("GetByID", mock.Anything, requestID).Return(expectedRequest, nil)

	// Execute test
	result, err := suite.service.GetRequest(suite.ctx, tenantID, requestID)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedRequest, result)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetRequestAccessDenied tests access denied scenario
func (suite *AIRequestServiceTestSuite) TestGetRequestAccessDenied() {
	tenantID := uint(1)
	requestID := uint(1)

	// Mock repository access check to return false
	suite.mockRepo.On("TenantHasAccess", mock.Anything, tenantID, requestID).Return(false, nil)

	// Execute test
	result, err := suite.service.GetRequest(suite.ctx, tenantID, requestID)

	// Assertions
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "request not found or access denied")

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestProcessRequest tests processing an AI request
func (suite *AIRequestServiceTestSuite) TestProcessRequest() {
	requestID := uint(1)

	// Mock repository get
	request := &models.AIRequest{
		ID:          requestID,
		TenantID:    1,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
		Status:      models.AIRequestStatusPending,
		Metadata:    make(models.JSONMap),
	}
	suite.mockRepo.On("GetByID", mock.Anything, requestID).Return(request, nil)

	// Mock cost calculator
	suite.mockCostCalc.On("CalculateCost", mock.Anything, uint(1), mock.AnythingOfType("int")).Return(10, nil)

	// Mock repository update
	suite.mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*models.AIRequest")).Return(nil)

	// Mock logger
	suite.mockLogger.On("WithContext", mock.Anything).Return(suite.mockLogger)
	suite.mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

	// Execute test
	result, err := suite.service.ProcessRequest(suite.ctx, requestID)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), models.AIRequestStatusCompleted, result.Status)
	assert.NotNil(suite.T(), result.ResponseText)
	assert.Greater(suite.T(), result.TokensUsed, 0)
	assert.Greater(suite.T(), result.ProcessingTimeMs, 0)
	assert.Equal(suite.T(), 10, result.CostCents)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockCostCalc.AssertExpectations(suite.T())
}

// TestProcessRequestInvalidStatus tests processing request with invalid status
func (suite *AIRequestServiceTestSuite) TestProcessRequestInvalidStatus() {
	requestID := uint(1)

	// Mock repository get with completed status
	request := &models.AIRequest{
		ID:          requestID,
		TenantID:    1,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
		Status:      models.AIRequestStatusCompleted,
	}
	suite.mockRepo.On("GetByID", mock.Anything, requestID).Return(request, nil)

	// Execute test
	result, err := suite.service.ProcessRequest(suite.ctx, requestID)

	// Assertions
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "request cannot be processed in status")

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestUpdateRequest tests updating an AI request
func (suite *AIRequestServiceTestSuite) TestUpdateRequest() {
	tenantID := uint(1)
	requestID := uint(1)

	// Mock repository access check
	suite.mockRepo.On("TenantHasAccess", mock.Anything, tenantID, requestID).Return(true, nil)

	// Mock repository get
	request := &models.AIRequest{
		ID:          requestID,
		TenantID:    tenantID,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
		Status:      models.AIRequestStatusPending,
		Metadata:    make(models.JSONMap),
	}
	suite.mockRepo.On("GetByID", mock.Anything, requestID).Return(request, nil)

	// Mock repository update
	suite.mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*models.AIRequest")).Return(nil)

	// Mock logger
	suite.mockLogger.On("WithContext", mock.Anything).Return(suite.mockLogger)
	suite.mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

	// Update input
	updateInput := UpdateAIRequestInput{
		Status:     statusPtr(models.AIRequestStatusCompleted),
		TokensUsed: intPtr(100),
		CostCents:  intPtr(50),
	}

	// Execute test
	result, err := suite.service.UpdateRequest(suite.ctx, tenantID, requestID, updateInput)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), models.AIRequestStatusCompleted, result.Status)
	assert.Equal(suite.T(), 100, result.TokensUsed)
	assert.Equal(suite.T(), 50, result.CostCents)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestDeleteRequest tests deleting an AI request
func (suite *AIRequestServiceTestSuite) TestDeleteRequest() {
	tenantID := uint(1)
	requestID := uint(1)

	// Mock repository access check
	suite.mockRepo.On("TenantHasAccess", mock.Anything, tenantID, requestID).Return(true, nil)

	// Mock repository delete
	suite.mockRepo.On("Delete", mock.Anything, requestID).Return(nil)

	// Mock logger
	suite.mockLogger.On("WithContext", mock.Anything).Return(suite.mockLogger)
	suite.mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

	// Execute test
	err := suite.service.DeleteRequest(suite.ctx, tenantID, requestID)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestListRequests tests listing AI requests
func (suite *AIRequestServiceTestSuite) TestListRequests() {
	tenantID := uint(1)

	input := ListAIRequestsInput{
		TenantID:   tenantID,
		WebsiteID:  uintPtr(1),
		Status:     statusPtr(models.AIRequestStatusPending),
		Pagination: &pagination.CursorPagination{Limit: 10},
	}

	// Mock repository list
	expectedRequests := []models.AIRequest{
		{
			ID:        1,
			TenantID:  tenantID,
			WebsiteID: uintPtr(1),
			Status:    models.AIRequestStatusPending,
		},
		{
			ID:        2,
			TenantID:  tenantID,
			WebsiteID: uintPtr(1),
			Status:    models.AIRequestStatusPending,
		},
	}

	expectedResponse := &pagination.CursorResponse{
		HasNext:    false,
		NextCursor: "",
	}

	suite.mockRepo.On("List", mock.Anything, mock.AnythingOfType("models.AIRequestFilter"), input.Pagination).Return(expectedRequests, expectedResponse, nil)

	// Execute test
	result, result, err := suite.service.ListRequests(suite.ctx, input)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.NotNil(suite.T(), response)
	assert.Equal(suite.T(), expectedRequests, result)
	assert.Equal(suite.T(), expectedResponse, response)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetAnalytics tests getting analytics
func (suite *AIRequestServiceTestSuite) TestGetAnalytics() {
	tenantID := uint(1)
	startTime := time.Now().Add(-24 * time.Hour)
	endTime := time.Now()

	input := AnalyticsInput{
		TenantID:  tenantID,
		StartTime: startTime,
		EndTime:   endTime,
		Scope:     "tenant",
	}

	// Mock repository analytics
	expectedAnalytics := &models.AIRequestAnalytics{
		TotalRequests:     100,
		CompletedRequests: 90,
		FailedRequests:    10,
		TotalTokensUsed:   1000,
		TotalCostCents:    500,
		RequestsByType:    map[string]int64{"content_generation": 50, "chat": 50},
		RequestsByStatus:  map[string]int64{"completed": 90, "failed": 10},
	}

	suite.mockRepo.On("GetAnalytics", mock.Anything, tenantID, startTime, endTime).Return(expectedAnalytics, nil)

	// Execute test
	result, err := suite.service.GetAnalytics(suite.ctx, input)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedAnalytics, result)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestRetryFailedRequest tests retrying a failed request
func (suite *AIRequestServiceTestSuite) TestRetryFailedRequest() {
	requestID := uint(1)

	// Mock repository get with failed request
	request := &models.AIRequest{
		ID:          requestID,
		TenantID:    1,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
		Status:      models.AIRequestStatusFailed,
		Metadata:    make(models.JSONMap),
	}
	suite.mockRepo.On("GetByID", mock.Anything, requestID).Times(2).Return(request, nil)

	// Mock repository update for retry
	suite.mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*models.AIRequest")).Times(2).Return(nil)

	// Mock cost calculator
	suite.mockCostCalc.On("CalculateCost", mock.Anything, uint(1), mock.AnythingOfType("int")).Return(10, nil)

	// Mock logger
	suite.mockLogger.On("WithContext", mock.Anything).Return(suite.mockLogger)
	suite.mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

	// Execute test
	result, err := suite.service.RetryFailedRequest(suite.ctx, requestID)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), models.AIRequestStatusCompleted, result.Status)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockCostCalc.AssertExpectations(suite.T())
}

// TestCancelRequest tests canceling a pending request
func (suite *AIRequestServiceTestSuite) TestCancelRequest() {
	requestID := uint(1)

	// Mock repository get
	request := &models.AIRequest{
		ID:          requestID,
		TenantID:    1,
		ModelID:     1,
		RequestType: models.AIRequestTypeContentGeneration,
		PromptText:  "Test prompt",
		Status:      models.AIRequestStatusPending,
		Metadata:    make(models.JSONMap),
	}
	suite.mockRepo.On("GetByID", mock.Anything, requestID).Return(request, nil)

	// Mock repository update
	suite.mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*models.AIRequest")).Return(nil)

	// Mock logger
	suite.mockLogger.On("WithContext", mock.Anything).Return(suite.mockLogger)
	suite.mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

	// Execute test
	err := suite.service.CancelRequest(suite.ctx, requestID)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestBulkCreateRequests tests bulk creating AI requests
func (suite *AIRequestServiceTestSuite) TestBulkCreateRequests() {
	inputs := []CreateAIRequestInput{
		{
			TenantID:    1,
			ModelID:     1,
			RequestType: models.AIRequestTypeContentGeneration,
			PromptText:  "Test prompt 1",
		},
		{
			TenantID:    1,
			ModelID:     1,
			RequestType: models.AIRequestTypeContentGeneration,
			PromptText:  "Test prompt 2",
		},
	}

	// Mock dependencies for each request
	for i := 0; i < len(inputs); i++ {
		suite.mockRateLimiter.On("CheckLimit", mock.Anything, mock.Anything).Return(&RateLimitResult{
			Exceeded: false,
		}, nil)

		suite.mockSecurity.On("SanitizePrompt", mock.Anything, mock.AnythingOfType("string")).Return(inputs[i].PromptText, nil)

		suite.mockPIIDetector.On("DetectPII", mock.Anything, mock.AnythingOfType("string")).Return(PIIDetectionResult{
			HasPII: false,
		}, nil)

		suite.mockRepo.On("Create", mock.Anything, mock.AnythingOfType("*models.AIRequest")).Return(nil).Run(func(args mock.Arguments) {
			req := args.Get(1).(*models.AIRequest)
			req.ID = uint(i + 1)
		})

		suite.mockLogger.On("WithContext", mock.Anything).Return(suite.mockLogger)
		suite.mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()
	}

	// Execute test
	results, errors := suite.service.BulkCreateRequests(suite.ctx, inputs)

	// Assertions
	assert.Len(suite.T(), results, len(inputs))
	assert.Len(suite.T(), errors, len(inputs))

	for i, result := range results {
		assert.Equal(suite.T(), uint(i+1), result.ID)
		assert.NoError(suite.T(), errors[i])
	}

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockRateLimiter.AssertExpectations(suite.T())
	suite.mockSecurity.AssertExpectations(suite.T())
	suite.mockPIIDetector.AssertExpectations(suite.T())
}

// TestSearchRequests tests searching AI requests
func (suite *AIRequestServiceTestSuite) TestSearchRequests() {
	tenantID := uint(1)
	query := "test search"

	input := SearchAIRequestsInput{
		TenantID:   tenantID,
		Query:      query,
		Pagination: &pagination.CursorPagination{Limit: 10},
	}

	// Mock repository search
	expectedRequests := []models.AIRequest{
		{
			ID:         1,
			TenantID:   tenantID,
			PromptText: "test search prompt",
		},
	}

	expectedResponse := &pagination.CursorResponse{
		HasNext:    false,
		NextCursor: "",
	}

	suite.mockRepo.On("Search", mock.Anything, tenantID, query, input.Pagination).Return(expectedRequests, expectedResponse, nil)

	// Execute test
	result, result, err := suite.service.SearchRequests(suite.ctx, input)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.NotNil(suite.T(), response)
	assert.Equal(suite.T(), expectedRequests, result)
	assert.Equal(suite.T(), expectedResponse, response)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestCleanupOldRequests tests cleanup of old requests
func (suite *AIRequestServiceTestSuite) TestCleanupOldRequests() {
	tenantID := uint(1)
	olderThan := time.Now().Add(-30 * 24 * time.Hour)

	input := CleanupInput{
		TenantID:  tenantID,
		OlderThan: olderThan,
	}

	// Mock repository cleanup
	suite.mockRepo.On("DeleteOldRequests", mock.Anything, tenantID, olderThan).Return(int64(50), nil)

	// Mock logger
	suite.mockLogger.On("WithContext", mock.Anything).Return(suite.mockLogger)
	suite.mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

	// Execute test
	result, err := suite.service.CleanupOldRequests(suite.ctx, input)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), tenantID, result.TenantID)
	assert.Equal(suite.T(), int64(50), result.DeletedCount)

	// Verify mocks
	suite.mockRepo.AssertExpectations(suite.T())
}

// Run the test suite
func TestAIRequestServiceTestSuite(t *testing.T) {
	suite.Run(t, new(AIRequestServiceTestSuite))
}

// Mock implementations

// MockAIRequestRepository mocks the AI request repository
type MockAIRequestRepository struct {
	mock.Mock
}

func (m *MockAIRequestRepository) Create(ctx context.Context, request *models.AIRequest) error {
	args := m.Called(ctx, request)
	return args.Error(0)
}

func (m *MockAIRequestRepository) GetByID(ctx context.Context, id uint) (*models.AIRequest, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.AIRequest), args.Error(1)
}

func (m *MockAIRequestRepository) GetByIDWithRelations(ctx context.Context, id uint) (*models.AIRequest, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.AIRequest), args.Error(1)
}

func (m *MockAIRequestRepository) Update(ctx context.Context, request *models.AIRequest) error {
	args := m.Called(ctx, request)
	return args.Error(0)
}

func (m *MockAIRequestRepository) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockAIRequestRepository) List(ctx context.Context, filter models.AIRequestFilter, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	args := m.Called(ctx, filter, pagination)
	return args.Get(0).([]models.AIRequest), args.Get(1).(*pagination.CursorResponse), args.Error(2)
}

func (m *MockAIRequestRepository) ListWithRelations(ctx context.Context, filter models.AIRequestFilter, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	args := m.Called(ctx, filter, pagination)
	return args.Get(0).([]models.AIRequest), args.Get(1).(*pagination.CursorResponse), args.Error(2)
}

func (m *MockAIRequestRepository) GetAnalytics(ctx context.Context, tenantID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error) {
	args := m.Called(ctx, tenantID, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.AIRequestAnalytics), args.Error(1)
}

func (m *MockAIRequestRepository) GetAnalyticsByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error) {
	args := m.Called(ctx, websiteID, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.AIRequestAnalytics), args.Error(1)
}

func (m *MockAIRequestRepository) GetAnalyticsByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.AIRequestAnalytics), args.Error(1)
}

func (m *MockAIRequestRepository) GetAnalyticsByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error) {
	args := m.Called(ctx, modelID, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.AIRequestAnalytics), args.Error(1)
}

func (m *MockAIRequestRepository) TenantHasAccess(ctx context.Context, tenantID uint, requestID uint) (bool, error) {
	args := m.Called(ctx, tenantID, requestID)
	return args.Bool(0), args.Error(1)
}

func (m *MockAIRequestRepository) Search(ctx context.Context, tenantID uint, query string, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	args := m.Called(ctx, tenantID, query, pagination)
	return args.Get(0).([]models.AIRequest), args.Get(1).(*pagination.CursorResponse), args.Error(2)
}

func (m *MockAIRequestRepository) DeleteOldRequests(ctx context.Context, tenantID uint, olderThan time.Time) (int64, error) {
	args := m.Called(ctx, tenantID, olderThan)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockAIRequestRepository) BulkUpdateStatus(ctx context.Context, ids []uint, status models.AIRequestStatus) error {
	args := m.Called(ctx, ids, status)
	return args.Error(0)
}

// Add other required methods as stubs for completeness
func (m *MockAIRequestRepository) GetByStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) UpdateStatus(ctx context.Context, id uint, status models.AIRequestStatus) error {
	return nil
}

func (m *MockAIRequestRepository) GetByTenant(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByTenantAndType(ctx context.Context, tenantID uint, requestType models.AIRequestType, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByTenantAndStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByWebsite(ctx context.Context, websiteID uint, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByWebsiteAndType(ctx context.Context, websiteID uint, requestType models.AIRequestType, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByWebsiteAndStatus(ctx context.Context, websiteID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByUser(ctx context.Context, userID uint, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByUserAndType(ctx context.Context, userID uint, requestType models.AIRequestType, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByUserAndStatus(ctx context.Context, userID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByModel(ctx context.Context, modelID uint, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByModelAndType(ctx context.Context, modelID uint, requestType models.AIRequestType, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByModelAndStatus(ctx context.Context, modelID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetByTimeRange(ctx context.Context, tenantID uint, startTime, endTime time.Time, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) GetRecentRequests(ctx context.Context, tenantID uint, hours int, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) Count(ctx context.Context, filter models.AIRequestFilter) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) CountByWebsite(ctx context.Context, websiteID uint) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) CountByUser(ctx context.Context, userID uint) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) CountByModel(ctx context.Context, modelID uint) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) CountByStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) CountByType(ctx context.Context, tenantID uint, requestType models.AIRequestType) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) CountByTimeRange(ctx context.Context, tenantID uint, startTime, endTime time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetTokenUsage(ctx context.Context, tenantID uint, startTime, endTime time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetTokenUsageByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetTokenUsageByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetTokenUsageByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetCostUsage(ctx context.Context, tenantID uint, startTime, endTime time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetCostUsageByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetCostUsageByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetCostUsageByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetAverageProcessingTime(ctx context.Context, tenantID uint, startTime, endTime time.Time) (float64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetAverageProcessingTimeByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (float64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetAverageProcessingTimeByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (float64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetAverageProcessingTimeByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (float64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetErrorRate(ctx context.Context, tenantID uint, startTime, endTime time.Time) (float64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetErrorRateByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (float64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetErrorRateByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (float64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetErrorRateByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (float64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) BulkCreate(ctx context.Context, requests []models.AIRequest) error {
	return nil
}

func (m *MockAIRequestRepository) BulkUpdate(ctx context.Context, requests []models.AIRequest) error {
	return nil
}

func (m *MockAIRequestRepository) BulkDelete(ctx context.Context, ids []uint) error {
	return nil
}

func (m *MockAIRequestRepository) DeleteRequestsByStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus, olderThan time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) SearchByPrompt(ctx context.Context, tenantID uint, prompt string, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) SearchByResponse(ctx context.Context, tenantID uint, response string, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	return nil, nil, nil
}

func (m *MockAIRequestRepository) RequestExists(ctx context.Context, tenantID uint, id uint) (bool, error) {
	return false, nil
}

func (m *MockAIRequestRepository) UserHasAccess(ctx context.Context, userID uint, requestID uint) (bool, error) {
	return false, nil
}

func (m *MockAIRequestRepository) WebsiteHasAccess(ctx context.Context, websiteID uint, requestID uint) (bool, error) {
	return false, nil
}

func (m *MockAIRequestRepository) GetPendingRequests(ctx context.Context, limit int) ([]models.AIRequest, error) {
	return nil, nil
}

func (m *MockAIRequestRepository) GetTimeoutRequests(ctx context.Context, timeoutMinutes int) ([]models.AIRequest, error) {
	return nil, nil
}

func (m *MockAIRequestRepository) GetRetryableRequests(ctx context.Context, maxRetries int) ([]models.AIRequest, error) {
	return nil, nil
}

func (m *MockAIRequestRepository) GetRequestCountInTimeWindow(ctx context.Context, tenantID uint, websiteID *uint, userID *uint, windowStart time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetTokenUsageInTimeWindow(ctx context.Context, tenantID uint, websiteID *uint, userID *uint, windowStart time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetCostUsageInTimeWindow(ctx context.Context, tenantID uint, websiteID *uint, userID *uint, windowStart time.Time) (int64, error) {
	return 0, nil
}

func (m *MockAIRequestRepository) GetRequestsBatch(ctx context.Context, tenantID uint, status models.AIRequestStatus, batchSize int, offset int) ([]models.AIRequest, error) {
	return nil, nil
}

func (m *MockAIRequestRepository) ProcessBatch(ctx context.Context, processor func([]models.AIRequest) error, batchSize int, tenantID uint, status models.AIRequestStatus) error {
	return nil
}

// MockRateLimiter mocks the rate limiter interface
type MockRateLimiter struct {
	mock.Mock
}

func (m *MockRateLimiter) CheckLimit(ctx context.Context, input RateLimitInput) (*RateLimitResult, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*RateLimitResult), args.Error(1)
}

func (m *MockRateLimiter) GetLimitInfo(ctx context.Context, input RateLimitInput) (*RateLimitInfo, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*RateLimitInfo), args.Error(1)
}

// MockPIIDetector mocks the PII detector interface
type MockPIIDetector struct {
	mock.Mock
}

func (m *MockPIIDetector) DetectPII(ctx context.Context, text string) (PIIDetectionResult, error) {
	args := m.Called(ctx, text)
	return args.Get(0).(PIIDetectionResult), args.Error(1)
}

// MockCostCalculator mocks the cost calculator interface
type MockCostCalculator struct {
	mock.Mock
}

func (m *MockCostCalculator) CalculateCost(ctx context.Context, modelID uint, tokensUsed int) (int, error) {
	args := m.Called(ctx, modelID, tokensUsed)
	return args.Int(0), args.Error(1)
}

// MockSecurityService mocks the security service interface
type MockSecurityService struct {
	mock.Mock
}

func (m *MockSecurityService) ValidateAccess(ctx context.Context, userID uint, requestID uint) error {
	args := m.Called(ctx, userID, requestID)
	return args.Error(0)
}

func (m *MockSecurityService) SanitizePrompt(ctx context.Context, prompt string) (string, error) {
	args := m.Called(ctx, prompt)
	return args.String(0), args.Error(1)
}

// MockQueueService mocks the queue service interface
type MockQueueService struct {
	mock.Mock
}

func (m *MockQueueService) QueueRequest(ctx context.Context, requestID uint) error {
	args := m.Called(ctx, requestID)
	return args.Error(0)
}

func (m *MockQueueService) GetQueueStatus(ctx context.Context, tenantID uint) (*QueueStatus, error) {
	args := m.Called(ctx, tenantID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*QueueStatus), args.Error(1)
}

// Helper functions for test pointers
func uintPtr(u uint) *uint {
	return &u
}

func intPtr(i int) *int {
	return &i
}

func statusPtr(s models.AIRequestStatus) *models.AIRequestStatus {
	return &s
}
