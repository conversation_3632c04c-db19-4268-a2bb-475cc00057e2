package repositories

import (
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

type NotificationRepository interface {
	Create(notification *models.Notification) error
	GetByID(tenantID, id uint) (*models.Notification, error)
	GetWithRelations(tenantID, id uint) (*models.Notification, error)
	List(tenantID uint, filters models.NotificationFilters) ([]models.Notification, int64, error)
	ListWithCursor(tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.Notification, *pagination.CursorResponse, error)
	ListByUserID(tenantID, userID uint, filters models.NotificationFilters) ([]models.Notification, int64, error)
	ListByUserIDWithCursor(tenantID, userID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.Notification, *pagination.CursorResponse, error)
	Update(notification *models.Notification) error
	Delete(tenantID, id uint) error
	GetPending(tenantID uint, limit int) ([]models.Notification, error)
	GetScheduledForProcessing(before time.Time, limit int) ([]models.Notification, error)
	GetRetryable(tenantID uint, limit int) ([]models.Notification, error)
	UpdateStatus(tenantID, id uint, status models.NotificationStatus) error
	BulkUpdateStatus(tenantID uint, ids []uint, status models.NotificationStatus) error
	GetStatistics(tenantID uint, dateFrom, dateTo *time.Time) (*NotificationStatistics, error)
}

type notificationRepository struct {
	db *gorm.DB
}

type NotificationStatistics struct {
	Total     int64 `json:"total"`
	Pending   int64 `json:"pending"`
	Queued    int64 `json:"queued"`
	Sent      int64 `json:"sent"`
	Delivered int64 `json:"delivered"`
	Failed    int64 `json:"failed"`
	Cancelled int64 `json:"cancelled"`
}

func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &notificationRepository{db: db}
}

func (r *notificationRepository) Create(notification *models.Notification) error {
	return r.db.Create(notification).Error
}

func (r *notificationRepository) GetByID(tenantID, id uint) (*models.Notification, error) {
	var notification models.Notification
	err := r.db.Where("tenant_id = ? AND id = ?", tenantID, id).First(&notification).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

func (r *notificationRepository) GetWithRelations(tenantID, id uint) (*models.Notification, error) {
	var notification models.Notification
	err := r.db.
		Preload("Template").
		Preload("Template.ActiveVersion").
		Preload("Recipients").
		Preload("Logs").
		Where("tenant_id = ? AND id = ?", tenantID, id).
		First(&notification).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

func (r *notificationRepository) List(tenantID uint, filters models.NotificationFilters) ([]models.Notification, int64, error) {
	query := r.db.Where("tenant_id = ?", tenantID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Channel != "" {
		query = query.Where("channel = ?", filters.Channel)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.Priority != "" {
		query = query.Where("priority = ?", filters.Priority)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", filters.DateTo)
	}

	// Count total
	var total int64
	if err := query.Model(&models.Notification{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	offset := (filters.Page - 1) * filters.Limit
	orderBy := fmt.Sprintf("%s %s", filters.SortBy, filters.SortOrder)

	var notifications []models.Notification
	err := query.
		Preload("Template").
		Offset(offset).
		Limit(filters.Limit).
		Order(orderBy).
		Find(&notifications).Error

	return notifications, total, err
}

// ListWithCursor retrieves notifications using cursor-based pagination
func (r *notificationRepository) ListWithCursor(tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.Notification, *pagination.CursorResponse, error) {
	var notifications []models.Notification

	// Build base query
	query := r.db.Where("tenant_id = ?", tenantID)

	// Apply filters
	if typeVal, ok := filters["type"]; ok && typeVal != "" {
		query = query.Where("type = ?", typeVal)
	}
	if channel, ok := filters["channel"]; ok && channel != "" {
		query = query.Where("channel = ?", channel)
	}
	if status, ok := filters["status"]; ok && status != "" {
		query = query.Where("status = ?", status)
	}
	if priority, ok := filters["priority"]; ok && priority != "" {
		query = query.Where("priority = ?", priority)
	}
	if dateFrom, ok := filters["date_from"]; ok && dateFrom != nil {
		query = query.Where("created_at >= ?", dateFrom)
	}
	if dateTo, ok := filters["date_to"]; ok && dateTo != nil {
		query = query.Where("created_at <= ?", dateTo)
	}

	// Apply cursor
	if req.Cursor != "" {
		cursor, err := pagination.ParseCursor(req.Cursor)
		if err == nil {
			query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))",
				cursor.CreatedAt, cursor.CreatedAt, cursor.ID)
		}
	}

	// Apply sorting and limit
	sortBy := "created_at"
	sortOrder := "DESC"
	if sortByFilter, ok := filters["sort_by"]; ok && sortByFilter != "" {
		sortBy = sortByFilter.(string)
	}
	if sortOrderFilter, ok := filters["sort_order"]; ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter.(string)
	}

	query = query.Order(fmt.Sprintf("%s %s, id %s", sortBy, sortOrder, sortOrder))

	// Get one extra record to check if there are more
	limit := pagination.ValidateLimit(req.Limit)
	query = query.Limit(limit + 1)

	err := query.
		Preload("Template").
		Find(&notifications).Error
	if err != nil {
		return nil, nil, err
	}

	// Build cursor response
	hasNext := len(notifications) > limit
	if hasNext {
		notifications = notifications[:limit]
	}

	response := &pagination.CursorResponse{
		HasNext: hasNext,
		Count:   len(notifications),
		Limit:   limit,
	}

	// Generate next cursor if there are more records
	if hasNext && len(notifications) > 0 {
		last := notifications[len(notifications)-1]
		response.NextCursor, _ = pagination.EncodeCursor(last.ID, last.CreatedAt)
	}

	return notifications, response, nil
}

func (r *notificationRepository) ListByUserID(tenantID, userID uint, filters models.NotificationFilters) ([]models.Notification, int64, error) {
	query := r.db.
		Table("notifications n").
		Joins("INNER JOIN notification_recipients nr ON n.id = nr.notification_id").
		Where("n.tenant_id = ? AND nr.user_id = ?", tenantID, userID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("n.type = ?", filters.Type)
	}
	if filters.Channel != "" {
		query = query.Where("n.channel = ?", filters.Channel)
	}
	if filters.Status != "" {
		query = query.Where("n.status = ?", filters.Status)
	}
	if filters.Priority != "" {
		query = query.Where("n.priority = ?", filters.Priority)
	}
	if filters.DateFrom != nil {
		query = query.Where("n.created_at >= ?", filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("n.created_at <= ?", filters.DateTo)
	}

	// Count total
	var total int64
	if err := query.Select("DISTINCT n.id").Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	offset := (filters.Page - 1) * filters.Limit
	orderBy := fmt.Sprintf("n.%s %s", filters.SortBy, filters.SortOrder)

	var notifications []models.Notification
	err := query.
		Select("DISTINCT n.*").
		Preload("Template").
		Preload("Recipients", "user_id = ?", userID).
		Offset(offset).
		Limit(filters.Limit).
		Order(orderBy).
		Find(&notifications).Error

	return notifications, total, err
}

// ListByUserIDWithCursor retrieves user notifications using cursor-based pagination
func (r *notificationRepository) ListByUserIDWithCursor(tenantID, userID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.Notification, *pagination.CursorResponse, error) {
	var notifications []models.Notification

	// Build base query
	query := r.db.
		Table("notifications n").
		Joins("INNER JOIN notification_recipients nr ON n.id = nr.notification_id").
		Where("n.tenant_id = ? AND nr.user_id = ?", tenantID, userID)

	// Apply filters
	if typeVal, ok := filters["type"]; ok && typeVal != "" {
		query = query.Where("n.type = ?", typeVal)
	}
	if channel, ok := filters["channel"]; ok && channel != "" {
		query = query.Where("n.channel = ?", channel)
	}
	if status, ok := filters["status"]; ok && status != "" {
		query = query.Where("n.status = ?", status)
	}
	if priority, ok := filters["priority"]; ok && priority != "" {
		query = query.Where("n.priority = ?", priority)
	}
	if dateFrom, ok := filters["date_from"]; ok && dateFrom != nil {
		query = query.Where("n.created_at >= ?", dateFrom)
	}
	if dateTo, ok := filters["date_to"]; ok && dateTo != nil {
		query = query.Where("n.created_at <= ?", dateTo)
	}

	// Apply cursor
	if req.Cursor != "" {
		cursor, err := pagination.ParseCursor(req.Cursor)
		if err == nil {
			query = query.Where("(n.created_at < ? OR (n.created_at = ? AND n.id < ?))",
				cursor.CreatedAt, cursor.CreatedAt, cursor.ID)
		}
	}

	// Apply sorting and limit
	sortBy := "n.created_at"
	sortOrder := "DESC"
	if sortByFilter, ok := filters["sort_by"]; ok && sortByFilter != "" {
		sortBy = fmt.Sprintf("n.%s", sortByFilter.(string))
	}
	if sortOrderFilter, ok := filters["sort_order"]; ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter.(string)
	}

	query = query.Order(fmt.Sprintf("%s %s, n.id %s", sortBy, sortOrder, sortOrder))

	// Get one extra record to check if there are more
	limit := pagination.ValidateLimit(req.Limit)
	query = query.Limit(limit + 1)

	err := query.
		Select("DISTINCT n.*").
		Preload("Template").
		Preload("Recipients", "user_id = ?", userID).
		Find(&notifications).Error
	if err != nil {
		return nil, nil, err
	}

	// Build cursor response
	hasNext := len(notifications) > limit
	if hasNext {
		notifications = notifications[:limit]
	}

	var nextCursor string
	if hasNext && len(notifications) > 0 {
		lastNotification := notifications[len(notifications)-1]
		nextCursor, _ = pagination.EncodeCursor(lastNotification.ID, lastNotification.CreatedAt)
	}

	cursorResponse := &pagination.CursorResponse{
		HasNext:    hasNext,
		NextCursor: nextCursor,
		HasMore:    hasNext,
		Count:      len(notifications),
		Limit:      limit,
	}

	return notifications, cursorResponse, nil
}

func (r *notificationRepository) Update(notification *models.Notification) error {
	return r.db.Save(notification).Error
}

func (r *notificationRepository) Delete(tenantID, id uint) error {
	return r.db.Where("tenant_id = ? AND id = ?", tenantID, id).Delete(&models.Notification{}).Error
}

func (r *notificationRepository) GetPending(tenantID uint, limit int) ([]models.Notification, error) {
	var notifications []models.Notification
	err := r.db.
		Where("tenant_id = ? AND status = ?", tenantID, models.StatusPending).
		Where("(scheduled_at IS NULL OR scheduled_at <= ?)", time.Now()).
		Preload("Template").
		Preload("Recipients").
		Order("priority DESC, created_at ASC").
		Limit(limit).
		Find(&notifications).Error
	return notifications, err
}

func (r *notificationRepository) GetScheduledForProcessing(before time.Time, limit int) ([]models.Notification, error) {
	var notifications []models.Notification
	err := r.db.
		Where("status = ? AND scheduled_at IS NOT NULL AND scheduled_at <= ?",
			models.StatusPending, before).
		Preload("Template").
		Preload("Recipients").
		Order("scheduled_at ASC").
		Limit(limit).
		Find(&notifications).Error
	return notifications, err
}

func (r *notificationRepository) GetRetryable(tenantID uint, limit int) ([]models.Notification, error) {
	var notifications []models.Notification
	err := r.db.
		Where("tenant_id = ? AND status = ? AND retry_count < max_retries",
			tenantID, models.StatusFailed).
		Preload("Template").
		Preload("Recipients").
		Order("priority DESC, failed_at ASC").
		Limit(limit).
		Find(&notifications).Error
	return notifications, err
}

func (r *notificationRepository) UpdateStatus(tenantID, id uint, status models.NotificationStatus) error {
	updates := map[string]interface{}{
		"status": status,
	}

	switch status {
	case models.StatusSent:
		updates["sent_at"] = time.Now()
	case models.StatusDelivered:
		updates["delivered_at"] = time.Now()
	case models.StatusFailed:
		updates["failed_at"] = time.Now()
	}

	return r.db.Model(&models.Notification{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(updates).Error
}

func (r *notificationRepository) BulkUpdateStatus(tenantID uint, ids []uint, status models.NotificationStatus) error {
	if len(ids) == 0 {
		return nil
	}

	updates := map[string]interface{}{
		"status": status,
	}

	switch status {
	case models.StatusSent:
		updates["sent_at"] = time.Now()
	case models.StatusDelivered:
		updates["delivered_at"] = time.Now()
	case models.StatusFailed:
		updates["failed_at"] = time.Now()
	}

	return r.db.Model(&models.Notification{}).
		Where("tenant_id = ? AND id IN ?", tenantID, ids).
		Updates(updates).Error
}

func (r *notificationRepository) GetStatistics(tenantID uint, dateFrom, dateTo *time.Time) (*NotificationStatistics, error) {
	query := r.db.Model(&models.Notification{}).Where("tenant_id = ?", tenantID)

	if dateFrom != nil {
		query = query.Where("created_at >= ?", dateFrom)
	}
	if dateTo != nil {
		query = query.Where("created_at <= ?", dateTo)
	}

	stats := &NotificationStatistics{}

	// Get total count
	if err := query.Count(&stats.Total).Error; err != nil {
		return nil, err
	}

	// Get counts by status
	var statusCounts []struct {
		Status string
		Count  int64
	}

	err := query.
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusCounts).Error
	if err != nil {
		return nil, err
	}

	// Map status counts
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(models.StatusPending):
			stats.Pending = sc.Count
		case string(models.StatusQueued):
			stats.Queued = sc.Count
		case string(models.StatusSent):
			stats.Sent = sc.Count
		case string(models.StatusDelivered):
			stats.Delivered = sc.Count
		case string(models.StatusFailed):
			stats.Failed = sc.Count
		case string(models.StatusCancelled):
			stats.Cancelled = sc.Count
		}
	}

	return stats, nil
}
