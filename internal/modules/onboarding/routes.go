package onboarding

import (
	"time"

	"github.com/gin-gonic/gin"
	govalidator "github.com/go-playground/validator/v10"
	"github.com/tranthanhloi/wn-api-v3/internal/config"
	authMysql "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	authServices "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/handlers"
	onboardingrepos "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories"
	onboardingmysql "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	tenantRepos "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	tenantServices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	userRepos "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories/mysql"
	userServices "github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	websiteMysql "github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories/mysql"
	websiteServices "github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all onboarding module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger) {
	// Initialize validator
	validatorInstance := govalidator.New()
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.WithError(err).Error("Failed to load config")
		return
	}

	// Initialize repositories
	progressRepo := onboardingmysql.NewOnboardingProgressRepository(db, logger)
	tenantRepo := tenantRepos.NewMySQLTenantRepository(db)
	planRepo := tenantRepos.NewMySQLTenantPlanRepository(db)
	tenantMembershipRepo := userRepos.NewTenantMembershipRepository(db, logger)
	websiteRepo := websiteMysql.NewWebsiteRepository(db)

	// Initialize JWT service dependencies
	tokenBlacklistRepo := authMysql.NewTokenBlacklistRepository(db)
	tokenBlacklistService := authServices.NewTokenBlacklistService(tokenBlacklistRepo, nil)

	// Create JWT service
	jwtService, err := authServices.NewJWTService(cfg, tokenBlacklistService, tenantMembershipRepo)
	if err != nil {
		logger.WithError(err).Error("Failed to create JWT service")
		return
	}

	// Initialize services
	onboardingService := services.NewOnboardingService(
		progressRepo,
		logger,
		&services.OnboardingConfig{
			AutoStart:           true,
			RequiredSteps:       []string{"create_tenant", "create_website"},
			OptionalSteps:       []string{},
			DefaultTimeout:      7 * 24 * time.Hour, // 7 days
			AllowSkip:           false,
			EnableNotifications: true,
		},
	)
	tenantService := tenantServices.NewTenantService(tenantRepo, planRepo)
	tenantMembershipService := userServices.NewTenantMembershipService(tenantMembershipRepo, logger)

	// Initialize website service with proper dependencies
	baseDomain := utils.GetEnv("BASE_DOMAIN", "example.com")
	domainService := websiteServices.NewDomainService(websiteRepo, baseDomain)
	websiteService := websiteServices.NewWebsiteService(websiteRepo, domainService, validatorInstance)

	// Initialize handlers
	onboardingHandler := handlers.NewOnboardingHandler(
		onboardingService,
		tenantService,
		tenantMembershipService,
		websiteService,
		v,
		logger,
	)

	// Create onboarding routes group
	onboardingRoutes := router.Group("/onboarding")
	onboardingRoutes.Use(httpmiddleware.EnhancedCORSMiddleware())
	onboardingRoutes.Use(httpmiddleware.SecurityHeadersMiddleware())
	onboardingRoutes.Use(httpmiddleware.AuthRateLimitingMiddleware(logger))
	onboardingRoutes.Use(httpmiddleware.ValidateJSONContentType())
	onboardingRoutes.Use(httpmiddleware.TenantContextMiddleware())
	onboardingRoutes.Use(httpmiddleware.RequestLoggingMiddleware(logger))

	// Public onboarding endpoints (require authentication but not specific roles)
	publicRoutes := onboardingRoutes.Group("")
	publicRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	publicRoutes.Use(httpmiddleware.RequireAuthentication())
	{
		// Organization management
		publicRoutes.POST("/create-organization", httpmiddleware.RequirePermission("onboarding.create"), onboardingHandler.CreateOrganization)

		// Personal endpoints - no RBAC needed (authentication only)
		publicRoutes.GET("/me/organization", onboardingHandler.GetMyOrganization)
		publicRoutes.POST("/me/organization", onboardingHandler.CreateMyOrganization)
		publicRoutes.GET("/me/website", onboardingHandler.GetMyWebsite)
		publicRoutes.POST("/me/website", onboardingHandler.CreateMyWebsite)
		publicRoutes.PUT("/me/step", onboardingHandler.UpdateStep)
		publicRoutes.GET("/me/step", onboardingHandler.GetCurrentStep)
		publicRoutes.POST("/me/complete", onboardingHandler.CompleteOnboarding)
	}

	// Admin onboarding endpoints (require admin role)
	adminRoutes := onboardingRoutes.Group("/admin")
	adminRoutes.Use(httpmiddleware.RequireAdminRole())
	{
		// Administrative endpoints
		adminRoutes.GET("/progress", httpmiddleware.RequirePermission("onboarding.manage-steps"), onboardingHandler.ListProgress)
		adminRoutes.GET("/stats", httpmiddleware.RequirePermission("onboarding.manage-steps"), onboardingHandler.GetStats)
	}

	// Health check for onboarding module
	onboardingRoutes.GET("/health", func(c *gin.Context) {
		response.Success(c.Writer, gin.H{
			"module":  "onboarding",
			"status":  "healthy",
			"version": "1.0.0",
			"checks": gin.H{
				"database":           db != nil,
				"progress_repo":      progressRepo != nil,
				"onboarding_service": onboardingService != nil,
			},
		})
	})
}

// GetOnboardingService creates and returns an onboarding service instance
// This function can be used by other modules that need to interact with onboarding
func GetOnboardingService(db *gorm.DB, logger utils.Logger) services.OnboardingService {
	progressRepo := onboardingmysql.NewOnboardingProgressRepository(db, logger)

	return services.NewOnboardingService(
		progressRepo,
		logger,
		&services.OnboardingConfig{
			AutoStart:           true,
			RequiredSteps:       []string{"create_tenant", "create_website"},
			OptionalSteps:       []string{},
			DefaultTimeout:      7 * 24 * time.Hour, // 7 days
			AllowSkip:           false,
			EnableNotifications: true,
		},
	)
}

// GetOnboardingRepository creates and returns an onboarding progress repository instance
// This function can be used by other modules that need direct repository access
func GetOnboardingRepository(db *gorm.DB, logger utils.Logger) onboardingrepos.OnboardingProgressRepository {
	return onboardingmysql.NewOnboardingProgressRepository(db, logger)
}
