package dto

import "time"

// GetOnboardingProgressRequest represents the request payload for getting onboarding progress
// Note: UserID is obtained from authentication context, not from request
type GetOnboardingProgressRequest struct {
	// UserID is set internally from JWT context
}

// GetOnboardingProgressResponse represents the response payload for getting onboarding progress
type GetOnboardingProgressResponse struct {
	ID          uint                   `json:"id"`
	UserID      uint                   `json:"user_id"`
	Status      string                 `json:"status"`
	Step        string                 `json:"step"`
	StartedAt   *time.Time             `json:"started_at,omitempty"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// StartOnboardingRequest represents the request payload for starting onboarding
// Note: UserID is obtained from authentication context, not from request
type StartOnboardingRequest struct {
	// UserID is set internally from JWT context
}

// StartOnboardingResponse represents the response payload for starting onboarding
type StartOnboardingResponse struct {
	ID        uint      `json:"id"`
	UserID    uint      `json:"user_id"`
	Status    string    `json:"status"`
	Step      string    `json:"step"`
	StartedAt time.Time `json:"started_at"`
	Message   string    `json:"message"`
}

// UpdateOnboardingStepRequest represents the request payload for updating onboarding step
type UpdateOnboardingStepRequest struct {
	UserID   uint                   `json:"-"` // Set internally from JWT context
	Step     string                 `json:"step" validate:"required,oneof=create_tenant create_website completed" example:"create_website"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateOnboardingStepResponse represents the response payload for updating onboarding step
type UpdateOnboardingStepResponse struct {
	ID          uint                   `json:"id"`
	UserID      uint                   `json:"user_id"`
	Status      string                 `json:"status"`
	Step        string                 `json:"step"`
	StartedAt   *time.Time             `json:"started_at,omitempty"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Message     string                 `json:"message"`
}

// CompleteOnboardingRequest represents the request payload for completing onboarding
type CompleteOnboardingRequest struct {
	UserID   uint                   `json:"-"` // Set internally from JWT context
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// CompleteOnboardingResponse represents the response payload for completing onboarding
type CompleteOnboardingResponse struct {
	ID          uint                   `json:"id"`
	UserID      uint                   `json:"user_id"`
	Status      string                 `json:"status"`
	Step        string                 `json:"step"`
	StartedAt   *time.Time             `json:"started_at,omitempty"`
	CompletedAt time.Time              `json:"completed_at"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Message     string                 `json:"message"`
}

// ResetOnboardingRequest represents the request payload for resetting onboarding
// Note: UserID is obtained from authentication context, not from request
type ResetOnboardingRequest struct {
	// UserID is set internally from JWT context
}

// ResetOnboardingResponse represents the response payload for resetting onboarding
type ResetOnboardingResponse struct {
	ID      uint   `json:"id"`
	UserID  uint   `json:"user_id"`
	Status  string `json:"status"`
	Step    string `json:"step"`
	Message string `json:"message"`
}

// OnboardingStatsResponse represents the response payload for onboarding statistics
type OnboardingStatsResponse struct {
	TotalUsers      uint    `json:"total_users"`
	PendingUsers    uint    `json:"pending_users"`
	ProcessingUsers uint    `json:"processing_users"`
	CompletedUsers  uint    `json:"completed_users"`
	CompletionRate  float64 `json:"completion_rate"`
}

// ListOnboardingProgressRequest represents the request payload for listing onboarding progress
type ListOnboardingProgressRequest struct {
	Status string `json:"status,omitempty" validate:"omitempty,oneof=pending processing completed" example:"pending"`
	Step   string `json:"step,omitempty" validate:"omitempty,oneof=create_tenant create_website completed" example:"create_tenant"`
	Limit  int    `json:"limit,omitempty" validate:"omitempty,min=1,max=100" example:"10"`
	Offset int    `json:"offset,omitempty" validate:"omitempty,min=0" example:"0"`
}

// ListOnboardingProgressResponse represents the response payload for listing onboarding progress
type ListOnboardingProgressResponse struct {
	Progress []GetOnboardingProgressResponse `json:"progress"`
	Total    int64                           `json:"total"`
	Limit    int                             `json:"limit"`
	Offset   int                             `json:"offset"`
}

// CreateOrganizationRequest represents the request payload for creating organization during onboarding
type CreateOrganizationRequest struct {
	UserID         uint   `json:"-"` // Set internally from JWT context
	Name           string `json:"name" validate:"required,min=3,max=100" example:"My Company"`
	Domain         string `json:"domain" validate:"required,min=3,max=100,alphanum" example:"mycompany"`
	ContactEmail   string `json:"contact_email" validate:"required,email" example:"<EMAIL>"`
	ContactPhone   string `json:"contact_phone" validate:"omitempty,e164" example:"+1234567890"`
	CompanyName    string `json:"company_name" validate:"omitempty,max=200" example:"My Company Inc."`
	CompanyAddress string `json:"company_address" validate:"omitempty,max=500" example:"123 Business St, City, State"`
	PlanID         uint   `json:"plan_id" validate:"omitempty" example:"1"`
}

// CreateOrganizationResponse represents the response payload for creating organization
type CreateOrganizationResponse struct {
	TenantID     uint   `json:"tenant_id"`
	Name         string `json:"name"`
	Domain       string `json:"domain"`
	Status       string `json:"status"`
	ContactEmail string `json:"contact_email"`
	CompanyName  string `json:"company_name,omitempty"`
	Message      string `json:"message"`
}

// GetMyOrganizationResponse represents the response payload for getting current user's organization
type GetMyOrganizationResponse struct {
	TenantID       uint   `json:"tenant_id"`
	Name           string `json:"name"`
	Domain         string `json:"domain"`
	Status         string `json:"status"`
	ContactEmail   string `json:"contact_email"`
	ContactPhone   string `json:"contact_phone,omitempty"`
	CompanyName    string `json:"company_name,omitempty"`
	CompanyAddress string `json:"company_address,omitempty"`
	LogoURL        string `json:"logo_url,omitempty"`
	IsPrimary      bool   `json:"is_primary"`
}

// CreateWebsiteRequest represents the request payload for creating website during onboarding
type CreateWebsiteRequest struct {
	UserID    uint   `json:"-"` // Set internally from JWT context
	SubDomain string `json:"subdomain" validate:"required,min=2,max=100,slug" example:"my-website"`
}

// CreateWebsiteResponse represents the response payload for creating website
type CreateWebsiteResponse struct {
	WebsiteID   uint   `json:"website_id"`
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	Domain      string `json:"domain,omitempty"`
	Status      string `json:"status"`
	Message     string `json:"message"`
}

// GetMyWebsiteResponse represents the response payload for getting current user's website
type GetMyWebsiteResponse struct {
	WebsiteID   uint   `json:"website_id"`
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	Domain      string `json:"domain,omitempty"`
	Language    string `json:"language"`
	Timezone    string `json:"timezone"`
	Status      string `json:"status"`
}
