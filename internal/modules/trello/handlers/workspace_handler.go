package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

type WorkspaceHandler struct {
	service   services.WorkspaceService
	validator validator.Validator
	logger    utils.Logger
}

func NewWorkspaceHandler(service services.WorkspaceService, validator validator.Validator, logger utils.Logger) *WorkspaceHandler {
	return &WorkspaceHandler{
		service:   service,
		validator: validator,
		logger:    logger,
	}
}

func (h *WorkspaceHandler) Create(c *gin.Context) {
	var request models.CreateWorkspaceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	workspace, err := h.service.Create(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), &request)
	if err != nil {
		h.logger.Error("failed to create workspace", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to create workspace")
		return
	}

	response.Created(c.Writer, workspace)
}

func (h *WorkspaceHandler) GetByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid workspace ID")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	workspace, err := h.service.GetByID(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "workspace not found" {
			response.NotFound(c.Writer, "Workspace not found")
			return
		}
		if err.Error() == "access denied" {
			response.Forbidden(c.Writer, "Access denied")
			return
		}
		h.logger.Error("failed to get workspace", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to get workspace")
		return
	}

	response.Success(c.Writer, workspace)
}

func (h *WorkspaceHandler) Update(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid workspace ID")
		return
	}

	var request models.UpdateWorkspaceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	workspace, err := h.service.Update(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), uint(id), &request)
	if err != nil {
		if err.Error() == "workspace not found" {
			response.NotFound(c.Writer, "Workspace not found")
			return
		}
		if err.Error() == "access denied" || err.Error() == "insufficient permissions" {
			response.Forbidden(c.Writer, "Access denied")
			return
		}
		h.logger.Error("failed to update workspace", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to update workspace")
		return
	}

	response.Success(c.Writer, workspace)
}

func (h *WorkspaceHandler) Delete(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid workspace ID")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	err = h.service.Delete(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "workspace not found" {
			response.NotFound(c.Writer, "Workspace not found")
			return
		}
		if err.Error() == "access denied" || err.Error() == "only workspace owner can delete workspace" {
			response.Forbidden(c.Writer, "Access denied")
			return
		}
		h.logger.Error("failed to delete workspace", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to delete workspace")
		return
	}

	response.NoContent(c.Writer)
}

func (h *WorkspaceHandler) List(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	pag := pagination.NewCursorPaginationFromQuery(c)

	workspaces, cursor, err := h.service.List(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), pag)
	if err != nil {
		h.logger.Error("failed to list workspaces", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to list workspaces")
		return
	}

	response.CursorPaginated(c.Writer, workspaces, *cursor)
}

func (h *WorkspaceHandler) GetWithBoards(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid workspace ID")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	workspace, err := h.service.GetWithBoards(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "workspace not found" {
			response.NotFound(c.Writer, "Workspace not found")
			return
		}
		if err.Error() == "access denied" {
			response.Forbidden(c.Writer, "Access denied")
			return
		}
		h.logger.Error("failed to get workspace with boards", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to get workspace with boards")
		return
	}

	response.Success(c.Writer, workspace)
}

func (h *WorkspaceHandler) GetWithMembers(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid workspace ID")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	workspace, err := h.service.GetWithMembers(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "workspace not found" {
			response.NotFound(c.Writer, "Workspace not found")
			return
		}
		if err.Error() == "access denied" {
			response.Forbidden(c.Writer, "Access denied")
			return
		}
		h.logger.Error("failed to get workspace with members", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to get workspace with members")
		return
	}

	response.Success(c.Writer, workspace)
}

func (h *WorkspaceHandler) AddMember(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid workspace ID")
		return
	}

	var request models.AddWorkspaceMemberRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	member, err := h.service.AddMember(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), uint(id), &request)
	if err != nil {
		if err.Error() == "access denied" || err.Error() == "insufficient permissions" {
			response.Forbidden(c.Writer, "Access denied")
			return
		}
		if err.Error() == "user is already a member" {
			response.Conflict(c.Writer, "User is already a member")
			return
		}
		h.logger.Error("failed to add workspace member", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to add workspace member")
		return
	}

	response.Created(c.Writer, member)
}

func (h *WorkspaceHandler) RemoveMember(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid workspace ID")
		return
	}

	memberID, err := strconv.ParseUint(c.Param("memberId"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid member ID")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	err = h.service.RemoveMember(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), uint(id), uint(memberID))
	if err != nil {
		if err.Error() == "access denied" || err.Error() == "insufficient permissions" {
			response.Forbidden(c.Writer, "Access denied")
			return
		}
		if err.Error() == "cannot remove yourself" {
			response.BadRequest(c.Writer, "Cannot remove yourself")
			return
		}
		h.logger.Error("failed to remove workspace member", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to remove workspace member")
		return
	}

	response.NoContent(c.Writer)
}

func (h *WorkspaceHandler) UpdateMember(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid workspace ID")
		return
	}

	memberID, err := strconv.ParseUint(c.Param("memberId"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid member ID")
		return
	}

	var request models.UpdateWorkspaceMemberRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	member, err := h.service.UpdateMember(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), uint(id), uint(memberID), &request)
	if err != nil {
		if err.Error() == "access denied" || err.Error() == "insufficient permissions" {
			response.Forbidden(c.Writer, "Access denied")
			return
		}
		h.logger.Error("failed to update workspace member", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to update workspace member")
		return
	}

	response.Success(c.Writer, member)
}

func (h *WorkspaceHandler) ListMembers(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid workspace ID")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.BadRequest(c.Writer, "User ID is required")
		return
	}

	members, err := h.service.ListMembers(c.Request.Context(), tenantID.(uint), c.GetUint("website_id"), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "access denied" {
			response.Forbidden(c.Writer, "Access denied")
			return
		}
		h.logger.Error("failed to list workspace members", utils.WithError(err))
		response.InternalServerError(c.Writer, "Failed to list workspace members")
		return
	}

	response.Success(c.Writer, members)
}
