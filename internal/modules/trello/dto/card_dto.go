package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"gorm.io/datatypes"
)

// @Summary Create card request
// @Description Request to create a new Trello card
// @Tags Card
type CreateCardRequest struct {
	ListID       uint             `json:"list_id" validate:"required" example:"1"`
	Title        string           `json:"title" validate:"required,max=512" example:"Complete project documentation"`
	Description  *string          `json:"description,omitempty" example:"Write comprehensive documentation for the API"`
	Position     float64          `json:"position" example:"1024.0"`
	DueDate      *time.Time       `json:"due_date,omitempty" example:"2024-02-15T17:00:00Z"`
	StartDate    *time.Time       `json:"start_date,omitempty" example:"2024-02-01T09:00:00Z"`
	CoverType    models.CoverType `json:"cover_type" validate:"oneof=none color image" example:"color"`
	CoverValue   *string          `json:"cover_value,omitempty" example:"#e74c3c"`
	CoverColor   *string          `json:"cover_color,omitempty" example:"#e74c3c"`
	CoverSize    models.CoverSize `json:"cover_size" validate:"oneof=normal full" example:"normal"`
	CustomFields datatypes.JSON   `json:"custom_fields,omitempty" example:"{\"priority\":\"high\",\"category\":\"documentation\"}"`
}

// @Summary Update card request
// @Description Request to update an existing Trello card
// @Tags Card
type UpdateCardRequest struct {
	Title        *string           `json:"title" validate:"omitempty,max=512" example:"Updated project documentation"`
	Description  *string           `json:"description,omitempty" example:"Updated comprehensive documentation"`
	Position     *float64          `json:"position,omitempty" example:"2048.0"`
	DueDate      *time.Time        `json:"due_date,omitempty" example:"2024-02-20T17:00:00Z"`
	StartDate    *time.Time        `json:"start_date,omitempty" example:"2024-02-05T09:00:00Z"`
	DueComplete  *bool             `json:"due_complete,omitempty" example:"false"`
	IsClosed     *bool             `json:"is_closed,omitempty" example:"false"`
	IsSubscribed *bool             `json:"is_subscribed,omitempty" example:"true"`
	CoverType    *models.CoverType `json:"cover_type" validate:"omitempty,oneof=none color image" example:"image"`
	CoverValue   *string           `json:"cover_value,omitempty" example:"https://example.com/cover.jpg"`
	CoverColor   *string           `json:"cover_color,omitempty" example:"#3498db"`
	CoverSize    *models.CoverSize `json:"cover_size" validate:"omitempty,oneof=normal full" example:"full"`
	CustomFields datatypes.JSON    `json:"custom_fields,omitempty" example:"{\"priority\":\"medium\",\"category\":\"documentation\"}"`
}

// @Summary Move card request
// @Description Request to move a card to different position or list
// @Tags Card
type MoveCardRequest struct {
	ListID   *uint   `json:"list_id,omitempty" example:"2"`
	Position float64 `json:"position" validate:"required" example:"3072.0"`
}

// @Summary Card response
// @Description Card information in API responses
// @Tags Card
type CardResponse struct {
	ID           uint                `json:"id" example:"1"`
	TenantID     uint                `json:"tenant_id" example:"1"`
	WebsiteID    uint                `json:"website_id" example:"1"`
	BoardID      uint                `json:"board_id" example:"1"`
	ListID       uint                `json:"list_id" example:"1"`
	Title        string              `json:"title" example:"Complete project documentation"`
	Description  *string             `json:"description,omitempty" example:"Write comprehensive documentation"`
	Position     float64             `json:"position" example:"1024.0"`
	DueDate      *time.Time          `json:"due_date,omitempty" example:"2024-02-15T17:00:00Z"`
	StartDate    *time.Time          `json:"start_date,omitempty" example:"2024-02-01T09:00:00Z"`
	DueComplete  bool                `json:"due_complete" example:"false"`
	IsClosed     bool                `json:"is_closed" example:"false"`
	IsSubscribed bool                `json:"is_subscribed" example:"true"`
	CoverType    models.CoverType    `json:"cover_type" example:"color"`
	CoverValue   *string             `json:"cover_value,omitempty" example:"#e74c3c"`
	CoverColor   *string             `json:"cover_color,omitempty" example:"#e74c3c"`
	CoverSize    models.CoverSize    `json:"cover_size" example:"normal"`
	Badges       datatypes.JSON      `json:"badges" example:"{\"votes\":0,\"attachments\":2,\"comments\":3}"`
	CustomFields datatypes.JSON      `json:"custom_fields" example:"{\"priority\":\"high\"}"`
	CreatedBy    uint                `json:"created_by" example:"123"`
	Status       models.TrelloStatus `json:"status" example:"active"`
	CreatedAt    time.Time           `json:"created_at"`
	UpdatedAt    time.Time           `json:"updated_at"`
}

// @Summary Card summary response
// @Description Simplified card information for listings
// @Tags Card
type CardSummaryResponse struct {
	ID          uint             `json:"id" example:"1"`
	Title       string           `json:"title" example:"Complete project documentation"`
	Position    float64          `json:"position" example:"1024.0"`
	DueDate     *time.Time       `json:"due_date,omitempty" example:"2024-02-15T17:00:00Z"`
	DueComplete bool             `json:"due_complete" example:"false"`
	IsClosed    bool             `json:"is_closed" example:"false"`
	CoverType   models.CoverType `json:"cover_type" example:"color"`
	CoverValue  *string          `json:"cover_value,omitempty" example:"#e74c3c"`
	CoverColor  *string          `json:"cover_color,omitempty" example:"#e74c3c"`
	UpdatedAt   time.Time        `json:"updated_at"`
}

// @Summary Card detail response
// @Description Detailed card information with associated data
// @Tags Card
type CardDetailResponse struct {
	CardResponse
	Members    []CardMemberResponse   `json:"members"`
	Labels     []CardLabelResponse    `json:"labels"`
	Checklists []ChecklistResponse    `json:"checklists"`
	Comments   []CardCommentResponse  `json:"comments"`
	Activities []CardActivityResponse `json:"activities"`
}

// @Summary Card member response
// @Description Card member assignment information
// @Tags Card
type CardMemberResponse struct {
	ID         uint      `json:"id" example:"1"`
	UserID     uint      `json:"user_id" example:"123"`
	AssignedBy uint      `json:"assigned_by" example:"456"`
	AssignedAt time.Time `json:"assigned_at"`
}

// @Summary Assign card member request
// @Description Request to assign a member to card
// @Tags Card
type AssignCardMemberRequest struct {
	UserID uint `json:"user_id" validate:"required" example:"123"`
}

// @Summary Card label response
// @Description Card label assignment information
// @Tags Card
type CardLabelResponse struct {
	ID        uint      `json:"id" example:"1"`
	LabelID   uint      `json:"label_id" example:"1"`
	Name      string    `json:"name" example:"High Priority"`
	Color     string    `json:"color" example:"#e74c3c"`
	AppliedBy uint      `json:"applied_by" example:"123"`
	AppliedAt time.Time `json:"applied_at"`
}

// @Summary Apply card label request
// @Description Request to apply a label to card
// @Tags Card
type ApplyCardLabelRequest struct {
	LabelID uint `json:"label_id" validate:"required" example:"1"`
}

// @Summary Card comment response
// @Description Card comment information
// @Tags Card
type CardCommentResponse struct {
	ID        uint               `json:"id" example:"1"`
	TenantID  uint               `json:"tenant_id" example:"1"`
	WebsiteID uint               `json:"website_id" example:"1"`
	CardID    uint               `json:"card_id" example:"1"`
	BoardID   uint               `json:"board_id" example:"1"`
	UserID    uint               `json:"user_id" example:"123"`
	Content   string             `json:"content" example:"This looks good to me!"`
	Type      models.CommentType `json:"type" example:"comment"`
	CreatedAt time.Time          `json:"created_at"`
	UpdatedAt time.Time          `json:"updated_at"`
}

// @Summary Create comment request
// @Description Request to create a comment on card
// @Tags Card
type CreateCommentRequest struct {
	CardID  uint               `json:"card_id" validate:"required" example:"1"`
	Content string             `json:"content" validate:"required" example:"This task is completed!"`
	Type    models.CommentType `json:"type" validate:"oneof=comment action" example:"comment"`
}

// @Summary Card activity response
// @Description Card activity information
// @Tags Card
type CardActivityResponse struct {
	ID        uint           `json:"id" example:"1"`
	TenantID  uint           `json:"tenant_id" example:"1"`
	WebsiteID uint           `json:"website_id" example:"1"`
	BoardID   uint           `json:"board_id" example:"1"`
	CardID    uint           `json:"card_id" example:"1"`
	ListID    *uint          `json:"list_id,omitempty" example:"1"`
	UserID    uint           `json:"user_id" example:"123"`
	Type      string         `json:"type" example:"card_moved"`
	Data      datatypes.JSON `json:"data" example:"{\"from_list\":\"To Do\",\"to_list\":\"In Progress\"}"`
	OldValue  datatypes.JSON `json:"old_value,omitempty" example:"{\"list_id\":1}"`
	NewValue  datatypes.JSON `json:"new_value,omitempty" example:"{\"list_id\":2}"`
	CreatedAt time.Time      `json:"created_at"`
}

// @Summary Card filter
// @Description Filter parameters for listing cards
// @Tags Card
type CardFilter struct {
	BoardID      *uint                `json:"board_id,omitempty" example:"1"`
	ListID       *uint                `json:"list_id,omitempty" example:"1"`
	Title        string               `json:"title,omitempty" example:"documentation"`
	AssignedTo   *uint                `json:"assigned_to,omitempty" example:"123"`
	LabelIDs     []uint               `json:"label_ids,omitempty" example:"1,2,3"`
	DueDate      *time.Time           `json:"due_date,omitempty" example:"2024-02-15T17:00:00Z"`
	DueSoon      *bool                `json:"due_soon,omitempty" example:"true"`
	Overdue      *bool                `json:"overdue,omitempty" example:"false"`
	DueComplete  *bool                `json:"due_complete,omitempty" example:"false"`
	IsClosed     *bool                `json:"is_closed,omitempty" example:"false"`
	IsSubscribed *bool                `json:"is_subscribed,omitempty" example:"true"`
	CoverType    *models.CoverType    `json:"cover_type,omitempty" validate:"omitempty,oneof=none color image" example:"color"`
	Status       *models.TrelloStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive archived deleted" example:"active"`
	CreatedBy    *uint                `json:"created_by,omitempty" example:"123"`
	Search       string               `json:"search,omitempty" example:"project"`
	DateFrom     *time.Time           `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
	DateTo       *time.Time           `json:"date_to,omitempty" example:"2024-12-31T23:59:59Z"`
	Page         int                  `json:"page" validate:"min=1" example:"1"`
	PageSize     int                  `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy       string               `json:"sort_by" validate:"omitempty,oneof=id title position due_date created_at updated_at" example:"due_date"`
	SortOrder    string               `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"asc"`
}

// @Summary Card stats response
// @Description Card statistics information
// @Tags Card
type CardStatsResponse struct {
	TotalCards       int                   `json:"total_cards" example:"85"`
	ActiveCards      int                   `json:"active_cards" example:"72"`
	ClosedCards      int                   `json:"closed_cards" example:"13"`
	OverdueCards     int                   `json:"overdue_cards" example:"5"`
	DueTodayCards    int                   `json:"due_today_cards" example:"3"`
	CompletedCards   int                   `json:"completed_cards" example:"45"`
	WithDueDates     int                   `json:"with_due_dates" example:"38"`
	WithMembers      int                   `json:"with_members" example:"62"`
	WithLabels       int                   `json:"with_labels" example:"71"`
	WithCovers       int                   `json:"with_covers" example:"29"`
	ByList           map[string]int        `json:"by_list"`
	ByCoverType      map[string]int        `json:"by_cover_type"`
	RecentCards      []CardResponse        `json:"recent_cards"`
	OverdueCardsList []CardSummaryResponse `json:"overdue_cards_list"`
}

// @Summary Archive card request
// @Description Request to archive/unarchive a card
// @Tags Card
type ArchiveCardRequest struct {
	IsClosed bool `json:"is_closed" example:"true"`
}

// @Summary Complete due date request
// @Description Request to mark card due date as complete/incomplete
// @Tags Card
type CompleteDueDateRequest struct {
	DueComplete bool `json:"due_complete" example:"true"`
}

// @Summary Subscribe to card request
// @Description Request to subscribe/unsubscribe to card notifications
// @Tags Card
type SubscribeCardRequest struct {
	IsSubscribed bool `json:"is_subscribed" example:"true"`
}

// @Summary Copy card request
// @Description Request to copy a card with options
// @Tags Card
type CopyCardRequest struct {
	ListID          uint    `json:"list_id" validate:"required" example:"1"`
	Title           string  `json:"title" validate:"required,max=512" example:"Copy of Complete project documentation"`
	Position        float64 `json:"position" example:"4096.0"`
	KeepLabels      bool    `json:"keep_labels" example:"true"`
	KeepMembers     bool    `json:"keep_members" example:"false"`
	KeepChecklists  bool    `json:"keep_checklists" example:"true"`
	KeepDueDate     bool    `json:"keep_due_date" example:"false"`
	KeepAttachments bool    `json:"keep_attachments" example:"false"`
}

// ToModelCreateRequest converts DTO to models.CreateCardRequest
func (r *CreateCardRequest) ToModelCreateRequest() models.CreateCardRequest {
	return models.CreateCardRequest{
		ListID:       r.ListID,
		Title:        r.Title,
		Description:  r.Description,
		Position:     r.Position,
		DueDate:      r.DueDate,
		StartDate:    r.StartDate,
		CoverType:    r.CoverType,
		CoverValue:   r.CoverValue,
		CoverColor:   r.CoverColor,
		CoverSize:    r.CoverSize,
		CustomFields: r.CustomFields,
	}
}

// ToModelUpdateRequest converts DTO to models.UpdateCardRequest
func (r *UpdateCardRequest) ToModelUpdateRequest() models.UpdateCardRequest {
	return models.UpdateCardRequest{
		Title:        r.Title,
		Description:  r.Description,
		Position:     r.Position,
		DueDate:      r.DueDate,
		StartDate:    r.StartDate,
		DueComplete:  r.DueComplete,
		IsClosed:     r.IsClosed,
		IsSubscribed: r.IsSubscribed,
		CoverType:    *r.CoverType,
		CoverValue:   r.CoverValue,
		CoverColor:   r.CoverColor,
		CoverSize:    *r.CoverSize,
		CustomFields: r.CustomFields,
	}
}

// ToModelMoveRequest converts DTO to models.MoveCardRequest
func (r *MoveCardRequest) ToModelMoveRequest() models.MoveCardRequest {
	return models.MoveCardRequest{
		ListID:   r.ListID,
		Position: r.Position,
	}
}

// ToModelAssignMemberRequest converts DTO to models.AssignCardMemberRequest
func (r *AssignCardMemberRequest) ToModelAssignMemberRequest() models.AssignCardMemberRequest {
	return models.AssignCardMemberRequest{
		UserID: r.UserID,
	}
}

// ToModelApplyLabelRequest converts DTO to models.ApplyCardLabelRequest
func (r *ApplyCardLabelRequest) ToModelApplyLabelRequest() models.ApplyCardLabelRequest {
	return models.ApplyCardLabelRequest{
		LabelID: r.LabelID,
	}
}

// ToModelCreateCommentRequest converts DTO to models.CreateCommentRequest
func (r *CreateCommentRequest) ToModelCreateCommentRequest() models.CreateCommentRequest {
	return models.CreateCommentRequest{
		CardID:  r.CardID,
		Content: r.Content,
		Type:    r.Type,
	}
}
