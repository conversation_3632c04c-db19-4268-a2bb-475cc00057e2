package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
)

// @Summary Create label request
// @Description Request to create a new Trello label
// @Tags Label
type CreateLabelRequest struct {
	BoardID uint   `json:"board_id" validate:"required" example:"1"`
	Name    string `json:"name" validate:"required,max=255" example:"High Priority"`
	Color   string `json:"color" validate:"required,hexcolor" example:"#e74c3c"`
}

// @Summary Update label request
// @Description Request to update an existing Trello label
// @Tags Label
type UpdateLabelRequest struct {
	Name  *string `json:"name" validate:"omitempty,max=255" example:"Critical Priority"`
	Color *string `json:"color" validate:"omitempty,hexcolor" example:"#c0392b"`
}

// @Summary Label response
// @Description Label information in API responses
// @Tags Label
type LabelResponse struct {
	ID        uint                `json:"id" example:"1"`
	TenantID  uint                `json:"tenant_id" example:"1"`
	WebsiteID uint                `json:"website_id" example:"1"`
	BoardID   uint                `json:"board_id" example:"1"`
	Name      string              `json:"name" example:"High Priority"`
	Color     string              `json:"color" example:"#e74c3c"`
	Uses      uint                `json:"uses" example:"15"`
	CreatedBy uint                `json:"created_by" example:"123"`
	Status    models.TrelloStatus `json:"status" example:"active"`
	CreatedAt time.Time           `json:"created_at"`
	UpdatedAt time.Time           `json:"updated_at"`
}

// @Summary Label summary response
// @Description Simplified label information for listings
// @Tags Label
type LabelSummaryResponse struct {
	ID    uint   `json:"id" example:"1"`
	Name  string `json:"name" example:"High Priority"`
	Color string `json:"color" example:"#e74c3c"`
	Uses  uint   `json:"uses" example:"15"`
}

// @Summary Label with usage response
// @Description Label information with detailed usage statistics
// @Tags Label
type LabelWithUsageResponse struct {
	LabelResponse
	UsageByList  map[string]int    `json:"usage_by_list"`
	RecentlyUsed []time.Time       `json:"recently_used"`
	TopCards     []CardLabelUsage  `json:"top_cards"`
	UsageTrend   []LabelUsageTrend `json:"usage_trend"`
}

// @Summary Card label usage
// @Description Card information where label is used
// @Tags Label
type CardLabelUsage struct {
	CardID    uint      `json:"card_id" example:"1"`
	CardTitle string    `json:"card_title" example:"Complete documentation"`
	ListName  string    `json:"list_name" example:"In Progress"`
	AppliedAt time.Time `json:"applied_at"`
}

// @Summary Label usage trend
// @Description Label usage trend over time
// @Tags Label
type LabelUsageTrend struct {
	Date  time.Time `json:"date" example:"2024-01-15T00:00:00Z"`
	Count int       `json:"count" example:"5"`
}

// @Summary Label filter
// @Description Filter parameters for listing labels
// @Tags Label
type LabelFilter struct {
	BoardID   *uint                `json:"board_id,omitempty" example:"1"`
	Name      string               `json:"name,omitempty" example:"Priority"`
	Color     string               `json:"color,omitempty" example:"#e74c3c"`
	MinUses   *uint                `json:"min_uses,omitempty" example:"5"`
	MaxUses   *uint                `json:"max_uses,omitempty" example:"50"`
	CreatedBy *uint                `json:"created_by,omitempty" example:"123"`
	Status    *models.TrelloStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive archived deleted" example:"active"`
	Search    string               `json:"search,omitempty" example:"priority"`
	DateFrom  *time.Time           `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
	DateTo    *time.Time           `json:"date_to,omitempty" example:"2024-12-31T23:59:59Z"`
	Page      int                  `json:"page" validate:"min=1" example:"1"`
	PageSize  int                  `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy    string               `json:"sort_by" validate:"omitempty,oneof=id name color uses created_at updated_at" example:"uses"`
	SortOrder string               `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// @Summary Label list response
// @Description Response for listing labels
// @Tags Label
type LabelListResponse struct {
	Labels []LabelResponse `json:"labels"`
}

// @Summary Label stats response
// @Description Label statistics information
// @Tags Label
type LabelStatsResponse struct {
	TotalLabels       int                       `json:"total_labels" example:"25"`
	ActiveLabels      int                       `json:"active_labels" example:"20"`
	UnusedLabels      int                       `json:"unused_labels" example:"3"`
	MostUsedLabels    []LabelResponse           `json:"most_used_labels"`
	LeastUsedLabels   []LabelResponse           `json:"least_used_labels"`
	ColorDistribution map[string]int            `json:"color_distribution"`
	UsageByBoard      map[string]int            `json:"usage_by_board"`
	RecentlyCreated   []LabelResponse           `json:"recently_created"`
	UsageTrends       []LabelUsageOverviewTrend `json:"usage_trends"`
}

// @Summary Label usage overview trend
// @Description Overall label usage trend information
// @Tags Label
type LabelUsageOverviewTrend struct {
	Date          time.Time `json:"date" example:"2024-01-15T00:00:00Z"`
	TotalUsages   int       `json:"total_usages" example:"45"`
	LabelsCreated int       `json:"labels_created" example:"2"`
	LabelsDeleted int       `json:"labels_deleted" example:"0"`
	ActiveLabels  int       `json:"active_labels" example:"23"`
}

// @Summary Bulk apply labels request
// @Description Request to apply labels to multiple cards
// @Tags Label
type BulkApplyLabelsRequest struct {
	CardIDs  []uint `json:"card_ids" validate:"required,min=1" example:"1,2,3"`
	LabelIDs []uint `json:"label_ids" validate:"required,min=1" example:"1,2"`
}

// @Summary Bulk remove labels request
// @Description Request to remove labels from multiple cards
// @Tags Label
type BulkRemoveLabelsRequest struct {
	CardIDs  []uint `json:"card_ids" validate:"required,min=1" example:"1,2,3"`
	LabelIDs []uint `json:"label_ids" validate:"required,min=1" example:"1,2"`
}

// @Summary Copy labels request
// @Description Request to copy labels from one board to another
// @Tags Label
type CopyLabelsRequest struct {
	SourceBoardID      uint   `json:"source_board_id" validate:"required" example:"1"`
	DestinationBoardID uint   `json:"destination_board_id" validate:"required" example:"2"`
	LabelIDs           []uint `json:"label_ids,omitempty" example:"1,2,3"`
	OverwriteExisting  bool   `json:"overwrite_existing" example:"false"`
	IncludeUsages      bool   `json:"include_usages" example:"false"`
}

// @Summary Merge labels request
// @Description Request to merge multiple labels into one
// @Tags Label
type MergeLabelsRequest struct {
	SourceLabelIDs []uint `json:"source_label_ids" validate:"required,min=2" example:"2,3,4"`
	TargetLabelID  uint   `json:"target_label_id" validate:"required" example:"1"`
	DeleteSources  bool   `json:"delete_sources" example:"true"`
}

// @Summary Cleanup unused labels request
// @Description Request to cleanup labels that are not used
// @Tags Label
type CleanupUnusedLabelsRequest struct {
	BoardID       *uint `json:"board_id,omitempty" example:"1"`
	DryRun        bool  `json:"dry_run" example:"true"`
	MinUnusedDays int   `json:"min_unused_days" validate:"min=1" example:"30"`
}

// @Summary Label cleanup response
// @Description Response for label cleanup operation
// @Tags Label
type LabelCleanupResponse struct {
	Success       bool            `json:"success" example:"true"`
	Message       string          `json:"message" example:"Cleanup completed successfully"`
	LabelsFound   int             `json:"labels_found" example:"15"`
	LabelsDeleted int             `json:"labels_deleted" example:"3"`
	DeletedLabels []LabelResponse `json:"deleted_labels,omitempty"`
	DryRun        bool            `json:"dry_run" example:"false"`
}

// @Summary Suggest label colors request
// @Description Request to get suggested colors for a label name
// @Tags Label
type SuggestLabelColorsRequest struct {
	Name          string   `json:"name" validate:"required" example:"Bug"`
	BoardID       uint     `json:"board_id" validate:"required" example:"1"`
	ExcludeColors []string `json:"exclude_colors,omitempty" example:"#e74c3c,#3498db"`
}

// @Summary Suggested label colors response
// @Description Response with suggested colors for labels
// @Tags Label
type SuggestedLabelColorsResponse struct {
	Suggestions []LabelColorSuggestion `json:"suggestions"`
}

// @Summary Label color suggestion
// @Description Individual color suggestion for label
// @Tags Label
type LabelColorSuggestion struct {
	Color       string  `json:"color" example:"#e74c3c"`
	Name        string  `json:"name" example:"Red"`
	Description string  `json:"description" example:"Commonly used for bugs or urgent items"`
	Confidence  float64 `json:"confidence" example:"0.85"`
	UsageCount  int     `json:"usage_count" example:"12"`
}

// ToModelCreateRequest converts DTO to models.CreateLabelRequest
func (r *CreateLabelRequest) ToModelCreateRequest() models.CreateLabelRequest {
	return models.CreateLabelRequest{
		BoardID: r.BoardID,
		Name:    r.Name,
		Color:   r.Color,
	}
}

// ToModelUpdateRequest converts DTO to models.UpdateLabelRequest
func (r *UpdateLabelRequest) ToModelUpdateRequest() models.UpdateLabelRequest {
	return models.UpdateLabelRequest{
		Name:  r.Name,
		Color: r.Color,
	}
}
