package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
)

// @Summary Create checklist request
// @Description Request to create a new Trello checklist
// @Tags Checklist
type CreateChecklistRequest struct {
	CardID   uint    `json:"card_id" validate:"required" example:"1"`
	Name     string  `json:"name" validate:"required,max=255" example:"Project Tasks"`
	Position float64 `json:"position" example:"1024.0"`
}

// @Summary Update checklist request
// @Description Request to update an existing Trello checklist
// @Tags Checklist
type UpdateChecklistRequest struct {
	Name     *string  `json:"name" validate:"omitempty,max=255" example:"Updated Project Tasks"`
	Position *float64 `json:"position,omitempty" example:"2048.0"`
}

// @Summary Checklist response
// @Description Checklist information in API responses
// @Tags Checklist
type ChecklistResponse struct {
	ID                     uint                    `json:"id" example:"1"`
	TenantID               uint                    `json:"tenant_id" example:"1"`
	WebsiteID              uint                    `json:"website_id" example:"1"`
	CardID                 uint                    `json:"card_id" example:"1"`
	Name                   string                  `json:"name" example:"Project Tasks"`
	Position               float64                 `json:"position" example:"1024.0"`
	CheckItemsCount        uint                    `json:"check_items_count" example:"5"`
	CheckItemsCheckedCount uint                    `json:"check_items_checked_count" example:"3"`
	CreatedBy              uint                    `json:"created_by" example:"123"`
	Status                 models.TrelloStatus     `json:"status" example:"active"`
	CreatedAt              time.Time               `json:"created_at"`
	UpdatedAt              time.Time               `json:"updated_at"`
	Items                  []ChecklistItemResponse `json:"items"`
}

// @Summary Checklist summary response
// @Description Simplified checklist information for nested responses
// @Tags Checklist
type ChecklistSummaryResponse struct {
	ID                     uint      `json:"id" example:"1"`
	Name                   string    `json:"name" example:"Project Tasks"`
	Position               float64   `json:"position" example:"1024.0"`
	CheckItemsCount        uint      `json:"check_items_count" example:"5"`
	CheckItemsCheckedCount uint      `json:"check_items_checked_count" example:"3"`
	CompletionPercentage   float64   `json:"completion_percentage" example:"60.0"`
	UpdatedAt              time.Time `json:"updated_at"`
}

// @Summary Create checklist item request
// @Description Request to create a new checklist item
// @Tags Checklist
type CreateChecklistItemRequest struct {
	ChecklistID    uint       `json:"checklist_id" validate:"required" example:"1"`
	Name           string     `json:"name" validate:"required,max=512" example:"Complete API documentation"`
	Position       float64    `json:"position" example:"1024.0"`
	DueDate        *time.Time `json:"due_date,omitempty" example:"2024-02-15T17:00:00Z"`
	AssignedUserID *uint      `json:"assigned_user_id,omitempty" example:"123"`
}

// @Summary Update checklist item request
// @Description Request to update an existing checklist item
// @Tags Checklist
type UpdateChecklistItemRequest struct {
	Name           *string    `json:"name" validate:"omitempty,max=512" example:"Updated API documentation"`
	Position       *float64   `json:"position,omitempty" example:"2048.0"`
	IsChecked      *bool      `json:"is_checked,omitempty" example:"true"`
	DueDate        *time.Time `json:"due_date,omitempty" example:"2024-02-20T17:00:00Z"`
	AssignedUserID *uint      `json:"assigned_user_id,omitempty" example:"456"`
}

// @Summary Checklist item response
// @Description Checklist item information in API responses
// @Tags Checklist
type ChecklistItemResponse struct {
	ID             uint                `json:"id" example:"1"`
	TenantID       uint                `json:"tenant_id" example:"1"`
	WebsiteID      uint                `json:"website_id" example:"1"`
	ChecklistID    uint                `json:"checklist_id" example:"1"`
	CardID         uint                `json:"card_id" example:"1"`
	Name           string              `json:"name" example:"Complete API documentation"`
	Position       float64             `json:"position" example:"1024.0"`
	IsChecked      bool                `json:"is_checked" example:"true"`
	DueDate        *time.Time          `json:"due_date,omitempty" example:"2024-02-15T17:00:00Z"`
	AssignedUserID *uint               `json:"assigned_user_id,omitempty" example:"123"`
	CreatedBy      uint                `json:"created_by" example:"123"`
	CheckedBy      *uint               `json:"checked_by,omitempty" example:"123"`
	CheckedAt      *time.Time          `json:"checked_at,omitempty"`
	Status         models.TrelloStatus `json:"status" example:"active"`
	CreatedAt      time.Time           `json:"created_at"`
	UpdatedAt      time.Time           `json:"updated_at"`
}

// @Summary Check item request
// @Description Request to check/uncheck a checklist item
// @Tags Checklist
type CheckItemRequest struct {
	IsChecked bool `json:"is_checked" example:"true"`
}

// @Summary Move checklist item request
// @Description Request to move checklist item to different position
// @Tags Checklist
type MoveChecklistItemRequest struct {
	ChecklistID *uint   `json:"checklist_id,omitempty" example:"2"`
	Position    float64 `json:"position" validate:"required" example:"3072.0"`
}

// @Summary Copy checklist request
// @Description Request to copy a checklist with options
// @Tags Checklist
type CopyChecklistRequest struct {
	CardID              uint    `json:"card_id" validate:"required" example:"2"`
	Name                string  `json:"name" validate:"required,max=255" example:"Copy of Project Tasks"`
	Position            float64 `json:"position" example:"4096.0"`
	CopyItems           bool    `json:"copy_items" example:"true"`
	KeepItemAssignments bool    `json:"keep_item_assignments" example:"false"`
	KeepItemDueDates    bool    `json:"keep_item_due_dates" example:"true"`
	KeepCheckedState    bool    `json:"keep_checked_state" example:"false"`
}

// @Summary Checklist filter
// @Description Filter parameters for listing checklists
// @Tags Checklist
type ChecklistFilter struct {
	CardID    *uint                `json:"card_id,omitempty" example:"1"`
	Name      string               `json:"name,omitempty" example:"Tasks"`
	CreatedBy *uint                `json:"created_by,omitempty" example:"123"`
	Status    *models.TrelloStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive archived deleted" example:"active"`
	Search    string               `json:"search,omitempty" example:"project"`
	DateFrom  *time.Time           `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
	DateTo    *time.Time           `json:"date_to,omitempty" example:"2024-12-31T23:59:59Z"`
	Page      int                  `json:"page" validate:"min=1" example:"1"`
	PageSize  int                  `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy    string               `json:"sort_by" validate:"omitempty,oneof=id name position created_at updated_at" example:"position"`
	SortOrder string               `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"asc"`
}

// @Summary Checklist item filter
// @Description Filter parameters for listing checklist items
// @Tags Checklist
type ChecklistItemFilter struct {
	ChecklistID    *uint                `json:"checklist_id,omitempty" example:"1"`
	CardID         *uint                `json:"card_id,omitempty" example:"1"`
	Name           string               `json:"name,omitempty" example:"documentation"`
	IsChecked      *bool                `json:"is_checked,omitempty" example:"false"`
	AssignedUserID *uint                `json:"assigned_user_id,omitempty" example:"123"`
	DueDate        *time.Time           `json:"due_date,omitempty" example:"2024-02-15T17:00:00Z"`
	DueSoon        *bool                `json:"due_soon,omitempty" example:"true"`
	Overdue        *bool                `json:"overdue,omitempty" example:"false"`
	CreatedBy      *uint                `json:"created_by,omitempty" example:"123"`
	CheckedBy      *uint                `json:"checked_by,omitempty" example:"123"`
	Status         *models.TrelloStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive archived deleted" example:"active"`
	Search         string               `json:"search,omitempty" example:"api"`
	DateFrom       *time.Time           `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
	DateTo         *time.Time           `json:"date_to,omitempty" example:"2024-12-31T23:59:59Z"`
	Page           int                  `json:"page" validate:"min=1" example:"1"`
	PageSize       int                  `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy         string               `json:"sort_by" validate:"omitempty,oneof=id name position due_date created_at updated_at" example:"due_date"`
	SortOrder      string               `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"asc"`
}

// @Summary Checklist stats response
// @Description Checklist statistics information
// @Tags Checklist
type ChecklistStatsResponse struct {
	TotalChecklists        int                          `json:"total_checklists" example:"12"`
	ActiveChecklists       int                          `json:"active_checklists" example:"10"`
	TotalItems             int                          `json:"total_items" example:"85"`
	CompletedItems         int                          `json:"completed_items" example:"62"`
	OverdueItems           int                          `json:"overdue_items" example:"3"`
	ItemsDueToday          int                          `json:"items_due_today" example:"5"`
	CompletionRate         float64                      `json:"completion_rate" example:"72.9"`
	AverageItemsPerList    float64                      `json:"average_items_per_list" example:"7.08"`
	ChecklistsWithoutItems int                          `json:"checklists_without_items" example:"1"`
	RecentChecklists       []ChecklistResponse          `json:"recent_checklists"`
	PopularChecklists      []ChecklistWithStatsResponse `json:"popular_checklists"`
}

// @Summary Checklist with stats response
// @Description Checklist information with usage statistics
// @Tags Checklist
type ChecklistWithStatsResponse struct {
	ChecklistResponse
	CompletionRate    float64 `json:"completion_rate" example:"80.0"`
	OverdueItemsCount int     `json:"overdue_items_count" example:"1"`
	RecentActivity    int     `json:"recent_activity" example:"3"`
	AverageItemAge    float64 `json:"average_item_age" example:"5.2"`
}

// @Summary Bulk check items request
// @Description Request to check/uncheck multiple checklist items
// @Tags Checklist
type BulkCheckItemsRequest struct {
	ItemIDs   []uint `json:"item_ids" validate:"required,min=1" example:"1,2,3"`
	IsChecked bool   `json:"is_checked" example:"true"`
}

// @Summary Convert to card request
// @Description Request to convert checklist item to card
// @Tags Checklist
type ConvertToCardRequest struct {
	ListID         uint    `json:"list_id" validate:"required" example:"1"`
	Position       float64 `json:"position" example:"1024.0"`
	KeepDueDate    bool    `json:"keep_due_date" example:"true"`
	KeepAssignment bool    `json:"keep_assignment" example:"false"`
}

// ToModelCreateRequest converts DTO to models.CreateChecklistRequest
func (r *CreateChecklistRequest) ToModelCreateRequest() models.CreateChecklistRequest {
	return models.CreateChecklistRequest{
		CardID:   r.CardID,
		Name:     r.Name,
		Position: r.Position,
	}
}

// ToModelUpdateRequest converts DTO to models.UpdateChecklistRequest
func (r *UpdateChecklistRequest) ToModelUpdateRequest() models.UpdateChecklistRequest {
	return models.UpdateChecklistRequest{
		Name:     r.Name,
		Position: r.Position,
	}
}

// ToModelCreateItemRequest converts DTO to models.CreateChecklistItemRequest
func (r *CreateChecklistItemRequest) ToModelCreateItemRequest() models.CreateChecklistItemRequest {
	return models.CreateChecklistItemRequest{
		ChecklistID:    r.ChecklistID,
		Name:           r.Name,
		Position:       r.Position,
		DueDate:        r.DueDate,
		AssignedUserID: r.AssignedUserID,
	}
}

// ToModelUpdateItemRequest converts DTO to models.UpdateChecklistItemRequest
func (r *UpdateChecklistItemRequest) ToModelUpdateItemRequest() models.UpdateChecklistItemRequest {
	return models.UpdateChecklistItemRequest{
		Name:           r.Name,
		Position:       r.Position,
		IsChecked:      r.IsChecked,
		DueDate:        r.DueDate,
		AssignedUserID: r.AssignedUserID,
	}
}
