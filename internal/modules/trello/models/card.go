package models

import (
	"time"

	"gorm.io/datatypes"
)

// TrelloCard represents a Trello card entity
type TrelloCard struct {
	ID           uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID     uint           `gorm:"not null;index:idx_trello_cards_tenant_id" json:"tenant_id"`
	WebsiteID    uint           `gorm:"not null;index:idx_trello_cards_website_id" json:"website_id"`
	BoardID      uint           `gorm:"not null;index:idx_trello_cards_board_id" json:"board_id"`
	ListID       uint           `gorm:"not null;index:idx_trello_cards_list_id" json:"list_id"`
	Title        string         `gorm:"type:varchar(512);not null" json:"title"`
	Description  *string        `gorm:"type:text" json:"description,omitempty"`
	Position     float64        `gorm:"type:double;not null" json:"position"`
	DueDate      *time.Time     `json:"due_date,omitempty"`
	StartDate    *time.Time     `json:"start_date,omitempty"`
	DueComplete  bool           `gorm:"default:false;not null" json:"due_complete"`
	IsClosed     bool           `gorm:"default:false;not null" json:"is_closed"`
	IsSubscribed bool           `gorm:"default:false;not null" json:"is_subscribed"`
	CoverType    CoverType      `gorm:"type:varchar(50);default:'none';not null" json:"cover_type"`
	CoverValue   *string        `gorm:"type:varchar(255)" json:"cover_value,omitempty"`
	CoverColor   *string        `gorm:"type:varchar(7)" json:"cover_color,omitempty"`
	CoverSize    CoverSize      `gorm:"type:varchar(50);default:'normal';not null" json:"cover_size"`
	Badges       datatypes.JSON `gorm:"type:json;default:'{}'" json:"badges"`
	CustomFields datatypes.JSON `gorm:"type:json;default:'{}'" json:"custom_fields"`
	CreatedBy    uint           `gorm:"not null;index" json:"created_by"`
	Status       TrelloStatus   `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	CreatedAt    time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Board      *TrelloBoard         `gorm:"foreignKey:BoardID" json:"board,omitempty"`
	List       *TrelloList          `gorm:"foreignKey:ListID" json:"list,omitempty"`
	Members    []TrelloCardMember   `gorm:"foreignKey:CardID" json:"members,omitempty"`
	Labels     []TrelloCardLabel    `gorm:"foreignKey:CardID" json:"labels,omitempty"`
	Checklists []TrelloChecklist    `gorm:"foreignKey:CardID" json:"checklists,omitempty"`
	Comments   []TrelloCardComment  `gorm:"foreignKey:CardID" json:"comments,omitempty"`
	Activities []TrelloCardActivity `gorm:"foreignKey:CardID" json:"activities,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloCard) TableName() string {
	return "trello_cards"
}

// TrelloCardMember represents card member assignments
type TrelloCardMember struct {
	ID         uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID   uint      `gorm:"not null;index:idx_trello_card_members_tenant_id" json:"tenant_id"`
	WebsiteID  uint      `gorm:"not null;index:idx_trello_card_members_website_id" json:"website_id"`
	CardID     uint      `gorm:"not null;index:idx_trello_card_members_card_id" json:"card_id"`
	UserID     uint      `gorm:"not null;index:idx_trello_card_members_user_id" json:"user_id"`
	AssignedBy uint      `gorm:"not null;index" json:"assigned_by"`
	AssignedAt time.Time `gorm:"autoCreateTime" json:"assigned_at"`

	// Relationships
	Card *TrelloCard `gorm:"foreignKey:CardID" json:"card,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloCardMember) TableName() string {
	return "trello_card_members"
}

// TrelloCardLabel represents card-label relationships
type TrelloCardLabel struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint      `gorm:"not null;index:idx_trello_card_labels_tenant_id" json:"tenant_id"`
	WebsiteID uint      `gorm:"not null;index:idx_trello_card_labels_website_id" json:"website_id"`
	CardID    uint      `gorm:"not null;index:idx_trello_card_labels_card_id" json:"card_id"`
	LabelID   uint      `gorm:"not null;index:idx_trello_card_labels_label_id" json:"label_id"`
	AppliedBy uint      `gorm:"not null;index" json:"applied_by"`
	AppliedAt time.Time `gorm:"autoCreateTime" json:"applied_at"`

	// Relationships
	Card  *TrelloCard       `gorm:"foreignKey:CardID" json:"card,omitempty"`
	Label *TrelloBoardLabel `gorm:"foreignKey:LabelID" json:"label,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloCardLabel) TableName() string {
	return "trello_card_labels"
}

// TrelloCardComment represents card comments
type TrelloCardComment struct {
	ID        uint        `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint        `gorm:"not null;index:idx_trello_card_comments_tenant_id" json:"tenant_id"`
	WebsiteID uint        `gorm:"not null;index:idx_trello_card_comments_website_id" json:"website_id"`
	CardID    uint        `gorm:"not null;index:idx_trello_card_comments_card_id" json:"card_id"`
	BoardID   uint        `gorm:"not null;index:idx_trello_card_comments_board_id" json:"board_id"`
	UserID    uint        `gorm:"not null;index:idx_trello_card_comments_user_id" json:"user_id"`
	Content   string      `gorm:"type:text;not null" json:"content"`
	Type      CommentType `gorm:"type:varchar(50);default:'comment';not null" json:"type"`
	CreatedAt time.Time   `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time   `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Card  *TrelloCard  `gorm:"foreignKey:CardID" json:"card,omitempty"`
	Board *TrelloBoard `gorm:"foreignKey:BoardID" json:"board,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloCardComment) TableName() string {
	return "trello_card_comments"
}

// TrelloCardActivity represents card activity logging
type TrelloCardActivity struct {
	ID        uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint           `gorm:"not null;index:idx_trello_card_activities_tenant_id" json:"tenant_id"`
	WebsiteID uint           `gorm:"not null;index:idx_trello_card_activities_website_id" json:"website_id"`
	BoardID   uint           `gorm:"not null;index:idx_trello_card_activities_board_id" json:"board_id"`
	CardID    uint           `gorm:"not null;index:idx_trello_card_activities_card_id" json:"card_id"`
	ListID    *uint          `gorm:"index:idx_trello_card_activities_list_id" json:"list_id,omitempty"`
	UserID    uint           `gorm:"not null;index:idx_trello_card_activities_user_id" json:"user_id"`
	Type      string         `gorm:"type:varchar(50);not null" json:"type"`
	Data      datatypes.JSON `gorm:"type:json;default:'{}'" json:"data"`
	OldValue  datatypes.JSON `gorm:"type:json" json:"old_value,omitempty"`
	NewValue  datatypes.JSON `gorm:"type:json" json:"new_value,omitempty"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"created_at"`

	// Relationships
	Card  *TrelloCard  `gorm:"foreignKey:CardID" json:"card,omitempty"`
	Board *TrelloBoard `gorm:"foreignKey:BoardID" json:"board,omitempty"`
	List  *TrelloList  `gorm:"foreignKey:ListID" json:"list,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloCardActivity) TableName() string {
	return "trello_card_activities"
}
