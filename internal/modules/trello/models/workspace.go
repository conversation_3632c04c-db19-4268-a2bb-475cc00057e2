package models

import (
	"time"

	"gorm.io/datatypes"
)

// TrelloWorkspace represents a Trello workspace entity
type TrelloWorkspace struct {
	ID          uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint           `gorm:"not null;index:idx_trello_workspaces_tenant_id" json:"tenant_id"`
	WebsiteID   uint           `gorm:"not null;index:idx_trello_workspaces_website_id" json:"website_id"`
	Name        string         `gorm:"type:varchar(255);not null" json:"name"`
	Description *string        `gorm:"type:text" json:"description,omitempty"`
	Visibility  VisibilityType `gorm:"type:varchar(50);default:'private';not null" json:"visibility"`
	LogoURL     *string        `gorm:"type:varchar(512)" json:"logo_url,omitempty"`
	WebsiteURL  *string        `gorm:"type:varchar(512)" json:"website_url,omitempty"`
	Settings    datatypes.JSON `gorm:"type:json;default:'{}'" json:"settings"`
	CreatedBy   uint           `gorm:"not null;index" json:"created_by"`
	Status      TrelloStatus   `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	CreatedAt   time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Boards  []TrelloBoard           `gorm:"foreignKey:WorkspaceID" json:"boards,omitempty"`
	Members []TrelloWorkspaceMember `gorm:"foreignKey:WorkspaceID" json:"members,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloWorkspace) TableName() string {
	return "trello_workspaces"
}

// TrelloWorkspaceMember represents workspace membership
type TrelloWorkspaceMember struct {
	ID          uint         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint         `gorm:"not null;index:idx_trello_workspace_members_tenant_id" json:"tenant_id"`
	WebsiteID   uint         `gorm:"not null;index:idx_trello_workspace_members_website_id" json:"website_id"`
	WorkspaceID uint         `gorm:"not null;index:idx_trello_workspace_members_workspace_id" json:"workspace_id"`
	UserID      uint         `gorm:"not null;index:idx_trello_workspace_members_user_id" json:"user_id"`
	Role        MemberRole   `gorm:"type:varchar(50);default:'member';not null" json:"role"`
	InvitedBy   uint         `gorm:"not null;index" json:"invited_by"`
	JoinedAt    time.Time    `gorm:"autoCreateTime" json:"joined_at"`
	Status      MemberStatus `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	CreatedAt   time.Time    `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time    `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Workspace *TrelloWorkspace `gorm:"foreignKey:WorkspaceID" json:"workspace,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloWorkspaceMember) TableName() string {
	return "trello_workspace_members"
}
