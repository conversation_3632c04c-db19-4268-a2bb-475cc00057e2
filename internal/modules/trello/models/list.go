package models

import (
	"time"

	"gorm.io/datatypes"
)

// TrelloList represents a Trello list entity
type TrelloList struct {
	ID           uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID     uint           `gorm:"not null;index:idx_trello_lists_tenant_id" json:"tenant_id"`
	WebsiteID    uint           `gorm:"not null;index:idx_trello_lists_website_id" json:"website_id"`
	BoardID      uint           `gorm:"not null;index:idx_trello_lists_board_id" json:"board_id"`
	Name         string         `gorm:"type:varchar(255);not null" json:"name"`
	Position     float64        `gorm:"type:double;not null" json:"position"`
	IsClosed     bool           `gorm:"default:false;not null" json:"is_closed"`
	IsSubscribed bool           `gorm:"default:false;not null" json:"is_subscribed"`
	Settings     datatypes.JSON `gorm:"type:json;default:'{}'" json:"settings"`
	Status       TrelloStatus   `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	CreatedAt    time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Board *TrelloBoard `gorm:"foreignKey:BoardID" json:"board,omitempty"`
	Cards []TrelloCard `gorm:"foreignKey:ListID" json:"cards,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloList) TableName() string {
	return "trello_lists"
}
