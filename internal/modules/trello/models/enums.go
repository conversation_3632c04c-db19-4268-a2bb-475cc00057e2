package models

import (
	"database/sql/driver"
	"fmt"
)

// VisibilityType represents visibility levels for workspaces and boards
// @Enum private,workspace,public
type VisibilityType string

const (
	VisibilityPrivate   VisibilityType = "private"
	VisibilityWorkspace VisibilityType = "workspace"
	VisibilityPublic    VisibilityType = "public"
)

func (v VisibilityType) String() string {
	return string(v)
}

func (v *VisibilityType) Scan(value interface{}) error {
	if value == nil {
		*v = VisibilityPrivate
		return nil
	}
	if s, ok := value.(string); ok {
		*v = VisibilityType(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*v = VisibilityType(string(b))
		return nil
	}
	return fmt.Errorf("cannot scan %T into VisibilityType", value)
}

func (v VisibilityType) Value() (driver.Value, error) {
	return string(v), nil
}

// TrelloStatus represents status for entities with soft delete support
// @Enum active,inactive,archived,deleted
type TrelloStatus string

const (
	TrelloStatusActive   TrelloStatus = "active"
	TrelloStatusInactive TrelloStatus = "inactive"
	TrelloStatusArchived TrelloStatus = "archived"
	TrelloStatusDeleted  TrelloStatus = "deleted"
)

func (s TrelloStatus) String() string {
	return string(s)
}

func (s *TrelloStatus) Scan(value interface{}) error {
	if value == nil {
		*s = TrelloStatusActive
		return nil
	}
	if str, ok := value.(string); ok {
		*s = TrelloStatus(str)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*s = TrelloStatus(string(b))
		return nil
	}
	return fmt.Errorf("cannot scan %T into TrelloStatus", value)
}

func (s TrelloStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// MemberRole represents roles for workspace and board members
// @Enum owner,admin,member,observer,guest
type MemberRole string

const (
	MemberRoleOwner    MemberRole = "owner"
	MemberRoleAdmin    MemberRole = "admin"
	MemberRoleMember   MemberRole = "member"
	MemberRoleObserver MemberRole = "observer"
	MemberRoleGuest    MemberRole = "guest"
)

func (r MemberRole) String() string {
	return string(r)
}

func (r *MemberRole) Scan(value interface{}) error {
	if value == nil {
		*r = MemberRoleMember
		return nil
	}
	if s, ok := value.(string); ok {
		*r = MemberRole(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*r = MemberRole(string(b))
		return nil
	}
	return fmt.Errorf("cannot scan %T into MemberRole", value)
}

func (r MemberRole) Value() (driver.Value, error) {
	return string(r), nil
}

// MemberStatus represents status for member entities
// @Enum active,inactive,pending,suspended,deleted
type MemberStatus string

const (
	MemberStatusActive    MemberStatus = "active"
	MemberStatusInactive  MemberStatus = "inactive"
	MemberStatusPending   MemberStatus = "pending"
	MemberStatusSuspended MemberStatus = "suspended"
	MemberStatusDeleted   MemberStatus = "deleted"
)

func (s MemberStatus) String() string {
	return string(s)
}

func (s *MemberStatus) Scan(value interface{}) error {
	if value == nil {
		*s = MemberStatusActive
		return nil
	}
	if str, ok := value.(string); ok {
		*s = MemberStatus(str)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*s = MemberStatus(string(b))
		return nil
	}
	return fmt.Errorf("cannot scan %T into MemberStatus", value)
}

func (s MemberStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// BackgroundType represents background types for boards
// @Enum color,image,gradient
type BackgroundType string

const (
	BackgroundColor    BackgroundType = "color"
	BackgroundImage    BackgroundType = "image"
	BackgroundGradient BackgroundType = "gradient"
)

func (t BackgroundType) String() string {
	return string(t)
}

func (t *BackgroundType) Scan(value interface{}) error {
	if value == nil {
		*t = BackgroundColor
		return nil
	}
	if s, ok := value.(string); ok {
		*t = BackgroundType(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*t = BackgroundType(string(b))
		return nil
	}
	return fmt.Errorf("cannot scan %T into BackgroundType", value)
}

func (t BackgroundType) Value() (driver.Value, error) {
	return string(t), nil
}

// CoverType represents cover types for cards
// @Enum none,color,image
type CoverType string

const (
	CoverNone  CoverType = "none"
	CoverColor CoverType = "color"
	CoverImage CoverType = "image"
)

func (t CoverType) String() string {
	return string(t)
}

func (t *CoverType) Scan(value interface{}) error {
	if value == nil {
		*t = CoverNone
		return nil
	}
	if s, ok := value.(string); ok {
		*t = CoverType(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*t = CoverType(string(b))
		return nil
	}
	return fmt.Errorf("cannot scan %T into CoverType", value)
}

func (t CoverType) Value() (driver.Value, error) {
	return string(t), nil
}

// CoverSize represents cover sizes for cards
// @Enum normal,full
type CoverSize string

const (
	CoverSizeNormal CoverSize = "normal"
	CoverSizeFull   CoverSize = "full"
)

func (s CoverSize) String() string {
	return string(s)
}

func (s *CoverSize) Scan(value interface{}) error {
	if value == nil {
		*s = CoverSizeNormal
		return nil
	}
	if str, ok := value.(string); ok {
		*s = CoverSize(str)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*s = CoverSize(string(b))
		return nil
	}
	return fmt.Errorf("cannot scan %T into CoverSize", value)
}

func (s CoverSize) Value() (driver.Value, error) {
	return string(s), nil
}

// CommentType represents types of comments
// @Enum comment,action
type CommentType string

const (
	CommentTypeComment CommentType = "comment"
	CommentTypeAction  CommentType = "action"
)

func (t CommentType) String() string {
	return string(t)
}

func (t *CommentType) Scan(value interface{}) error {
	if value == nil {
		*t = CommentTypeComment
		return nil
	}
	if s, ok := value.(string); ok {
		*t = CommentType(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*t = CommentType(string(b))
		return nil
	}
	return fmt.Errorf("cannot scan %T into CommentType", value)
}

func (t CommentType) Value() (driver.Value, error) {
	return string(t), nil
}
