package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
)

// ChecklistRepository defines the interface for checklist data operations
type ChecklistRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, checklist *models.TrelloChecklist) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloChecklist, error)
	Update(ctx context.Context, tenantID, websiteID, id uint, checklist *models.TrelloChecklist) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error

	// List operations
	ListByCard(ctx context.Context, tenantID, websiteID, cardID uint) ([]models.TrelloChecklist, error)
	ListByCardWithItems(ctx context.Context, tenantID, websiteID, cardID uint) ([]models.TrelloChecklist, error)

	// Checklist-specific operations
	GetWithItems(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloChecklist, error)
	GetByName(ctx context.Context, tenantID, websiteID, cardID uint, name string) (*models.TrelloChecklist, error)

	// Position operations
	GetMaxPosition(ctx context.Context, tenantID, websiteID, cardID uint) (float64, error)
	GetNextPosition(ctx context.Context, tenantID, websiteID, cardID uint, afterPosition *float64) (float64, error)
	UpdatePosition(ctx context.Context, tenantID, websiteID, id uint, position float64) error

	// Progress operations
	UpdateProgress(ctx context.Context, tenantID, websiteID, checklistID uint) error
	GetProgress(ctx context.Context, tenantID, websiteID, checklistID uint) (uint, uint, error) // total, checked

	// Item operations
	CreateItem(ctx context.Context, item *models.TrelloChecklistItem) error
	UpdateItem(ctx context.Context, tenantID, websiteID, itemID uint, item *models.TrelloChecklistItem) error
	DeleteItem(ctx context.Context, tenantID, websiteID, itemID uint) error
	GetItem(ctx context.Context, tenantID, websiteID, itemID uint) (*models.TrelloChecklistItem, error)
	ListItems(ctx context.Context, tenantID, websiteID, checklistID uint) ([]models.TrelloChecklistItem, error)

	// Item position operations
	GetMaxItemPosition(ctx context.Context, tenantID, websiteID, checklistID uint) (float64, error)
	GetNextItemPosition(ctx context.Context, tenantID, websiteID, checklistID uint, afterPosition *float64) (float64, error)
	UpdateItemPosition(ctx context.Context, tenantID, websiteID, itemID uint, position float64) error

	// Item check operations
	CheckItem(ctx context.Context, tenantID, websiteID, itemID, userID uint) error
	UncheckItem(ctx context.Context, tenantID, websiteID, itemID uint) error
	ToggleItem(ctx context.Context, tenantID, websiteID, itemID, userID uint) error

	// Batch operations
	CheckAllItems(ctx context.Context, tenantID, websiteID, checklistID, userID uint) error
	UncheckAllItems(ctx context.Context, tenantID, websiteID, checklistID uint) error

	// Copy operations
	CopyChecklist(ctx context.Context, tenantID, websiteID, sourceChecklistID, targetCardID, userID uint) (*models.TrelloChecklist, error)

	// Count operations
	CountByCard(ctx context.Context, tenantID, websiteID, cardID uint) (int64, error)
	CountItemsByChecklist(ctx context.Context, tenantID, websiteID, checklistID uint) (int64, error)
	CountCheckedItemsByChecklist(ctx context.Context, tenantID, websiteID, checklistID uint) (int64, error)
}
