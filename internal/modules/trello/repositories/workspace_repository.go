package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// WorkspaceRepository defines the interface for workspace data operations
type WorkspaceRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, workspace *models.TrelloWorkspace) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloWorkspace, error)
	Update(ctx context.Context, tenantID, websiteID, id uint, workspace *models.TrelloWorkspace) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error

	// List operations
	List(ctx context.Context, tenantID, websiteID uint, pagination *pagination.CursorPagination) ([]models.TrelloWorkspace, *pagination.CursorResponse, error)
	ListByStatus(ctx context.Context, tenantID, websiteID uint, status models.TrelloStatus, pagination *pagination.CursorPagination) ([]models.TrelloWorkspace, *pagination.CursorResponse, error)

	// Workspace-specific operations
	GetByName(ctx context.Context, tenantID, websiteID uint, name string) (*models.TrelloWorkspace, error)
	GetWithBoards(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloWorkspace, error)
	GetWithMembers(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloWorkspace, error)

	// Member operations
	AddMember(ctx context.Context, member *models.TrelloWorkspaceMember) error
	RemoveMember(ctx context.Context, tenantID, websiteID, workspaceID, userID uint) error
	UpdateMember(ctx context.Context, tenantID, websiteID, workspaceID, userID uint, member *models.TrelloWorkspaceMember) error
	GetMember(ctx context.Context, tenantID, websiteID, workspaceID, userID uint) (*models.TrelloWorkspaceMember, error)
	ListMembers(ctx context.Context, tenantID, websiteID, workspaceID uint) ([]models.TrelloWorkspaceMember, error)
	IsMember(ctx context.Context, tenantID, websiteID, workspaceID, userID uint) (bool, error)
	GetMemberRole(ctx context.Context, tenantID, websiteID, workspaceID, userID uint) (models.MemberRole, error)

	// Search and filter operations
	SearchByName(ctx context.Context, tenantID, websiteID uint, query string, pagination *pagination.CursorPagination) ([]models.TrelloWorkspace, *pagination.CursorResponse, error)
	GetUserWorkspaces(ctx context.Context, tenantID, websiteID, userID uint, pagination *pagination.CursorPagination) ([]models.TrelloWorkspace, *pagination.CursorResponse, error)
}
