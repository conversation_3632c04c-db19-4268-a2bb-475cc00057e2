package mysql

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"gorm.io/gorm"
)

type listRepository struct {
	db *gorm.DB
}

func NewListRepository(db *gorm.DB) repositories.ListRepository {
	return &listRepository{db: db}
}

func (r *listRepository) Create(ctx context.Context, list *models.TrelloList) error {
	return r.db.WithContext(ctx).Create(list).Error
}

func (r *listRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloList, error) {
	var list models.TrelloList
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&list).Error
	if err != nil {
		return nil, err
	}
	return &list, nil
}

func (r *listRepository) Update(ctx context.Context, tenantID, websiteID, id uint, list *models.TrelloList) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Updates(list).Error
}

func (r *listRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloList{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Update("status", models.TrelloStatusDeleted).Error
}

func (r *listRepository) ListByBoard(ctx context.Context, tenantID, websiteID, boardID uint) ([]models.TrelloList, error) {
	var lists []models.TrelloList
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND status != ?",
			tenantID, websiteID, boardID, models.TrelloStatusDeleted).
		Order("position ASC").
		Find(&lists).Error
	return lists, err
}

func (r *listRepository) GetByName(ctx context.Context, tenantID, websiteID, boardID uint, name string) (*models.TrelloList, error) {
	var list models.TrelloList
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND name = ? AND status != ?",
			tenantID, websiteID, boardID, name, models.TrelloStatusDeleted).
		First(&list).Error
	if err != nil {
		return nil, err
	}
	return &list, nil
}

func (r *listRepository) GetWithCards(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloList, error) {
	var list models.TrelloList
	err := r.db.WithContext(ctx).
		Preload("Cards", "status != ?", models.TrelloStatusDeleted).
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&list).Error
	if err != nil {
		return nil, err
	}
	return &list, nil
}

func (r *listRepository) GetNextPosition(ctx context.Context, tenantID, websiteID, boardID uint, afterPosition *float64) (float64, error) {
	// Use timestamp for position to ensure uniqueness and proper ordering
	// With DOUBLE type, we can use large timestamp values without precision issues
	now := time.Now()
	timestamp := float64(now.UnixNano()) / 1e6 // Convert to milliseconds with microsecond precision

	// If afterPosition is specified, ensure our timestamp is greater
	if afterPosition != nil && timestamp <= *afterPosition {
		timestamp = *afterPosition + 1.0
	}

	return timestamp, nil
}

func (r *listRepository) UpdatePosition(ctx context.Context, tenantID, websiteID, id uint, position float64) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloList{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Update("position", position).Error
}

func (r *listRepository) Archive(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloList{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Update("status", models.TrelloStatusArchived).Error
}

func (r *listRepository) Unarchive(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloList{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Update("status", models.TrelloStatusActive).Error
}

func (r *listRepository) GetCardCount(ctx context.Context, tenantID, websiteID, listID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND list_id = ? AND status != ?",
			tenantID, websiteID, listID, models.TrelloStatusDeleted).
		Count(&count).Error
	return count, err
}

func (r *listRepository) MoveToBoard(ctx context.Context, tenantID, websiteID, listID, newBoardID uint, position float64) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloList{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, listID).
		Updates(map[string]interface{}{
			"board_id": newBoardID,
			"position": position,
		}).Error
}

func (r *listRepository) Move(ctx context.Context, tenantID, websiteID, id uint, boardID *uint, position float64) error {
	updates := map[string]interface{}{
		"position": position,
	}
	if boardID != nil {
		updates["board_id"] = *boardID
	}

	return r.db.WithContext(ctx).
		Model(&models.TrelloList{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Updates(updates).Error
}

func (r *listRepository) CountActiveByBoard(ctx context.Context, tenantID, websiteID, boardID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloList{}).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND status = ?", tenantID, websiteID, boardID, models.TrelloStatusActive).
		Count(&count).Error
	return count, err
}

func (r *listRepository) CountByBoard(ctx context.Context, tenantID, websiteID, boardID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloList{}).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND status != ?", tenantID, websiteID, boardID, models.TrelloStatusDeleted).
		Count(&count).Error
	return count, err
}

func (r *listRepository) GetMaxPosition(ctx context.Context, tenantID, websiteID, boardID uint) (float64, error) {
	var maxPosition float64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloList{}).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND status != ?", tenantID, websiteID, boardID, models.TrelloStatusDeleted).
		Select("COALESCE(MAX(position), 0)").
		Scan(&maxPosition).Error
	return maxPosition, err
}

func (r *listRepository) ListByBoardWithCards(ctx context.Context, tenantID, websiteID, boardID uint) ([]models.TrelloList, error) {
	var lists []models.TrelloList
	err := r.db.WithContext(ctx).
		Preload("Cards", func(db *gorm.DB) *gorm.DB {
			return db.Where("status != ?", models.TrelloStatusDeleted).Order("position ASC")
		}).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND status != ?",
			tenantID, websiteID, boardID, models.TrelloStatusDeleted).
		Order("position ASC").
		Find(&lists).Error
	return lists, err
}

func (r *listRepository) ListByStatus(ctx context.Context, tenantID, websiteID, boardID uint, status models.TrelloStatus) ([]models.TrelloList, error) {
	var lists []models.TrelloList
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND status = ?", tenantID, websiteID, boardID, status).
		Order("position ASC").
		Find(&lists).Error
	return lists, err
}

// UpdatePositions updates multiple list positions in a transaction
func (r *listRepository) UpdatePositions(ctx context.Context, tenantID, websiteID, boardID uint, positions map[uint]float64) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for listID, position := range positions {
			if err := tx.Model(&models.TrelloList{}).
				Where("tenant_id = ? AND website_id = ? AND id = ? AND board_id = ?", tenantID, websiteID, listID, boardID).
				Update("position", position).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
