package mysql

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

type cardRepository struct {
	db *gorm.DB
}

func NewCardRepository(db *gorm.DB) repositories.CardRepository {
	return &cardRepository{db: db}
}

func (r *cardRepository) Create(ctx context.Context, card *models.TrelloCard) error {
	return r.db.WithContext(ctx).Create(card).Error
}

func (r *cardRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloCard, error) {
	var card models.TrelloCard
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&card).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

func (r *cardRepository) Update(ctx context.Context, tenantID, websiteID, id uint, card *models.TrelloCard) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Updates(card).Error
}

func (r *cardRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Update("status", models.TrelloStatusDeleted).Error
}

func (r *cardRepository) ListByList(ctx context.Context, tenantID, websiteID, listID uint, pag *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error) {
	var cards []models.TrelloCard
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND list_id = ? AND status != ?", tenantID, websiteID, listID, models.TrelloStatusDeleted).
		Order("position ASC")

	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&cards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(cards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		cards = cards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", cards[len(cards)-1].ID)
	}

	return cards, response, nil
}

func (r *cardRepository) ListByBoard(ctx context.Context, tenantID, websiteID, boardID uint, pag *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error) {
	var cards []models.TrelloCard
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND status != ?", tenantID, websiteID, boardID, models.TrelloStatusDeleted).
		Order("updated_at DESC")

	if pag.Cursor != "" {
		query = query.Where("id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&cards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(cards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		cards = cards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", cards[len(cards)-1].ID)
	}

	return cards, response, nil
}

func (r *cardRepository) GetDetail(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloCard, error) {
	var card models.TrelloCard
	err := r.db.WithContext(ctx).
		Preload("Members", "status != ?", models.MemberStatusDeleted).
		Preload("Labels", "status != ?", models.TrelloStatusDeleted).
		Preload("Checklists", "status != ?", models.TrelloStatusDeleted).
		Preload("Checklists.Items", "status != ?", models.TrelloStatusDeleted).
		Preload("Comments", "status != ?", models.TrelloStatusDeleted).
		Preload("Activities").
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&card).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

func (r *cardRepository) GetNextPosition(ctx context.Context, tenantID, websiteID, listID uint, afterPosition *float64) (float64, error) {
	// Use timestamp for position to ensure uniqueness and proper ordering
	// With DOUBLE type, we can use large timestamp values without precision issues
	now := time.Now()
	timestamp := float64(now.UnixNano()) / 1e6 // Convert to milliseconds with microsecond precision

	// If afterPosition is specified, ensure our timestamp is greater
	if afterPosition != nil && timestamp <= *afterPosition {
		timestamp = *afterPosition + 1.0
	}

	return timestamp, nil
}

func (r *cardRepository) MoveToList(ctx context.Context, tenantID, websiteID, cardID, newListID uint, position float64) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, cardID).
		Updates(map[string]interface{}{
			"list_id":  newListID,
			"position": position,
		}).Error
}

func (r *cardRepository) Move(ctx context.Context, tenantID, websiteID, id uint, listID *uint, position float64) error {
	updates := map[string]interface{}{
		"position": position,
	}
	if listID != nil {
		updates["list_id"] = *listID
	}

	return r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Updates(updates).Error
}

func (r *cardRepository) GetMember(ctx context.Context, tenantID, websiteID, cardID, userID uint) (*models.TrelloCardMember, error) {
	var member models.TrelloCardMember
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ? AND user_id = ?", tenantID, cardID, userID).
		First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

func (r *cardRepository) GetLabel(ctx context.Context, tenantID, websiteID, cardID, labelID uint) (*models.TrelloCardLabel, error) {
	var label models.TrelloCardLabel
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ? AND label_id = ?", tenantID, cardID, labelID).
		First(&label).Error
	if err != nil {
		return nil, err
	}
	return &label, nil
}

func (r *cardRepository) CountActiveByList(ctx context.Context, tenantID, websiteID, listID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND list_id = ? AND status = ?", tenantID, websiteID, listID, models.TrelloStatusActive).
		Count(&count).Error
	return count, err
}

func (r *cardRepository) AssignMember(ctx context.Context, member *models.TrelloCardMember) error {
	return r.db.WithContext(ctx).Create(member).Error
}

func (r *cardRepository) UnassignMember(ctx context.Context, tenantID, websiteID, cardID, userID uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ? AND user_id = ?", tenantID, cardID, userID).
		Delete(&models.TrelloCardMember{}).Error
}

func (r *cardRepository) ListMembers(ctx context.Context, tenantID, websiteID, cardID uint) ([]models.TrelloCardMember, error) {
	var members []models.TrelloCardMember
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ?", tenantID, cardID).
		Order("assigned_at ASC").
		Find(&members).Error
	return members, err
}

func (r *cardRepository) ApplyLabel(ctx context.Context, label *models.TrelloCardLabel) error {
	return r.db.WithContext(ctx).Create(label).Error
}

func (r *cardRepository) RemoveLabel(ctx context.Context, tenantID, websiteID, cardID, labelID uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ? AND label_id = ?", tenantID, cardID, labelID).
		Delete(&models.TrelloCardLabel{}).Error
}

func (r *cardRepository) ListLabels(ctx context.Context, tenantID, websiteID, cardID uint) ([]models.TrelloCardLabel, error) {
	var labels []models.TrelloCardLabel
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ?", tenantID, cardID).
		Order("applied_at ASC").
		Find(&labels).Error
	return labels, err
}

func (r *cardRepository) GetLabels(ctx context.Context, tenantID, websiteID, cardID uint) ([]models.TrelloCardLabel, error) {
	var labels []models.TrelloCardLabel
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ?", tenantID, cardID).
		Order("applied_at ASC").
		Find(&labels).Error
	return labels, err
}

func (r *cardRepository) GetMaxPosition(ctx context.Context, tenantID, websiteID, listID uint) (float64, error) {
	var maxPosition float64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND list_id = ? AND status != ?", tenantID, websiteID, listID, models.TrelloStatusDeleted).
		Select("COALESCE(MAX(position), 0)").
		Scan(&maxPosition).Error
	return maxPosition, err
}

func (r *cardRepository) GetMembers(ctx context.Context, tenantID, websiteID, cardID uint) ([]models.TrelloCardMember, error) {
	var members []models.TrelloCardMember
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ?", tenantID, cardID).
		Order("assigned_at ASC").
		Find(&members).Error
	return members, err
}

func (r *cardRepository) CreateComment(ctx context.Context, comment *models.TrelloCardComment) error {
	return r.db.WithContext(ctx).Create(comment).Error
}

func (r *cardRepository) GetComment(ctx context.Context, tenantID, websiteID, commentID uint) (*models.TrelloCardComment, error) {
	var comment models.TrelloCardComment
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, commentID).
		First(&comment).Error
	if err != nil {
		return nil, err
	}
	return &comment, nil
}

func (r *cardRepository) DeleteComment(ctx context.Context, tenantID, websiteID, commentID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloCardComment{}).
		Where("tenant_id = ? AND id = ?", tenantID, commentID).
		Update("status", models.TrelloStatusDeleted).Error
}

func (r *cardRepository) Archive(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Update("status", models.TrelloStatusArchived).Error
}

func (r *cardRepository) Unarchive(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Update("status", models.TrelloStatusActive).Error
}

func (r *cardRepository) Search(ctx context.Context, tenantID, websiteID, boardID uint, query string, pag *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error) {
	var cards []models.TrelloCard
	dbQuery := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND (title LIKE ? OR description LIKE ?) AND status != ?",
			tenantID, websiteID, boardID, "%"+query+"%", "%"+query+"%", models.TrelloStatusDeleted).
		Order("updated_at DESC")

	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("id < ?", pag.Cursor)
	}
	dbQuery = dbQuery.Limit(int(pag.Limit) + 1)

	if err := dbQuery.Find(&cards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(cards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		cards = cards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", cards[len(cards)-1].ID)
	}

	return cards, response, nil
}

func (r *cardRepository) CreateActivity(ctx context.Context, activity *models.TrelloCardActivity) error {
	return r.db.WithContext(ctx).Create(activity).Error
}

func (r *cardRepository) CountByBoard(ctx context.Context, tenantID, websiteID, boardID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND status != ?", tenantID, websiteID, boardID, models.TrelloStatusDeleted).
		Count(&count).Error
	return count, err
}

func (r *cardRepository) CountByList(ctx context.Context, tenantID, websiteID, listID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND list_id = ? AND status != ?", tenantID, websiteID, listID, models.TrelloStatusDeleted).
		Count(&count).Error
	return count, err
}

func (r *cardRepository) GetCardsAssignedToUser(ctx context.Context, tenantID, websiteID, userID uint, pag *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error) {
	var cards []models.TrelloCard
	query := r.db.WithContext(ctx).
		Joins("JOIN trello_card_members ON trello_cards.id = trello_card_members.card_id").
		Where("trello_cards.tenant_id = ? AND trello_cards.website_id = ? AND trello_card_members.user_id = ? AND trello_cards.status != ?",
			tenantID, websiteID, userID, models.TrelloStatusDeleted).
		Order("trello_cards.updated_at DESC")

	if pag.Cursor != "" {
		query = query.Where("trello_cards.id < ?", pag.Cursor)
	}
	query = query.Limit(pag.Limit + 1)

	if err := query.Find(&cards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(cards) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		cards = cards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", cards[len(cards)-1].ID)
	}

	return cards, response, nil
}

func (r *cardRepository) GetCardsDueBy(ctx context.Context, tenantID, websiteID, boardID uint, dueDate time.Time, pag *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error) {
	var cards []models.TrelloCard
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND due_date <= ? AND status != ?",
			tenantID, websiteID, boardID, dueDate, models.TrelloStatusDeleted).
		Order("due_date ASC")

	if pag.Cursor != "" {
		query = query.Where("id < ?", pag.Cursor)
	}
	query = query.Limit(pag.Limit + 1)

	if err := query.Find(&cards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(cards) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		cards = cards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", cards[len(cards)-1].ID)
	}

	return cards, response, nil
}

func (r *cardRepository) GetOverdueCards(ctx context.Context, tenantID, websiteID, boardID uint, pag *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error) {
	var cards []models.TrelloCard
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND due_date < NOW() AND status != ?",
			tenantID, websiteID, boardID, models.TrelloStatusDeleted).
		Order("due_date ASC")

	if pag.Cursor != "" {
		query = query.Where("id < ?", pag.Cursor)
	}
	query = query.Limit(pag.Limit + 1)

	if err := query.Find(&cards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(cards) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		cards = cards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", cards[len(cards)-1].ID)
	}

	return cards, response, nil
}

// GetWithActivities retrieves a card with its activities
func (r *cardRepository) GetWithActivities(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloCard, error) {
	var card models.TrelloCard
	err := r.db.WithContext(ctx).
		Preload("Activities", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at DESC")
		}).
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&card).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

// GetWithDetails retrieves a card with all its details
func (r *cardRepository) GetWithDetails(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloCard, error) {
	var card models.TrelloCard
	err := r.db.WithContext(ctx).
		Preload("Members", "status != ?", models.MemberStatusDeleted).
		Preload("Labels", "status != ?", models.TrelloStatusDeleted).
		Preload("Checklists", "status != ?", models.TrelloStatusDeleted).
		Preload("Checklists.Items", "status != ?", models.TrelloStatusDeleted).
		Preload("Comments", "status != ?", models.TrelloStatusDeleted).
		Preload("Activities").
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&card).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

// GetWithMembers retrieves a card with its members
func (r *cardRepository) GetWithMembers(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloCard, error) {
	var card models.TrelloCard
	err := r.db.WithContext(ctx).
		Preload("Members", "status != ?", models.MemberStatusDeleted).
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&card).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

// GetWithLabels retrieves a card with its labels
func (r *cardRepository) GetWithLabels(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloCard, error) {
	var card models.TrelloCard
	err := r.db.WithContext(ctx).
		Preload("Labels", "status != ?", models.TrelloStatusDeleted).
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&card).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

// GetWithChecklists retrieves a card with its checklists
func (r *cardRepository) GetWithChecklists(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloCard, error) {
	var card models.TrelloCard
	err := r.db.WithContext(ctx).
		Preload("Checklists", func(db *gorm.DB) *gorm.DB {
			return db.Where("status != ?", models.TrelloStatusDeleted).Order("position ASC")
		}).
		Preload("Checklists.Items", func(db *gorm.DB) *gorm.DB {
			return db.Where("status != ?", models.TrelloStatusDeleted).Order("position ASC")
		}).
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&card).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

// GetWithComments retrieves a card with its comments
func (r *cardRepository) GetWithComments(ctx context.Context, tenantID, websiteID, id uint) (*models.TrelloCard, error) {
	var card models.TrelloCard
	err := r.db.WithContext(ctx).
		Preload("Comments", func(db *gorm.DB) *gorm.DB {
			return db.Where("status != ?", models.TrelloStatusDeleted).Order("created_at DESC")
		}).
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?", tenantID, websiteID, id, models.TrelloStatusDeleted).
		First(&card).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

// ListByStatus retrieves cards by status in a list
func (r *cardRepository) ListByStatus(ctx context.Context, tenantID, websiteID, listID uint, status models.TrelloStatus, pag *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error) {
	var cards []models.TrelloCard
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND list_id = ? AND status = ?", tenantID, websiteID, listID, status).
		Order("position ASC")

	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&cards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(cards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		cards = cards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", cards[len(cards)-1].ID)
	}

	return cards, response, nil
}

// UpdatePosition updates the position of a card
func (r *cardRepository) UpdatePosition(ctx context.Context, tenantID, websiteID, id uint, position float64) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloCard{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Update("position", position).Error
}

// UpdatePositions updates multiple card positions
func (r *cardRepository) UpdatePositions(ctx context.Context, tenantID, websiteID, listID uint, positions map[uint]float64) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for cardID, position := range positions {
			if err := tx.Model(&models.TrelloCard{}).
				Where("tenant_id = ? AND website_id = ? AND id = ? AND list_id = ?", tenantID, websiteID, cardID, listID).
				Update("position", position).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// MoveBatch moves multiple cards in a batch
func (r *cardRepository) MoveBatch(ctx context.Context, tenantID, websiteID uint, moves []struct {
	CardID, ListID uint
	Position       float64
}) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, move := range moves {
			if err := tx.Model(&models.TrelloCard{}).
				Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, move.CardID).
				Updates(map[string]interface{}{
					"list_id":  move.ListID,
					"position": move.Position,
				}).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// IsMemberAssigned checks if a member is assigned to a card
func (r *cardRepository) IsMemberAssigned(ctx context.Context, tenantID, websiteID, cardID, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloCardMember{}).
		Where("tenant_id = ? AND card_id = ? AND user_id = ?", tenantID, cardID, userID).
		Count(&count).Error
	return count > 0, err
}

// IsLabelApplied checks if a label is applied to a card
func (r *cardRepository) IsLabelApplied(ctx context.Context, tenantID, websiteID, cardID, labelID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloCardLabel{}).
		Where("tenant_id = ? AND card_id = ? AND label_id = ?", tenantID, cardID, labelID).
		Count(&count).Error
	return count > 0, err
}

// UpdateComment updates a comment
func (r *cardRepository) UpdateComment(ctx context.Context, tenantID, websiteID, commentID uint, comment *models.TrelloCardComment) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloCardComment{}).
		Where("tenant_id = ? AND id = ?", tenantID, commentID).
		Updates(comment).Error
}

// ListComments with pagination support
func (r *cardRepository) ListComments(ctx context.Context, tenantID, websiteID, cardID uint, pag *pagination.CursorPagination) ([]models.TrelloCardComment, *pagination.CursorResponse, error) {
	var comments []models.TrelloCardComment
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ? AND status != ?", tenantID, cardID, models.TrelloStatusDeleted).
		Order("created_at DESC")

	if pag.Cursor != "" {
		query = query.Where("id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&comments).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(comments) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		comments = comments[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", comments[len(comments)-1].ID)
	}

	return comments, response, nil
}

// ListActivities with pagination support
func (r *cardRepository) ListActivities(ctx context.Context, tenantID, websiteID, cardID uint, pag *pagination.CursorPagination) ([]models.TrelloCardActivity, *pagination.CursorResponse, error) {
	var activities []models.TrelloCardActivity
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ?", tenantID, cardID).
		Order("created_at DESC")

	if pag.Cursor != "" {
		query = query.Where("id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&activities).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(activities) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		activities = activities[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", activities[len(activities)-1].ID)
	}

	return activities, response, nil
}

// ListBoardActivities lists activities for an entire board
func (r *cardRepository) ListBoardActivities(ctx context.Context, tenantID, websiteID, boardID uint, pag *pagination.CursorPagination) ([]models.TrelloCardActivity, *pagination.CursorResponse, error) {
	var activities []models.TrelloCardActivity
	query := r.db.WithContext(ctx).
		Joins("JOIN trello_cards ON trello_card_activities.card_id = trello_cards.id").
		Where("trello_card_activities.tenant_id = ? AND trello_cards.website_id = ? AND trello_cards.board_id = ?", tenantID, websiteID, boardID).
		Order("trello_card_activities.created_at DESC")

	if pag.Cursor != "" {
		query = query.Where("trello_card_activities.id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&activities).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(activities) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		activities = activities[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", activities[len(activities)-1].ID)
	}

	return activities, response, nil
}

// SearchByTitle searches cards by title
func (r *cardRepository) SearchByTitle(ctx context.Context, tenantID, websiteID, boardID uint, title string, pag *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error) {
	var cards []models.TrelloCard
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND board_id = ? AND title LIKE ? AND status != ?",
			tenantID, websiteID, boardID, "%"+title+"%", models.TrelloStatusDeleted).
		Order("updated_at DESC")

	if pag.Cursor != "" {
		query = query.Where("id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&cards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(cards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		cards = cards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", cards[len(cards)-1].ID)
	}

	return cards, response, nil
}
