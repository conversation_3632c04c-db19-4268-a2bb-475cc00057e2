package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// tenantMembershipService implements TenantMembershipService interface
type tenantMembershipService struct {
	membershipRepo repositories.TenantMembershipRepository
	logger         utils.Logger
}

// NewTenantMembershipService creates a new tenant membership service
func NewTenantMembershipService(
	membershipRepo repositories.TenantMembershipRepository,
	logger utils.Logger,
) TenantMembershipService {
	return &tenantMembershipService{
		membershipRepo: membershipRepo,
		logger:         logger,
	}
}

// AddTenantMember adds a user to a tenant
func (s *tenantMembershipService) AddTenantMember(ctx context.Context, input AddTenantMemberInput) (*models.TenantMembership, error) {
	s.logger.WithContext(ctx).Info("Adding user to tenant", map[string]interface{}{
		"user_id":   input.UserID,
		"tenant_id": input.TenantID,
	})

	// Validate input
	if err := s.validateAddTenantMemberInput(ctx, input); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to validate add user to tenant input")
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Check if user is already a member of the tenant
	existingMembership, err := s.membershipRepo.GetByUserAndTenant(ctx, input.UserID, input.TenantID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to check existing membership")
		return nil, fmt.Errorf("failed to check existing membership: %w", err)
	}

	if existingMembership != nil {
		// If membership exists but is deleted, update it instead of creating new one
		if existingMembership.Status == models.TenantMembershipStatusDeleted {
			existingMembership.Status = models.TenantMembershipStatusActive
			if input.Status != "" {
				existingMembership.Status = input.Status
			}
			if input.LocalUsername != nil {
				existingMembership.LocalUsername = input.LocalUsername
			}
			if input.DisplayName != nil {
				existingMembership.DisplayName = input.DisplayName
			}
			if input.InvitedBy != nil {
				existingMembership.InvitedBy = input.InvitedBy
			}
			existingMembership.UpdatedAt = time.Now()
			existingMembership.UpdateActivity()

			if err := s.membershipRepo.Update(ctx, existingMembership); err != nil {
				s.logger.WithContext(ctx).WithError(err).Error("Failed to update existing membership")
				return nil, fmt.Errorf("failed to update existing membership: %w", err)
			}

			s.logger.WithContext(ctx).Info("Successfully reactivated user membership", map[string]interface{}{
				"user_id":       input.UserID,
				"tenant_id":     input.TenantID,
				"membership_id": existingMembership.ID,
			})

			return existingMembership, nil
		}

		// User is already an active member
		return nil, fmt.Errorf("user is already a member of the tenant")
	}

	// Validate local username uniqueness if provided
	if input.LocalUsername != nil {
		exists, err := s.membershipRepo.LocalUsernameExists(ctx, input.TenantID, *input.LocalUsername)
		if err != nil {
			s.logger.WithContext(ctx).WithError(err).Error("Failed to check local username uniqueness")
			return nil, fmt.Errorf("failed to check local username uniqueness: %w", err)
		}
		if exists {
			return nil, fmt.Errorf("local username already exists in tenant")
		}
	}

	// Create new membership
	membership := &models.TenantMembership{
		UserID:        input.UserID,
		TenantID:      input.TenantID,
		Status:        models.TenantMembershipStatusActive,
		LocalUsername: input.LocalUsername,
		DisplayName:   input.DisplayName,
		InvitedBy:     input.InvitedBy,
		JoinedAt:      time.Now(),
	}

	// Set custom status if provided
	if input.Status != "" {
		membership.Status = input.Status
	}

	// Set invitation acceptance time if status is active
	if membership.Status == models.TenantMembershipStatusActive {
		now := time.Now()
		membership.InvitationAcceptedAt = &now
	}

	// Update activity
	membership.UpdateActivity()

	if err := s.membershipRepo.Create(ctx, membership); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to create membership")
		return nil, fmt.Errorf("failed to create membership: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully added user to tenant", map[string]interface{}{
		"user_id":       input.UserID,
		"tenant_id":     input.TenantID,
		"membership_id": membership.ID,
		"status":        membership.Status,
	})

	return membership, nil
}

// RemoveTenantMember removes a user from a tenant
func (s *tenantMembershipService) RemoveTenantMember(ctx context.Context, tenantID, userID uint) error {
	s.logger.WithContext(ctx).Info("Removing user from tenant", map[string]interface{}{
		"user_id":   userID,
		"tenant_id": tenantID,
	})

	// Validate input
	if userID == 0 || tenantID == 0 {
		return fmt.Errorf("user ID and tenant ID are required")
	}

	// Get membership
	membership, err := s.membershipRepo.GetByUserAndTenant(ctx, userID, tenantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("membership not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get membership")
		return fmt.Errorf("failed to get membership: %w", err)
	}

	// Check if already deleted
	if membership.Status == models.TenantMembershipStatusDeleted {
		return fmt.Errorf("membership is already deleted")
	}

	// Update status to deleted (soft delete)
	if err := s.membershipRepo.UpdateStatus(ctx, membership.ID, models.TenantMembershipStatusDeleted); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to update membership status to deleted")
		return fmt.Errorf("failed to update membership status: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully removed user from tenant", map[string]interface{}{
		"user_id":       userID,
		"tenant_id":     tenantID,
		"membership_id": membership.ID,
	})

	return nil
}

// UpdateMemberRole updates the role of a user in a tenant
func (s *tenantMembershipService) UpdateMemberRole(ctx context.Context, tenantID, userID uint, input UpdateMemberRoleInput) (*models.TenantMembership, error) {
	s.logger.WithContext(ctx).Info("Updating membership role", map[string]interface{}{
		"user_id":   userID,
		"tenant_id": tenantID,
		"role":      input.Role,
	})

	// Validate input
	if userID == 0 || tenantID == 0 {
		return nil, fmt.Errorf("user ID and tenant ID are required")
	}

	if input.Role == "" {
		return nil, fmt.Errorf("role is required")
	}

	// Get membership
	membership, err := s.membershipRepo.GetByUserAndTenant(ctx, userID, tenantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("membership not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get membership")
		return nil, fmt.Errorf("failed to get membership: %w", err)
	}

	// Check if membership is deleted
	if membership.Status == models.TenantMembershipStatusDeleted {
		return nil, fmt.Errorf("cannot update role for deleted membership")
	}

	// Update membership fields
	if input.LocalUsername != nil {
		membership.LocalUsername = input.LocalUsername
	}
	if input.DisplayName != nil {
		membership.DisplayName = input.DisplayName
	}

	// Update activity
	membership.UpdateActivity()

	// NOTE: Role field is not implemented in TenantMembership model yet
	// This should be implemented when the role system is added
	s.logger.WithContext(ctx).Warn("Role update requested but role field not implemented in TenantMembership model", map[string]interface{}{
		"user_id":        userID,
		"tenant_id":      tenantID,
		"requested_role": input.Role,
	})

	// Update membership
	if err := s.membershipRepo.Update(ctx, membership); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to update membership")
		return nil, fmt.Errorf("failed to update membership: %w", err)
	}

	return membership, nil
}

// UpdateMembershipStatus updates the status of a user's membership
func (s *tenantMembershipService) UpdateMembershipStatus(ctx context.Context, tenantID, userID uint, status models.TenantMembershipStatus) error {
	s.logger.WithContext(ctx).Info("Updating membership status", map[string]interface{}{
		"user_id":   userID,
		"tenant_id": tenantID,
		"status":    status,
	})

	// Validate input
	if userID == 0 || tenantID == 0 {
		return fmt.Errorf("user ID and tenant ID are required")
	}

	if err := s.validateMembershipStatus(status); err != nil {
		return fmt.Errorf("invalid status: %w", err)
	}

	// Get membership
	membership, err := s.membershipRepo.GetByUserAndTenant(ctx, userID, tenantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("membership not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get membership")
		return fmt.Errorf("failed to get membership: %w", err)
	}

	// Check if already at the desired status
	if membership.Status == status {
		return fmt.Errorf("membership is already at the desired status")
	}

	// Update status
	if err := s.membershipRepo.UpdateStatus(ctx, membership.ID, status); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to update membership status")
		return fmt.Errorf("failed to update membership status: %w", err)
	}

	// Update activity if status is active
	if status == models.TenantMembershipStatusActive {
		if err := s.membershipRepo.UpdateActivity(ctx, membership.ID); err != nil {
			s.logger.WithContext(ctx).WithError(err).Warn("Failed to update activity after status change")
		}
	}

	s.logger.WithContext(ctx).Info("Successfully updated membership status", map[string]interface{}{
		"user_id":       userID,
		"tenant_id":     tenantID,
		"membership_id": membership.ID,
		"old_status":    membership.Status,
		"new_status":    status,
	})

	return nil
}

// GetUserMemberships retrieves all memberships for a user
func (s *tenantMembershipService) GetUserMemberships(ctx context.Context, userID uint) ([]models.TenantMembership, error) {
	s.logger.WithContext(ctx).Info("Getting user memberships", map[string]interface{}{
		"user_id": userID,
	})

	// Validate input
	if userID == 0 {
		return nil, fmt.Errorf("user ID is required")
	}

	memberships, err := s.membershipRepo.GetByUserID(ctx, userID)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get user memberships")
		return nil, fmt.Errorf("failed to get user memberships: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully retrieved user memberships", map[string]interface{}{
		"user_id": userID,
		"count":   len(memberships),
	})

	return memberships, nil
}

// GetTenantMembers retrieves all members of a tenant
func (s *tenantMembershipService) GetTenantMembers(ctx context.Context, tenantID uint, filter *TenantMembershipFilter) (*TenantMembershipListResponse, error) {
	// Handle nil filter
	if filter == nil {
		filter = &TenantMembershipFilter{}
	}

	// Set tenant ID in filter
	filter.TenantID = tenantID

	// Extract pagination from filter
	pagination := filter.Pagination
	s.logger.WithContext(ctx).Info("Getting tenant members", map[string]interface{}{
		"tenant_id": tenantID,
	})

	// Validate input
	if tenantID == 0 {
		return nil, fmt.Errorf("tenant ID is required")
	}

	memberships, paginationResponse, err := s.membershipRepo.GetByTenantID(ctx, tenantID, pagination)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get tenant members")
		return nil, fmt.Errorf("failed to get tenant members: %w", err)
	}

	// Count total members
	totalMembers, err := s.membershipRepo.CountByTenant(ctx, tenantID)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Warn("Failed to count total members")
		totalMembers = 0
	}

	response := &TenantMembershipListResponse{
		Memberships: memberships,
		Pagination:  paginationResponse,
		Total:       totalMembers,
	}

	s.logger.WithContext(ctx).Info("Successfully retrieved tenant members", map[string]interface{}{
		"tenant_id": tenantID,
		"count":     len(memberships),
		"total":     totalMembers,
	})

	return response, nil
}

// GetActiveMemberships retrieves active memberships for a user
func (s *tenantMembershipService) GetActiveMemberships(ctx context.Context, userID uint) ([]models.TenantMembership, error) {
	s.logger.WithContext(ctx).Info("Getting active memberships for user", map[string]interface{}{
		"user_id": userID,
	})

	// Validate input
	if userID == 0 {
		return nil, fmt.Errorf("user ID is required")
	}

	// Get all memberships for user
	allMemberships, err := s.membershipRepo.GetByUserID(ctx, userID)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get user memberships")
		return nil, fmt.Errorf("failed to get user memberships: %w", err)
	}

	// Filter active memberships
	activeMemberships := make([]models.TenantMembership, 0)
	for _, membership := range allMemberships {
		if membership.Status == models.TenantMembershipStatusActive {
			activeMemberships = append(activeMemberships, membership)
		}
	}

	s.logger.WithContext(ctx).Info("Successfully retrieved active memberships", map[string]interface{}{
		"user_id":      userID,
		"total_count":  len(allMemberships),
		"active_count": len(activeMemberships),
	})

	return activeMemberships, nil
}

// validateAddTenantMemberInput validates the input for adding a user to a tenant
func (s *tenantMembershipService) validateAddTenantMemberInput(ctx context.Context, input AddTenantMemberInput) error {
	if input.UserID == 0 {
		return fmt.Errorf("user ID is required")
	}

	if input.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}

	if input.Status != "" {
		if err := s.validateMembershipStatus(input.Status); err != nil {
			return fmt.Errorf("invalid status: %w", err)
		}
	}

	return nil
}

// validateMembershipStatus validates the membership status
func (s *tenantMembershipService) validateMembershipStatus(status models.TenantMembershipStatus) error {
	validStatuses := []models.TenantMembershipStatus{
		models.TenantMembershipStatusActive,
		models.TenantMembershipStatusInactive,
		models.TenantMembershipStatusSuspended,
		models.TenantMembershipStatusPending,
		models.TenantMembershipStatusDeleted,
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return nil
		}
	}

	return fmt.Errorf("invalid membership status: %s", status)
}

// GetMembership retrieves a specific membership
func (s *tenantMembershipService) GetMembership(ctx context.Context, tenantID, userID uint) (*models.TenantMembership, error) {
	s.logger.WithContext(ctx).Info("Getting membership", map[string]interface{}{
		"user_id":   userID,
		"tenant_id": tenantID,
	})

	// Validate input
	if userID == 0 || tenantID == 0 {
		return nil, fmt.Errorf("user ID and tenant ID are required")
	}

	membership, err := s.membershipRepo.GetByUserAndTenant(ctx, userID, tenantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("membership not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get membership")
		return nil, fmt.Errorf("failed to get membership: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully retrieved membership", map[string]interface{}{
		"user_id":       userID,
		"tenant_id":     tenantID,
		"membership_id": membership.ID,
		"status":        membership.Status,
	})

	return membership, nil
}

// GetMembershipStats retrieves membership statistics
func (s *tenantMembershipService) GetMembershipStats(ctx context.Context, tenantID uint) (*TenantMembershipStats, error) {
	s.logger.WithContext(ctx).Info("Getting membership statistics", map[string]interface{}{
		"tenant_id": tenantID,
	})

	// Validate input
	if tenantID == 0 {
		return nil, fmt.Errorf("tenant ID is required")
	}

	// Get all memberships for tenant
	memberships, _, err := s.membershipRepo.GetByTenantID(ctx, tenantID, nil)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get tenant memberships")
		return nil, fmt.Errorf("failed to get tenant memberships: %w", err)
	}

	// Calculate statistics
	stats := &TenantMembershipStats{
		StatusDistribution: make(map[models.TenantMembershipStatus]int64),
		RecentJoins:        make([]models.TenantMembership, 0),
		RecentActivity:     make([]models.TenantMembership, 0),
	}

	for _, membership := range memberships {
		stats.TotalMembers++
		stats.StatusDistribution[membership.Status]++

		switch membership.Status {
		case models.TenantMembershipStatusActive:
			stats.ActiveMembers++
		case models.TenantMembershipStatusInactive:
			stats.InactiveMembers++
		case models.TenantMembershipStatusSuspended:
			stats.SuspendedMembers++
		case models.TenantMembershipStatusPending:
			stats.PendingMembers++
		case models.TenantMembershipStatusDeleted:
			stats.DeletedMembers++
		}

		// Add to recent joins (last 10)
		if len(stats.RecentJoins) < 10 {
			stats.RecentJoins = append(stats.RecentJoins, membership)
		}

		// Add to recent activity (last 10)
		if len(stats.RecentActivity) < 10 {
			stats.RecentActivity = append(stats.RecentActivity, membership)
		}
	}

	s.logger.WithContext(ctx).Info("Successfully retrieved membership statistics", map[string]interface{}{
		"tenant_id":      tenantID,
		"total_members":  stats.TotalMembers,
		"active_members": stats.ActiveMembers,
	})

	return stats, nil
}

// AddUserToTenant adds a user to a tenant with role
func (s *tenantMembershipService) AddUserToTenant(ctx context.Context, userID uint, tenantID uint, role string, isPrimary bool) (*models.TenantMembership, error) {
	// Create input for AddTenantMember
	input := AddTenantMemberInput{
		UserID:    userID,
		TenantID:  tenantID,
		Role:      role,
		IsPrimary: isPrimary,
		Status:    models.TenantMembershipStatusActive,
	}

	return s.AddTenantMember(ctx, input)
}

// RemoveUserFromTenant removes a user from a tenant
func (s *tenantMembershipService) RemoveUserFromTenant(ctx context.Context, userID uint, tenantID uint) error {
	return s.RemoveTenantMember(ctx, tenantID, userID)
}

// SetPrimaryTenant sets the primary tenant for a user
func (s *tenantMembershipService) SetPrimaryTenant(ctx context.Context, userID uint, tenantID uint) error {
	// First, unset any existing primary tenant
	memberships, err := s.GetUserMemberships(ctx, userID)
	if err != nil {
		return err
	}

	for _, membership := range memberships {
		if membership.IsPrimary && membership.TenantID != tenantID {
			// Unset the current primary
			if err := s.membershipRepo.UpdatePrimaryStatus(ctx, membership.ID, false); err != nil {
				return err
			}
		}
	}

	// Set the new primary tenant
	membership, err := s.GetMembership(ctx, tenantID, userID)
	if err != nil {
		return err
	}

	if membership == nil {
		return fmt.Errorf("user %d is not a member of tenant %d", userID, tenantID)
	}

	return s.membershipRepo.UpdatePrimaryStatus(ctx, membership.ID, true)
}
