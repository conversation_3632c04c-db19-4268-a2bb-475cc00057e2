package services

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// User Service Types

// CreateUserInput represents input for creating a user
type CreateUserInput struct {
	TenantID         uint              `json:"tenant_id" validate:"required"`
	Email            string            `json:"email" validate:"required,email"`
	Username         string            `json:"username" validate:"required,min=3,max=30"`
	Password         string            `json:"password" validate:"required,min=8"`
	FirstName        string            `json:"first_name" validate:"required,min=1,max=50"`
	LastName         string            `json:"last_name" validate:"required,min=1,max=50"`
	DisplayName      string            `json:"display_name" validate:"max=100"`
	Phone            string            `json:"phone,omitempty" validate:"omitempty,phone"`
	AvatarURL        string            `json:"avatar_url,omitempty" validate:"omitempty,url"`
	Language         string            `json:"language,omitempty" validate:"omitempty,len=2"`
	Timezone         string            `json:"timezone,omitempty"`
	Role             models.UserRole   `json:"role" validate:"required,oneof=user admin moderator"`
	Status           models.UserStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended pending_verification"`
	SendWelcomeEmail bool              `json:"send_welcome_email"`
}

// UpdateUserInput represents input for updating a user
type UpdateUserInput struct {
	Email       *string            `json:"email,omitempty" validate:"omitempty,email"`
	Username    *string            `json:"username,omitempty" validate:"omitempty,min=3,max=30"`
	FirstName   *string            `json:"first_name,omitempty" validate:"omitempty,min=1,max=50"`
	LastName    *string            `json:"last_name,omitempty" validate:"omitempty,min=1,max=50"`
	DisplayName *string            `json:"display_name,omitempty" validate:"omitempty,max=100"`
	Phone       *string            `json:"phone,omitempty" validate:"omitempty,phone"`
	AvatarURL   *string            `json:"avatar_url,omitempty" validate:"omitempty,url"`
	Language    *string            `json:"language,omitempty" validate:"omitempty,len=2"`
	Timezone    *string            `json:"timezone,omitempty"`
	Role        *models.UserRole   `json:"role,omitempty" validate:"omitempty,oneof=user admin moderator"`
	Status      *models.UserStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended pending_verification deleted"`
}

// ListUserFilter represents filter for listing users
type ListUserFilter struct {
	TenantID         uint                         `json:"tenant_id,omitempty"`
	Status           *models.UserStatus           `json:"status,omitempty"`
	Role             *models.UserRole             `json:"role,omitempty"`
	EmailVerified    *bool                        `json:"email_verified,omitempty"`
	PhoneVerified    *bool                        `json:"phone_verified,omitempty"`
	TwoFactorEnabled *bool                        `json:"two_factor_enabled,omitempty"`
	CreatedAfter     *time.Time                   `json:"created_after,omitempty"`
	CreatedBefore    *time.Time                   `json:"created_before,omitempty"`
	LastLoginAfter   *time.Time                   `json:"last_login_after,omitempty"`
	LastLoginBefore  *time.Time                   `json:"last_login_before,omitempty"`
	SearchQuery      string                       `json:"search_query,omitempty"`
	Pagination       *pagination.CursorPagination `json:"pagination,omitempty"`
}

// UserListResponse represents response for listing users
type UserListResponse struct {
	Users      []models.User              `json:"users"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	Total      int64                      `json:"total"`
}

// UserSearchResponse represents response for searching users
type UserSearchResponse struct {
	Users      []models.User              `json:"users"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	Total      int64                      `json:"total"`
	Query      string                     `json:"query"`
}

// UserSearchInput represents input for searching users
type UserSearchInput struct {
	TenantID   uint                         `json:"tenant_id,omitempty"`
	Query      string                       `json:"query" validate:"required,min=1"`
	Skills     []string                     `json:"skills,omitempty"`
	Location   string                       `json:"location,omitempty"`
	Company    string                       `json:"company,omitempty"`
	Role       *models.UserRole             `json:"role,omitempty"`
	Status     *models.UserStatus           `json:"status,omitempty"`
	Pagination *pagination.CursorPagination `json:"pagination,omitempty"`
}

// TwoFactorSetup represents two-factor authentication setup
type TwoFactorSetup struct {
	Secret      string   `json:"secret"`
	QRCodeURL   string   `json:"qr_code_url"`
	BackupCodes []string `json:"backup_codes"`
}

// UserStats represents user statistics
type UserStats struct {
	TotalUsers          int64                       `json:"total_users"`
	ActiveUsers         int64                       `json:"active_users"`
	InactiveUsers       int64                       `json:"inactive_users"`
	SuspendedUsers      int64                       `json:"suspended_users"`
	DeletedUsers        int64                       `json:"deleted_users"`
	VerifiedUsers       int64                       `json:"verified_users"`
	UnverifiedUsers     int64                       `json:"unverified_users"`
	TwoFactorUsers      int64                       `json:"two_factor_users"`
	RoleDistribution    map[models.UserRole]int64   `json:"role_distribution"`
	StatusDistribution  map[models.UserStatus]int64 `json:"status_distribution"`
	RegistrationTrend   []RegistrationPoint         `json:"registration_trend"`
	ActivityTrend       []ActivityPoint             `json:"activity_trend"`
	AverageLoginCount   float64                     `json:"average_login_count"`
	RecentlyActiveCount int64                       `json:"recently_active_count"`
}

// RegistrationPoint represents a point in registration trend
type RegistrationPoint struct {
	Date  time.Time `json:"date"`
	Count int64     `json:"count"`
}

// ActivityPoint represents a point in activity trend
type ActivityPoint struct {
	Date   time.Time `json:"date"`
	Logins int64     `json:"logins"`
	Active int64     `json:"active"`
}

// User Profile Service Types

// CreateUserProfileInput represents input for creating a user profile
type CreateUserProfileInput struct {
	UserID         uint                   `json:"user_id" validate:"required"`
	Bio            string                 `json:"bio,omitempty" validate:"omitempty,max=500"`
	Title          string                 `json:"title,omitempty" validate:"omitempty,max=100"`
	Company        string                 `json:"company,omitempty" validate:"omitempty,max=100"`
	Location       string                 `json:"location,omitempty" validate:"omitempty,max=100"`
	Website        string                 `json:"website,omitempty" validate:"omitempty,url"`
	BirthDate      *time.Time             `json:"birth_date,omitempty"`
	Gender         string                 `json:"gender,omitempty" validate:"omitempty,oneof=male female other prefer_not_to_say"`
	Address        string                 `json:"address,omitempty" validate:"omitempty,max=200"`
	City           string                 `json:"city,omitempty" validate:"omitempty,max=100"`
	State          string                 `json:"state,omitempty" validate:"omitempty,max=100"`
	Country        string                 `json:"country,omitempty" validate:"omitempty,max=100"`
	PostalCode     string                 `json:"postal_code,omitempty" validate:"omitempty,max=20"`
	JobTitle       string                 `json:"job_title,omitempty" validate:"omitempty,max=100"`
	Department     string                 `json:"department,omitempty" validate:"omitempty,max=100"`
	Skills         []string               `json:"skills,omitempty"`
	Interests      []string               `json:"interests,omitempty"`
	DisplayProfile bool                   `json:"display_profile"`
	AllowContact   bool                   `json:"allow_contact"`
	ShowEmail      bool                   `json:"show_email"`
	ShowPhone      bool                   `json:"show_phone"`
	CustomFields   map[string]interface{} `json:"custom_fields,omitempty"`
}

// UpdateUserProfileInput represents input for updating a user profile
type UpdateUserProfileInput struct {
	Bio            *string                `json:"bio,omitempty" validate:"omitempty,max=500"`
	Title          *string                `json:"title,omitempty" validate:"omitempty,max=100"`
	Company        *string                `json:"company,omitempty" validate:"omitempty,max=100"`
	Location       *string                `json:"location,omitempty" validate:"omitempty,max=100"`
	Website        *string                `json:"website,omitempty" validate:"omitempty,url"`
	BirthDate      *time.Time             `json:"birth_date,omitempty"`
	Gender         *string                `json:"gender,omitempty" validate:"omitempty,oneof=male female other prefer_not_to_say"`
	Address        *string                `json:"address,omitempty" validate:"omitempty,max=200"`
	City           *string                `json:"city,omitempty" validate:"omitempty,max=100"`
	State          *string                `json:"state,omitempty" validate:"omitempty,max=100"`
	Country        *string                `json:"country,omitempty" validate:"omitempty,max=100"`
	PostalCode     *string                `json:"postal_code,omitempty" validate:"omitempty,max=20"`
	JobTitle       *string                `json:"job_title,omitempty" validate:"omitempty,max=100"`
	Department     *string                `json:"department,omitempty" validate:"omitempty,max=100"`
	Skills         []string               `json:"skills,omitempty"`
	Interests      []string               `json:"interests,omitempty"`
	DisplayProfile *bool                  `json:"display_profile,omitempty"`
	AllowContact   *bool                  `json:"allow_contact,omitempty"`
	ShowEmail      *bool                  `json:"show_email,omitempty"`
	ShowPhone      *bool                  `json:"show_phone,omitempty"`
	CustomFields   map[string]interface{} `json:"custom_fields,omitempty"`
}

// UserProfileListResponse represents response for listing user profiles
type UserProfileListResponse struct {
	Profiles   []models.UserProfile       `json:"profiles"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	Total      int64                      `json:"total"`
}

// ProfileCompletionStats represents profile completion statistics
type ProfileCompletionStats struct {
	TotalProfiles        int64   `json:"total_profiles"`
	CompletedProfiles    int64   `json:"completed_profiles"`
	IncompleteProfiles   int64   `json:"incomplete_profiles"`
	AverageCompletion    float64 `json:"average_completion"`
	CompletionRate       float64 `json:"completion_rate"`
	ProfilesWithBio      int64   `json:"profiles_with_bio"`
	ProfilesWithAvatar   int64   `json:"profiles_with_avatar"`
	ProfilesWithLocation int64   `json:"profiles_with_location"`
	ProfilesWithCompany  int64   `json:"profiles_with_company"`
	ProfilesWithSkills   int64   `json:"profiles_with_skills"`
}

// User Preferences Service Types

// CreateUserPreferencesInput represents input for creating user preferences
type CreateUserPreferencesInput struct {
	UserID                 uint                     `json:"user_id" validate:"required"`
	EmailNotifications     bool                     `json:"email_notifications"`
	PushNotifications      bool                     `json:"push_notifications"`
	SMSNotifications       bool                     `json:"sms_notifications"`
	MarketingEmails        bool                     `json:"marketing_emails"`
	NewsletterSubscription bool                     `json:"newsletter_subscription"`
	ProductUpdates         bool                     `json:"product_updates"`
	SecurityAlerts         bool                     `json:"security_alerts"`
	ProfileVisibility      models.ProfileVisibility `json:"profile_visibility" validate:"oneof=public private friends_only"`
	ShowOnlineStatus       bool                     `json:"show_online_status"`
	AllowSearch            bool                     `json:"allow_search"`
	DataProcessingConsent  bool                     `json:"data_processing_consent"`
	Theme                  models.Theme             `json:"theme" validate:"oneof=light dark system"`
	DashboardLayout        string                   `json:"dashboard_layout"`
	ItemsPerPage           uint                     `json:"items_per_page" validate:"min=10,max=100"`
	AutoSave               bool                     `json:"auto_save"`
	KeyboardShortcuts      bool                     `json:"keyboard_shortcuts"`
	TooltipsEnabled        bool                     `json:"tooltips_enabled"`
	NotificationTypes      map[string]bool          `json:"notification_types,omitempty"`
	FeaturePreferences     map[string]interface{}   `json:"feature_preferences,omitempty"`
	CustomPreferences      map[string]interface{}   `json:"custom_preferences,omitempty"`
}

// UpdateUserPreferencesInput represents input for updating user preferences
type UpdateUserPreferencesInput struct {
	EmailNotifications     *bool                     `json:"email_notifications,omitempty"`
	PushNotifications      *bool                     `json:"push_notifications,omitempty"`
	SMSNotifications       *bool                     `json:"sms_notifications,omitempty"`
	MarketingEmails        *bool                     `json:"marketing_emails,omitempty"`
	NewsletterSubscription *bool                     `json:"newsletter_subscription,omitempty"`
	ProductUpdates         *bool                     `json:"product_updates,omitempty"`
	SecurityAlerts         *bool                     `json:"security_alerts,omitempty"`
	ProfileVisibility      *models.ProfileVisibility `json:"profile_visibility,omitempty" validate:"omitempty,oneof=public private friends_only"`
	ShowOnlineStatus       *bool                     `json:"show_online_status,omitempty"`
	AllowSearch            *bool                     `json:"allow_search,omitempty"`
	DataProcessingConsent  *bool                     `json:"data_processing_consent,omitempty"`
	Theme                  *models.Theme             `json:"theme,omitempty" validate:"omitempty,oneof=light dark system"`
	DashboardLayout        *string                   `json:"dashboard_layout,omitempty"`
	ItemsPerPage           *uint                     `json:"items_per_page,omitempty" validate:"omitempty,min=10,max=100"`
	AutoSave               *bool                     `json:"auto_save,omitempty"`
	KeyboardShortcuts      *bool                     `json:"keyboard_shortcuts,omitempty"`
	TooltipsEnabled        *bool                     `json:"tooltips_enabled,omitempty"`
	NotificationTypes      map[string]bool           `json:"notification_types,omitempty"`
	FeaturePreferences     map[string]interface{}    `json:"feature_preferences,omitempty"`
	CustomPreferences      map[string]interface{}    `json:"custom_preferences,omitempty"`
}

// NotificationPreferencesInput represents input for notification preferences
type NotificationPreferencesInput struct {
	EmailNotifications     bool            `json:"email_notifications"`
	PushNotifications      bool            `json:"push_notifications"`
	SMSNotifications       bool            `json:"sms_notifications"`
	MarketingEmails        bool            `json:"marketing_emails"`
	NewsletterSubscription bool            `json:"newsletter_subscription"`
	ProductUpdates         bool            `json:"product_updates"`
	SecurityAlerts         bool            `json:"security_alerts"`
	NotificationTypes      map[string]bool `json:"notification_types,omitempty"`
}

// PrivacyPreferencesInput represents input for privacy preferences
type PrivacyPreferencesInput struct {
	ProfileVisibility     models.ProfileVisibility `json:"profile_visibility" validate:"oneof=public private friends_only"`
	ShowOnlineStatus      bool                     `json:"show_online_status"`
	AllowSearch           bool                     `json:"allow_search"`
	DataProcessingConsent bool                     `json:"data_processing_consent"`
}

// UIPreferencesInput represents input for UI preferences
type UIPreferencesInput struct {
	Theme             models.Theme `json:"theme" validate:"oneof=light dark system"`
	DashboardLayout   string       `json:"dashboard_layout"`
	ItemsPerPage      uint         `json:"items_per_page" validate:"min=10,max=100"`
	AutoSave          bool         `json:"auto_save"`
	KeyboardShortcuts bool         `json:"keyboard_shortcuts"`
	TooltipsEnabled   bool         `json:"tooltips_enabled"`
}

// PreferencesStats represents preferences statistics
type PreferencesStats struct {
	TotalPreferences     int64                  `json:"total_preferences"`
	ThemeDistribution    map[models.Theme]int64 `json:"theme_distribution"`
	LanguageDistribution map[string]int64       `json:"language_distribution"`
	TimezoneDistribution map[string]int64       `json:"timezone_distribution"`
	NotificationStats    *NotificationStats     `json:"notification_stats"`
	PrivacyStats         *PrivacyStats          `json:"privacy_stats"`
}

// NotificationStats represents notification preferences statistics
type NotificationStats struct {
	EmailEnabled    int64 `json:"email_enabled"`
	PushEnabled     int64 `json:"push_enabled"`
	SMSEnabled      int64 `json:"sms_enabled"`
	MarketingAllow  int64 `json:"marketing_allow"`
	NewsletterAllow int64 `json:"newsletter_allow"`
	SecurityAllow   int64 `json:"security_allow"`
}

// PrivacyStats represents privacy preferences statistics
type PrivacyStats struct {
	PublicProfiles   int64 `json:"public_profiles"`
	PrivateProfiles  int64 `json:"private_profiles"`
	FriendsProfiles  int64 `json:"friends_profiles"`
	OnlineStatusShow int64 `json:"online_status_show"`
	SearchAllow      int64 `json:"search_allow"`
}

// User Social Links Service Types

// CreateUserSocialLinkInput represents input for creating a social link
type CreateUserSocialLinkInput struct {
	UserID       uint                   `json:"user_id" validate:"required"`
	Platform     models.SocialPlatform  `json:"platform" validate:"required,oneof=twitter linkedin github facebook instagram youtube tiktok snapchat discord twitch reddit pinterest medium dev stackoverflow behance dribbble website other"`
	Username     string                 `json:"username" validate:"required,min=1,max=255"`
	URL          string                 `json:"url" validate:"required,url"`
	DisplayOrder uint                   `json:"display_order"`
	IsPublic     bool                   `json:"is_public"`
	ProfileData  map[string]interface{} `json:"profile_data,omitempty"`
}

// UpdateUserSocialLinkInput represents input for updating a social link
type UpdateUserSocialLinkInput struct {
	Username     *string                `json:"username,omitempty" validate:"omitempty,min=1,max=255"`
	URL          *string                `json:"url,omitempty" validate:"omitempty,url"`
	DisplayOrder *uint                  `json:"display_order,omitempty"`
	IsPublic     *bool                  `json:"is_public,omitempty"`
	ProfileData  map[string]interface{} `json:"profile_data,omitempty"`
}

// SocialPlatformStats represents social platform statistics
type SocialPlatformStats struct {
	TotalLinks          int64                             `json:"total_links"`
	VerifiedLinks       int64                             `json:"verified_links"`
	UnverifiedLinks     int64                             `json:"unverified_links"`
	PublicLinks         int64                             `json:"public_links"`
	PrivateLinks        int64                             `json:"private_links"`
	PlatformCounts      map[models.SocialPlatform]int64   `json:"platform_counts"`
	VerificationRates   map[models.SocialPlatform]float64 `json:"verification_rates"`
	PublicRates         map[models.SocialPlatform]float64 `json:"public_rates"`
	UsersWithLinks      int64                             `json:"users_with_links"`
	AverageLinksPerUser float64                           `json:"average_links_per_user"`
}

// Verification Service Types

// VerificationStatus represents verification status
type VerificationStatus struct {
	UserID          uint       `json:"user_id"`
	EmailVerified   bool       `json:"email_verified"`
	EmailVerifiedAt *time.Time `json:"email_verified_at"`
	PhoneVerified   bool       `json:"phone_verified"`
	PhoneVerifiedAt *time.Time `json:"phone_verified_at"`
	LastEmailSent   *time.Time `json:"last_email_sent"`
	LastPhoneSent   *time.Time `json:"last_phone_sent"`
}

// Analytics Service Types

// UserActivity represents user activity data
type UserActivity struct {
	UserID                 uint              `json:"user_id"`
	LoginHistory           []LoginActivity   `json:"login_history"`
	ActionHistory          []ActionActivity  `json:"action_history"`
	SessionHistory         []SessionActivity `json:"session_history"`
	TotalLogins            int64             `json:"total_logins"`
	TotalActions           int64             `json:"total_actions"`
	TotalSessions          int64             `json:"total_sessions"`
	AverageSessionDuration float64           `json:"average_session_duration"`
	LastActivity           *time.Time        `json:"last_activity"`
}

// LoginActivity represents login activity
type LoginActivity struct {
	Date      time.Time `json:"date"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
	Success   bool      `json:"success"`
}

// ActionActivity represents action activity
type ActionActivity struct {
	Date   time.Time `json:"date"`
	Action string    `json:"action"`
	Target string    `json:"target"`
	Result string    `json:"result"`
}

// SessionActivity represents session activity
type SessionActivity struct {
	Date      time.Time `json:"date"`
	Duration  int64     `json:"duration"` // in seconds
	Actions   int64     `json:"actions"`
	IPAddress string    `json:"ip_address"`
}

// RegistrationStats represents registration statistics
type RegistrationStats struct {
	Period             string              `json:"period"`
	TotalRegistrations int64               `json:"total_registrations"`
	Trend              []RegistrationPoint `json:"trend"`
	Sources            map[string]int64    `json:"sources"`
	Devices            map[string]int64    `json:"devices"`
	Locations          map[string]int64    `json:"locations"`
}

// EngagementStats represents engagement statistics
type EngagementStats struct {
	Period                string  `json:"period"`
	TotalActiveUsers      int64   `json:"total_active_users"`
	DailyActiveUsers      int64   `json:"daily_active_users"`
	WeeklyActiveUsers     int64   `json:"weekly_active_users"`
	MonthlyActiveUsers    int64   `json:"monthly_active_users"`
	AverageSessionTime    float64 `json:"average_session_time"`
	AverageActionsPerUser float64 `json:"average_actions_per_user"`
	BounceRate            float64 `json:"bounce_rate"`
	ReturnRate            float64 `json:"return_rate"`
}

// RetentionStats represents retention statistics
type RetentionStats struct {
	Period         string   `json:"period"`
	CohortData     []Cohort `json:"cohort_data"`
	Day1Retention  float64  `json:"day1_retention"`
	Day7Retention  float64  `json:"day7_retention"`
	Day30Retention float64  `json:"day30_retention"`
	ChurnRate      float64  `json:"churn_rate"`
}

// Cohort represents cohort data
type Cohort struct {
	Date      time.Time `json:"date"`
	Size      int64     `json:"size"`
	Retention []float64 `json:"retention"`
}

// DemographicStats represents demographic statistics
type DemographicStats struct {
	AgeDistribution      map[string]int64 `json:"age_distribution"`
	GenderDistribution   map[string]int64 `json:"gender_distribution"`
	LocationDistribution map[string]int64 `json:"location_distribution"`
	LanguageDistribution map[string]int64 `json:"language_distribution"`
	TimezoneDistribution map[string]int64 `json:"timezone_distribution"`
	DeviceDistribution   map[string]int64 `json:"device_distribution"`
}

// Tenant Membership Service Types

// AddTenantMemberInput represents input for adding a member to a tenant
type AddTenantMemberInput struct {
	UserID        uint                          `json:"user_id" validate:"required"`
	TenantID      uint                          `json:"tenant_id" validate:"required"`
	Role          string                        `json:"role" validate:"required"`
	LocalUsername *string                       `json:"local_username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	DisplayName   *string                       `json:"display_name,omitempty" validate:"omitempty,min=1,max=255"`
	InvitedBy     *uint                         `json:"invited_by,omitempty"`
	Status        models.TenantMembershipStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended pending"`
	IsPrimary     bool                          `json:"is_primary,omitempty"`
}

// UpdateMemberRoleInput represents input for updating a member's role
type UpdateMemberRoleInput struct {
	Role          string  `json:"role" validate:"required"`
	LocalUsername *string `json:"local_username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	DisplayName   *string `json:"display_name,omitempty" validate:"omitempty,min=1,max=255"`
}

// TenantMembershipFilter represents filter for querying tenant memberships
type TenantMembershipFilter struct {
	TenantID       uint                          `json:"tenant_id,omitempty"`
	UserID         uint                          `json:"user_id,omitempty"`
	Status         models.TenantMembershipStatus `json:"status,omitempty"`
	Role           string                        `json:"role,omitempty"`
	InvitedBy      *uint                         `json:"invited_by,omitempty"`
	Search         string                        `json:"search,omitempty"`
	SortBy         string                        `json:"sort_by,omitempty"`
	SortOrder      string                        `json:"sort_order,omitempty"`
	IncludeDeleted bool                          `json:"include_deleted,omitempty"`
	Pagination     *pagination.CursorPagination  `json:"pagination,omitempty"`
}

// TenantMembershipListResponse represents response for listing tenant memberships
type TenantMembershipListResponse struct {
	Memberships []models.TenantMembership  `json:"memberships"`
	Pagination  *pagination.CursorResponse `json:"pagination"`
	Total       int64                      `json:"total"`
}

// TenantMembershipStats represents tenant membership statistics
type TenantMembershipStats struct {
	TotalMembers       int64                                   `json:"total_members"`
	ActiveMembers      int64                                   `json:"active_members"`
	InactiveMembers    int64                                   `json:"inactive_members"`
	SuspendedMembers   int64                                   `json:"suspended_members"`
	PendingMembers     int64                                   `json:"pending_members"`
	DeletedMembers     int64                                   `json:"deleted_members"`
	StatusDistribution map[models.TenantMembershipStatus]int64 `json:"status_distribution"`
	RecentJoins        []models.TenantMembership               `json:"recent_joins"`
	RecentActivity     []models.TenantMembership               `json:"recent_activity"`
}

// User Invitation Service Types

// CreateInvitationInput represents input for creating a user invitation
type CreateInvitationInput struct {
	TenantID  uint    `json:"tenant_id" validate:"required,min=1"`
	WebsiteID *uint   `json:"website_id,omitempty" validate:"omitempty,min=1"`
	Email     string  `json:"email" validate:"required,email"`
	RoleID    *uint   `json:"role_id,omitempty" validate:"omitempty,min=1"`
	Message   *string `json:"message,omitempty" validate:"omitempty,max=1000"`
	ExpiresIn int     `json:"expires_in,omitempty" validate:"omitempty,min=1,max=8760"` // Hours, max 1 year
}

// UpdateInvitationInput represents input for updating a user invitation
type UpdateInvitationInput struct {
	RoleID    *uint      `json:"role_id,omitempty" validate:"omitempty,min=1"`
	Message   *string    `json:"message,omitempty" validate:"omitempty,max=1000"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
}

// InvitationListFilter represents filter for listing invitations
type InvitationListFilter struct {
	TenantID       uint                         `json:"tenant_id,omitempty"`
	WebsiteID      *uint                        `json:"website_id,omitempty"`
	Email          string                       `json:"email,omitempty"`
	Status         models.UserInvitationStatus  `json:"status,omitempty"`
	InvitedBy      uint                         `json:"invited_by,omitempty"`
	Search         string                       `json:"search,omitempty"`
	SortBy         string                       `json:"sort_by,omitempty"`
	SortOrder      string                       `json:"sort_order,omitempty"`
	IncludeExpired bool                         `json:"include_expired,omitempty"`
	Pagination     *pagination.CursorPagination `json:"pagination,omitempty"`
}

// InvitationListResponse represents response for listing user invitations
type InvitationListResponse struct {
	Invitations []models.UserInvitationResponse `json:"invitations"`
	Pagination  *pagination.CursorResponse      `json:"pagination"`
	Total       int64                           `json:"total"`
}

// AcceptInvitationInput represents input for accepting an invitation
type AcceptInvitationInput struct {
	Token string `json:"token" validate:"required"`
}

// AcceptInvitationResponse represents response when accepting an invitation
type AcceptInvitationResponse struct {
	Invitation *models.UserInvitationResponse `json:"invitation"`
	Membership *models.TenantMembership       `json:"membership"`
}

// RejectInvitationInput represents input for rejecting an invitation
type RejectInvitationInput struct {
	Token string `json:"token" validate:"required"`
}
