package user

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all user-related routes
func RegisterRoutes(
	router *gin.RouterGroup,
	db *gorm.DB,
	validator validator.Validator,
	logger utils.Logger,
) {
	// Register CMS routes under /cms/v1
	cmsGroup := router.Group("/cms/v1")
	RegisterCMSRoutes(cmsGroup, db, validator, logger)

	// Register Admin routes under /admin/v1
	adminGroup := router.Group("/admin/v1")
	RegisterAdminRoutes(adminGroup, db, validator, logger)
}
