package handlers

import (
	"encoding/json"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// UserProfileHandler handles user profile-related HTTP requests
type UserProfileHandler struct {
	profileService services.UserProfileService
	validator      validator.Validator
	logger         utils.Logger
}

// NewUserProfileHandler creates a new user profile handler
func NewUserProfileHandler(
	profileService services.UserProfileService,
	validator validator.Validator,
	logger utils.Logger,
) *UserProfileHandler {
	return &UserProfileHandler{
		profileService: profileService,
		validator:      validator,
		logger:         logger,
	}
}

// CreateUserProfile creates a new user profile
// @Summary Create a new user profile
// @Description Create a new user profile with the provided information
// @Tags user-profiles
// @Accept json
// @Produce json
// @Security Bearer
// @Param profile body dto.UserProfileCreateRequest true "User profile data"
// @Success 201 {object} response.Response{data=dto.UserProfileResponse} "User profile created successfully"
// @Failure 400 {object} response.Response "Invalid request body or validation error"
// @Failure 409 {object} response.Response "Profile already exists"
// @Failure 500 {object} response.Response "Failed to create user profile"
// @Router /user-profiles [post]
func (h *UserProfileHandler) CreateUserProfile(c *gin.Context) {
	var req dto.UserProfileCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind profile input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		h.logger.WithError(err).Error("Failed to validate profile input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert DTO to service input
	serviceInput := h.convertToCreateProfileInput(req)

	// Create user profile
	profile, err := h.profileService.Create(c.Request.Context(), serviceInput)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create user profile")
		if err.Error() == "profile already exists for user" {
			response.Conflict(c.Writer, err.Error())
			return
		}
		if err.Error() == "user not found" {
			response.BadRequest(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to create user profile")
		return
	}

	// Convert to response DTO
	responseDTO := h.convertToProfileResponse(profile)
	response.Created(c.Writer, responseDTO)
}

// GetUserProfile retrieves a user profile by user ID
// @Summary Get a user profile by user ID
// @Description Retrieve a user profile by user ID
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 200 {object} models.UserProfile
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/user/{user_id} [get]
func (h *UserProfileHandler) GetUserProfile(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	profile, err := h.profileService.GetByUserID(c.Request.Context(), uint(userID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user profile")
		if err.Error() == "profile not found" {
			response.NotFound(c.Writer, "User profile not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get user profile")
		return
	}

	response.OK(c.Writer, profile)
}

// UpdateUserProfile updates a user profile
// @Summary Update a user profile
// @Description Update a user profile's information
// @Tags user-profiles
// @Accept json
// @Produce json
// @Security Bearer
// @Param user_id path int true "User ID"
// @Param profile body dto.UserProfileUpdateRequest true "User profile update data"
// @Success 200 {object} response.Response{data=dto.UserProfileResponse} "User profile updated successfully"
// @Failure 400 {object} response.Response "Invalid request body or validation error"
// @Failure 404 {object} response.Response "User profile not found"
// @Failure 500 {object} response.Response "Failed to update user profile"
// @Router /user-profiles/user/{user_id} [put]
func (h *UserProfileHandler) UpdateUserProfile(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var req dto.UserProfileUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind profile input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		h.logger.WithError(err).Error("Failed to validate profile input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert DTO to service input
	serviceInput := h.convertToUpdateProfileInput(req)

	// Update user profile
	profile, err := h.profileService.Update(c.Request.Context(), uint(userID), serviceInput)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update user profile")
		if err.Error() == "profile not found" {
			response.NotFound(c.Writer, "User profile not found")
			return
		}
		response.InternalError(c.Writer, "Failed to update user profile")
		return
	}

	// Convert to response DTO
	responseDTO := h.convertToProfileResponse(profile)
	response.OK(c.Writer, responseDTO)
}

// UpdateCompletionStatus updates profile completion status
// @Summary Update profile completion status
// @Description Update the completion status of a user profile
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/user/{user_id}/completion [put]
func (h *UserProfileHandler) UpdateCompletionStatus(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Update completion status
	if err := h.profileService.UpdateCompletionStatus(c.Request.Context(), uint(userID)); err != nil {
		h.logger.WithError(err).Error("Failed to update completion status")
		response.InternalError(c.Writer, "Failed to update completion status")
		return
	}

	response.NoContent(c.Writer)
}

// GetProfilesWithLowCompletion retrieves profiles with low completion
// @Summary Get profiles with low completion
// @Description Retrieve profiles with completion percentage below threshold
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param threshold query int false "Completion threshold percentage" default(50)
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserProfileListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/low-completion [get]
func (h *UserProfileHandler) GetProfilesWithLowCompletion(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse threshold parameter
	thresholdStr := c.DefaultQuery("threshold", "50")
	threshold, err := strconv.ParseUint(thresholdStr, 10, 8)
	if err != nil || threshold > 100 {
		h.logger.WithError(err).Error("Invalid threshold parameter")
		response.BadRequest(c.Writer, "Invalid threshold parameter (must be 0-100)")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		response.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Get profiles with low completion
	result, err := h.profileService.GetProfilesWithLowCompletion(c.Request.Context(), tenantID.(uint), uint8(threshold), pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get profiles with low completion")
		response.InternalError(c.Writer, "Failed to get profiles with low completion")
		return
	}

	// Use cursor-based pagination response format according to cursor-rule.md
	response.CursorPaginated(c.Writer, result.Profiles, *result.Pagination)
}

// SearchProfilesBySkills searches profiles by skills
// @Summary Search profiles by skills
// @Description Search user profiles by skills
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param skills query []string true "Skills to search for" collectionFormat(multi)
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserProfileListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/search/skills [get]
func (h *UserProfileHandler) SearchProfilesBySkills(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse skills parameter
	skills := c.QueryArray("skills")
	if len(skills) == 0 {
		h.logger.Error("Skills parameter is required")
		response.BadRequest(c.Writer, "Skills parameter is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		response.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Search profiles by skills
	result, err := h.profileService.SearchBySkills(c.Request.Context(), tenantID.(uint), skills, pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search profiles by skills")
		response.InternalError(c.Writer, "Failed to search profiles by skills")
		return
	}

	// Use cursor-based pagination response format according to cursor-rule.md
	response.CursorPaginated(c.Writer, result.Profiles, *result.Pagination)
}

// SearchProfilesByLocation searches profiles by location
// @Summary Search profiles by location
// @Description Search user profiles by location
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param location query string true "Location to search for"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserProfileListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/search/location [get]
func (h *UserProfileHandler) SearchProfilesByLocation(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse location parameter
	location := c.Query("location")
	if location == "" {
		h.logger.Error("Location parameter is required")
		response.BadRequest(c.Writer, "Location parameter is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		response.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Search profiles by location
	result, err := h.profileService.SearchByLocation(c.Request.Context(), tenantID.(uint), location, pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search profiles by location")
		response.InternalError(c.Writer, "Failed to search profiles by location")
		return
	}

	// Use cursor-based pagination response format according to cursor-rule.md
	response.CursorPaginated(c.Writer, result.Profiles, *result.Pagination)
}

// GetCompletionStats retrieves profile completion statistics
// @Summary Get profile completion statistics
// @Description Retrieve statistics about profile completion for the current tenant
// @Tags user-profiles
// @Accept json
// @Produce json
// @Success 200 {object} services.ProfileCompletionStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/stats/completion [get]
func (h *UserProfileHandler) GetCompletionStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get completion statistics
	stats, err := h.profileService.GetCompletionStats(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get completion statistics")
		response.InternalError(c.Writer, "Failed to get completion statistics")
		return
	}

	response.OK(c.Writer, stats)
}

// ValidateProfile validates profile data
// @Summary Validate profile data
// @Description Validate user profile data without saving
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param profile body services.UpdateUserProfileInput true "User profile data to validate"
// @Success 200 {object} map[string]bool
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/validate [post]
func (h *UserProfileHandler) ValidateProfile(c *gin.Context) {
	var input services.UpdateUserProfileInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind profile input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate profile data
	if err := h.profileService.ValidateProfile(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Profile validation failed")
		response.ValidationError(c.Writer, err)
		return
	}

	response.OK(c.Writer, map[string]bool{"valid": true})
}

// UpdateCustomFields updates custom fields for a user profile
// @Summary Update custom fields
// @Description Update custom fields for a user profile
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param custom_fields body map[string]interface{} true "Custom fields data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/user/{user_id}/custom-fields [put]
func (h *UserProfileHandler) UpdateCustomFields(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var customFields map[string]interface{}
	if err := c.ShouldBindJSON(&customFields); err != nil {
		h.logger.WithError(err).Error("Failed to bind custom fields input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Update custom fields
	if err := h.profileService.UpdateCustomFields(c.Request.Context(), uint(userID), customFields); err != nil {
		h.logger.WithError(err).Error("Failed to update custom fields")
		response.InternalError(c.Writer, "Failed to update custom fields")
		return
	}

	response.NoContent(c.Writer)
}

// Conversion functions

// convertToCreateProfileInput converts DTO to service input
func (h *UserProfileHandler) convertToCreateProfileInput(req dto.UserProfileCreateRequest) services.CreateUserProfileInput {
	return services.CreateUserProfileInput{
		UserID:         req.UserID,
		Bio:            req.Bio,
		Title:          req.Title,
		Company:        req.Company,
		Location:       req.Location,
		Website:        req.Website,
		BirthDate:      req.BirthDate,
		Gender:         req.Gender,
		Address:        req.Address,
		City:           req.City,
		State:          req.State,
		Country:        req.Country,
		PostalCode:     req.PostalCode,
		JobTitle:       req.JobTitle,
		Department:     req.Department,
		Skills:         req.Skills,
		Interests:      req.Interests,
		DisplayProfile: req.DisplayProfile,
		AllowContact:   req.AllowContact,
		ShowEmail:      req.ShowEmail,
		ShowPhone:      req.ShowPhone,
		CustomFields:   req.CustomFields,
	}
}

// convertToUpdateProfileInput converts DTO to service input
func (h *UserProfileHandler) convertToUpdateProfileInput(req dto.UserProfileUpdateRequest) services.UpdateUserProfileInput {
	return services.UpdateUserProfileInput{
		Bio:            req.Bio,
		Title:          req.Title,
		Company:        req.Company,
		Location:       req.Location,
		Website:        req.Website,
		BirthDate:      req.BirthDate,
		Gender:         req.Gender,
		Address:        req.Address,
		City:           req.City,
		State:          req.State,
		Country:        req.Country,
		PostalCode:     req.PostalCode,
		JobTitle:       req.JobTitle,
		Department:     req.Department,
		Skills:         req.Skills,
		Interests:      req.Interests,
		DisplayProfile: req.DisplayProfile,
		AllowContact:   req.AllowContact,
		ShowEmail:      req.ShowEmail,
		ShowPhone:      req.ShowPhone,
		CustomFields:   req.CustomFields,
	}
}

// convertToProfileResponse converts models.UserProfile to DTO response
func (h *UserProfileHandler) convertToProfileResponse(profile *models.UserProfile) *dto.UserProfileResponse {
	response := &dto.UserProfileResponse{
		ID:        profile.ID,
		UserID:    profile.UserID,
		TenantID:  profile.TenantID,
		BirthDate: profile.BirthDate,
		CreatedAt: profile.CreatedAt,
		UpdatedAt: profile.UpdatedAt,
	}

	// Handle pointer fields safely
	if profile.Bio != nil {
		response.Bio = *profile.Bio
	}
	if profile.Title != nil {
		response.Title = *profile.Title
	}
	if profile.Company != nil {
		response.Company = *profile.Company
	}
	if profile.Location != nil {
		response.Location = *profile.Location
	}
	if profile.Website != nil {
		response.Website = *profile.Website
	}
	if profile.Gender != nil {
		response.Gender = string(*profile.Gender)
	}
	if profile.AddressLine1 != nil {
		response.Address = *profile.AddressLine1
	}
	if profile.City != nil {
		response.City = *profile.City
	}
	if profile.State != nil {
		response.State = *profile.State
	}
	if profile.Country != nil {
		response.Country = *profile.Country
	}
	if profile.PostalCode != nil {
		response.PostalCode = *profile.PostalCode
	}
	if profile.JobTitle != nil {
		response.JobTitle = *profile.JobTitle
	}
	if profile.Department != nil {
		response.Department = *profile.Department
	}

	// Handle JSON fields
	if len(profile.Skills) > 0 {
		var skills []string
		if err := json.Unmarshal(profile.Skills, &skills); err == nil {
			response.Skills = skills
		}
	}
	if len(profile.Interests) > 0 {
		var interests []string
		if err := json.Unmarshal(profile.Interests, &interests); err == nil {
			response.Interests = interests
		}
	}

	// Handle boolean fields
	response.DisplayProfile = profile.DisplayProfile
	response.AllowContact = profile.AllowContact
	response.ShowEmail = profile.ShowEmail
	response.ShowPhone = profile.ShowPhone

	// Handle custom fields
	if len(profile.CustomFields) > 0 {
		var customFields map[string]interface{}
		if err := json.Unmarshal(profile.CustomFields, &customFields); err == nil {
			response.CustomFields = customFields
		}
	}

	return response
}
