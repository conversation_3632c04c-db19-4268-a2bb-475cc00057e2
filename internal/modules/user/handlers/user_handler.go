package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	userService services.UserService
	validator   validator.Validator
	logger      utils.Logger
}

// NewUserHandler creates a new user handler
func NewUserHandler(
	userService services.UserService,
	validator validator.Validator,
	logger utils.Logger,
) *UserHandler {
	return &UserHandler{
		userService: userService,
		validator:   validator,
		logger:      logger,
	}
}

// CreateUser creates a new user
// @Summary      Create a new user
// @Description  Create a new user with the provided information
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        request body dto.UserCreateRequest true "User data"
// @Success      201 {object} response.Response{data=dto.UserResponse} "User created successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      409 {object} response.Response "Email or username already exists"
// @Failure      500 {object} response.Response "Failed to create user"
// @Router       /api/cms/v1/users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req dto.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind user input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		h.logger.WithError(err).Error("Failed to validate user input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Convert DTO to service input
	serviceInput := h.convertToCreateUserInput(req, tenantID.(uint))

	// Create user
	user, err := h.userService.Create(c.Request.Context(), serviceInput)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create user")
		if err.Error() == "email already exists" || err.Error() == "username already exists" {
			response.Conflict(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to create user")
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToUserResponse(user)
	response.Created(c.Writer, responseDTO)
}

// GetUser retrieves a user by ID with tenant context
// @Summary Get a user by ID
// @Description Retrieve a user by their unique identifier with tenant membership information
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {object} models.User
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Get tenant ID from context for authorization
	tenantID, tenantExists := c.Get("tenant_id")
	if !tenantExists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	user, err := h.userService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get user")
		return
	}

	// Check if user has membership in the current tenant or if request is from system admin
	hasAccess := false
	if user.TenantMemberships != nil {
		for _, membership := range user.TenantMemberships {
			if membership.TenantID == tenantID.(uint) && membership.IsActive() {
				hasAccess = true
				break
			}
		}
	}

	if !hasAccess {
		h.logger.Error("User not found in current tenant", "user_id", id, "tenant_id", tenantID)
		response.NotFound(c.Writer, "User not found in current tenant")
		return
	}

	response.OK(c.Writer, user)
}

// UpdateUser updates a user
// @Summary      Update a user
// @Description  Update a user's information with tenant authorization
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "User ID"
// @Param        request body dto.UserUpdateRequest true "User update data"
// @Success      200 {object} response.Response{data=dto.UserResponse} "User updated successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "User not found"
// @Failure      409 {object} response.Response "Email or username already exists"
// @Failure      500 {object} response.Response "Failed to update user"
// @Router       /api/cms/v1/users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Get tenant ID from context for authorization
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	var req dto.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind user input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		h.logger.WithError(err).Error("Failed to validate user input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Check if user exists and has membership in current tenant
	user, err := h.userService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user for authorization")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get user")
		return
	}

	// Check if user has membership in the current tenant
	hasAccess := false
	if user.TenantMemberships != nil {
		for _, membership := range user.TenantMemberships {
			if membership.TenantID == tenantID.(uint) && membership.IsActive() {
				hasAccess = true
				break
			}
		}
	}

	if !hasAccess {
		h.logger.Error("User not found in current tenant", "user_id", id, "tenant_id", tenantID)
		response.NotFound(c.Writer, "User not found in current tenant")
		return
	}

	// Convert DTO to service input
	serviceInput := h.convertToUpdateUserInput(req)

	// Update user
	updatedUser, err := h.userService.Update(c.Request.Context(), uint(id), serviceInput)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update user")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		if err.Error() == "email already exists" || err.Error() == "username already exists" {
			response.Conflict(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to update user")
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToUserResponse(updatedUser)
	response.OK(c.Writer, responseDTO)
}

// DeleteUser deletes a user
// @Summary Delete a user
// @Description Soft delete a user with tenant authorization
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Get tenant ID from context for authorization
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Check if user exists and has membership in current tenant
	user, err := h.userService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user for authorization")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get user")
		return
	}

	// Check if user has membership in the current tenant
	hasAccess := false
	if user.TenantMemberships != nil {
		for _, membership := range user.TenantMemberships {
			if membership.TenantID == tenantID.(uint) && membership.IsActive() {
				hasAccess = true
				break
			}
		}
	}

	if !hasAccess {
		h.logger.Error("User not found in current tenant", "user_id", id, "tenant_id", tenantID)
		response.NotFound(c.Writer, "User not found in current tenant")
		return
	}

	// Delete user
	if err := h.userService.Delete(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to delete user")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to delete user")
		return
	}

	response.NoContent(c.Writer)
}

// ListUsers lists users with pagination and filtering
// @Summary List users
// @Description Retrieve a list of users with pagination and filtering options. Supports both tenant-specific and global listing.
// @Tags users
// @Accept json
// @Produce json
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Param status query string false "Filter by status"
// @Param role query string false "Filter by role"
// @Param email_verified query bool false "Filter by email verification status"
// @Param phone_verified query bool false "Filter by phone verification status"
// @Param two_factor_enabled query bool false "Filter by two-factor authentication status"
// @Param search query string false "Search query"
// @Param global query bool false "Search across all tenants (requires admin privileges)"
// @Success 200 {object} services.UserListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		response.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Check if global search is requested
	globalSearch := c.Query("global") == "true"

	// Parse filter parameters
	filter := services.ListUserFilter{
		Pagination: pagination,
	}

	// For global search, don't restrict by tenant (requires admin privileges)
	if !globalSearch {
		filter.TenantID = tenantID.(uint)
	}

	// Parse optional filters
	if status := c.Query("status"); status != "" {
		// Convert string to UserStatus enum
		filter.Status = (*models.UserStatus)(&status)
	}

	if role := c.Query("role"); role != "" {
		// Convert string to UserRole enum
		filter.Role = (*models.UserRole)(&role)
	}

	if emailVerified := c.Query("email_verified"); emailVerified != "" {
		verified, err := strconv.ParseBool(emailVerified)
		if err != nil {
			h.logger.WithError(err).Error("Invalid email_verified parameter")
			response.BadRequest(c.Writer, "Invalid email_verified parameter")
			return
		}
		filter.EmailVerified = &verified
	}

	if phoneVerified := c.Query("phone_verified"); phoneVerified != "" {
		verified, err := strconv.ParseBool(phoneVerified)
		if err != nil {
			h.logger.WithError(err).Error("Invalid phone_verified parameter")
			response.BadRequest(c.Writer, "Invalid phone_verified parameter")
			return
		}
		filter.PhoneVerified = &verified
	}

	if twoFactorEnabled := c.Query("two_factor_enabled"); twoFactorEnabled != "" {
		enabled, err := strconv.ParseBool(twoFactorEnabled)
		if err != nil {
			h.logger.WithError(err).Error("Invalid two_factor_enabled parameter")
			response.BadRequest(c.Writer, "Invalid two_factor_enabled parameter")
			return
		}
		filter.TwoFactorEnabled = &enabled
	}

	if search := c.Query("search"); search != "" {
		filter.SearchQuery = search
	}

	// List users
	result, err := h.userService.List(c.Request.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list users")
		response.InternalError(c.Writer, "Failed to list users")
		return
	}

	// Use cursor-based pagination response format according to cursor-rule.md
	response.CursorPaginated(c.Writer, result.Users, *result.Pagination)
}

// SearchUsers searches users by query
// @Summary Search users
// @Description Search users by query with pagination. Supports both tenant-specific and global search.
// @Tags users
// @Accept json
// @Produce json
// @Param query query string true "Search query"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Param global query bool false "Search across all tenants (requires admin privileges)"
// @Success 200 {object} services.UserSearchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/search [get]
func (h *UserHandler) SearchUsers(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get query parameter
	query := c.Query("query")
	if query == "" {
		h.logger.Error("Search query is required")
		response.BadRequest(c.Writer, "Search query is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		response.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Check if global search is requested
	globalSearch := c.Query("global") == "true"

	// Set tenant ID based on search scope
	searchTenantID := tenantID.(uint)
	if globalSearch {
		// For global search, pass 0 to indicate no tenant restriction
		// This requires admin privileges - should be checked at middleware level
		searchTenantID = 0
	}

	// Search users
	result, err := h.userService.Search(c.Request.Context(), searchTenantID, query, pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search users")
		response.InternalError(c.Writer, "Failed to search users")
		return
	}

	// Use cursor-based pagination response format according to cursor-rule.md
	response.CursorPaginated(c.Writer, result.Users, *result.Pagination)
}

// UpdateUserStatus updates user status
// @Summary Update user status
// @Description Update the status of a user with tenant authorization
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Param status body map[string]string true "Status data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/status [put]
func (h *UserHandler) UpdateUserStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Get tenant ID from context for authorization
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	var input struct {
		Status string `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind status input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Check if user exists and has membership in current tenant
	user, err := h.userService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user for authorization")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get user")
		return
	}

	// Check if user has membership in the current tenant
	hasAccess := false
	if user.TenantMemberships != nil {
		for _, membership := range user.TenantMemberships {
			if membership.TenantID == tenantID.(uint) && membership.IsActive() {
				hasAccess = true
				break
			}
		}
	}

	if !hasAccess {
		h.logger.Error("User not found in current tenant", "user_id", id, "tenant_id", tenantID)
		response.NotFound(c.Writer, "User not found in current tenant")
		return
	}

	// Convert string to UserStatus
	status := models.UserStatus(input.Status)

	// Update user status
	if err := h.userService.UpdateStatus(c.Request.Context(), uint(id), status); err != nil {
		h.logger.WithError(err).Error("Failed to update user status")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to update user status")
		return
	}

	response.NoContent(c.Writer)
}

// UpdateUserPassword updates user password
// @Summary Update user password
// @Description Update a user's password
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Param password body map[string]string true "Password data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/password [put]
func (h *UserHandler) UpdateUserPassword(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input struct {
		Password string `json:"password" binding:"required,min=8"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind password input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Update user password
	if err := h.userService.UpdatePassword(c.Request.Context(), uint(id), input.Password); err != nil {
		h.logger.WithError(err).Error("Failed to update user password")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to update user password")
		return
	}

	response.NoContent(c.Writer)
}

// VerifyUserEmail verifies user email
// @Summary Verify user email
// @Description Verify a user's email address
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/verify-email [post]
func (h *UserHandler) VerifyUserEmail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Verify user email
	if err := h.userService.VerifyEmail(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to verify user email")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to verify user email")
		return
	}

	response.NoContent(c.Writer)
}

// VerifyUserPhone verifies user phone
// @Summary Verify user phone
// @Description Verify a user's phone number
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/verify-phone [post]
func (h *UserHandler) VerifyUserPhone(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Verify user phone
	if err := h.userService.VerifyPhone(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to verify user phone")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to verify user phone")
		return
	}

	response.NoContent(c.Writer)
}

// EnableTwoFactor enables two-factor authentication
// @Summary Enable two-factor authentication
// @Description Enable two-factor authentication for a user
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {object} services.TwoFactorSetup
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/two-factor/enable [post]
func (h *UserHandler) EnableTwoFactor(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Enable two-factor authentication
	setup, err := h.userService.EnableTwoFactor(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to enable two-factor authentication")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to enable two-factor authentication")
		return
	}

	response.OK(c.Writer, setup)
}

// DisableTwoFactor disables two-factor authentication
// @Summary Disable two-factor authentication
// @Description Disable two-factor authentication for a user
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/two-factor/disable [post]
func (h *UserHandler) DisableTwoFactor(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Disable two-factor authentication
	if err := h.userService.DisableTwoFactor(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to disable two-factor authentication")
		if err.Error() == "user not found" {
			response.NotFound(c.Writer, "User not found")
			return
		}
		response.InternalError(c.Writer, "Failed to disable two-factor authentication")
		return
	}

	response.NoContent(c.Writer)
}

// GetUserStats retrieves user statistics
// @Summary Get user statistics
// @Description Retrieve statistics for users in the current tenant
// @Tags users
// @Accept json
// @Produce json
// @Success 200 {object} services.UserStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/stats [get]
func (h *UserHandler) GetUserStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user statistics
	stats, err := h.userService.GetUserStats(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user statistics")
		response.InternalError(c.Writer, "Failed to get user statistics")
		return
	}

	response.OK(c.Writer, stats)
}

// BulkUpdateUserStatus updates status for multiple users
// @Summary Bulk update user status
// @Description Update the status of multiple users at once
// @Tags users
// @Accept json
// @Produce json
// @Param data body map[string]interface{} true "Bulk update data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/bulk/status [put]
func (h *UserHandler) BulkUpdateUserStatus(c *gin.Context) {
	var input struct {
		UserIDs []uint `json:"user_ids" binding:"required"`
		Status  string `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind bulk update input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Convert string to UserStatus
	status := models.UserStatus(input.Status)

	// Bulk update user status
	if err := h.userService.BulkUpdateStatus(c.Request.Context(), input.UserIDs, status); err != nil {
		h.logger.WithError(err).Error("Failed to bulk update user status")
		response.InternalError(c.Writer, "Failed to bulk update user status")
		return
	}

	response.NoContent(c.Writer)
}

// Conversion functions

// convertToCreateUserInput converts DTO to service input
func (h *UserHandler) convertToCreateUserInput(req dto.UserCreateRequest, tenantID uint) services.CreateUserInput {
	return services.CreateUserInput{
		TenantID:         tenantID,
		Email:            req.Email,
		Username:         req.Username,
		Password:         req.Password,
		FirstName:        req.FirstName,
		LastName:         req.LastName,
		DisplayName:      req.DisplayName,
		Phone:            req.Phone,
		AvatarURL:        req.AvatarURL,
		Language:         req.Language,
		Timezone:         req.Timezone,
		Role:             req.Role,
		Status:           req.Status,
		SendWelcomeEmail: req.SendWelcomeEmail,
	}
}

// convertToUpdateUserInput converts DTO to service input
func (h *UserHandler) convertToUpdateUserInput(req dto.UserUpdateRequest) services.UpdateUserInput {
	return services.UpdateUserInput{
		Email:       req.Email,
		Username:    req.Username,
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		DisplayName: req.DisplayName,
		Phone:       req.Phone,
		AvatarURL:   req.AvatarURL,
		Language:    req.Language,
		Timezone:    req.Timezone,
		Role:        req.Role,
		Status:      req.Status,
	}
}

// convertToUserResponse converts models.User to DTO response
func (h *UserHandler) convertToUserResponse(user *models.User) *dto.UserResponse {
	response := &dto.UserResponse{
		ID:               user.ID,
		Email:            user.Email,
		Language:         user.Language,
		Timezone:         user.Timezone,
		Status:           user.Status,
		EmailVerified:    user.EmailVerified,
		EmailVerifiedAt:  user.EmailVerifiedAt,
		PhoneVerified:    user.PhoneVerified,
		PhoneVerifiedAt:  user.PhoneVerifiedAt,
		TwoFactorEnabled: user.TwoFactorEnabled,
		LastLoginAt:      user.LastLoginAt,
		LoginCount:       int64(user.LoginCount),
		CreatedAt:        user.CreatedAt,
		UpdatedAt:        user.UpdatedAt,
	}

	// Handle pointer fields
	if user.Username != nil {
		response.Username = *user.Username
	}
	if user.FirstName != nil {
		response.FirstName = *user.FirstName
	}
	if user.LastName != nil {
		response.LastName = *user.LastName
	}
	if user.DisplayName != nil {
		response.DisplayName = *user.DisplayName
	}
	if user.Phone != nil {
		response.Phone = *user.Phone
	}
	if user.AvatarURL != nil {
		response.AvatarURL = *user.AvatarURL
	}

	// Get TenantID from tenant memberships if available
	if user.TenantMemberships != nil && len(user.TenantMemberships) > 0 {
		// Use the first active membership for tenant ID
		for _, membership := range user.TenantMemberships {
			if membership.IsActive() {
				response.TenantID = membership.TenantID
				// Default role since TenantMembership doesn't have role field
				response.Role = models.UserRoleUser
				break
			}
		}
	}

	return response
}
