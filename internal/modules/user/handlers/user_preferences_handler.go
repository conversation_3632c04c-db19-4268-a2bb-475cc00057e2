package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// UserPreferencesHandler handles user preferences-related HTTP requests
type UserPreferencesHandler struct {
	preferencesService services.UserPreferencesService
	validator          validator.Validator
	logger             utils.Logger
}

// NewUserPreferencesHandler creates a new user preferences handler
func NewUserPreferencesHandler(
	preferencesService services.UserPreferencesService,
	validator validator.Validator,
	logger utils.Logger,
) *UserPreferencesHandler {
	return &UserPreferencesHandler{
		preferencesService: preferencesService,
		validator:          validator,
		logger:             logger,
	}
}

// CreateUserPreferences creates new user preferences
// @Summary Create new user preferences
// @Description Create new user preferences with default values
// @Tags user-preferences
// @Accept json
// @Produce json
// @Param preferences body services.CreateUserPreferencesInput true "User preferences data"
// @Success 201 {object} models.UserPreferences
// @Failure 400 {object} map[string]interface{}
// @Failure 409 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences [post]
func (h *UserPreferencesHandler) CreateUserPreferences(c *gin.Context) {
	var input services.CreateUserPreferencesInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind preferences input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate preferences input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Create user preferences
	preferences, err := h.preferencesService.Create(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create user preferences")
		if err.Error() == "preferences already exist for user" {
			response.Conflict(c.Writer, err.Error())
			return
		}
		if err.Error() == "user not found" {
			response.BadRequest(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to create user preferences")
		return
	}

	response.Created(c.Writer, preferences)
}

// GetUserPreferences retrieves user preferences by user ID
// @Summary Get user preferences by user ID
// @Description Retrieve user preferences by user ID
// @Tags user-preferences
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 200 {object} models.UserPreferences
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/user/{user_id} [get]
func (h *UserPreferencesHandler) GetUserPreferences(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	preferences, err := h.preferencesService.GetByUserID(c.Request.Context(), uint(userID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user preferences")
		if err.Error() == "preferences not found" {
			response.NotFound(c.Writer, "User preferences not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get user preferences")
		return
	}

	response.OK(c.Writer, preferences)
}

// UpdateUserPreferences updates user preferences
// @Summary Update user preferences
// @Description Update user preferences with new values
// @Tags user-preferences
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param preferences body services.UpdateUserPreferencesInput true "User preferences update data"
// @Success 200 {object} models.UserPreferences
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/user/{user_id} [put]
func (h *UserPreferencesHandler) UpdateUserPreferences(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input services.UpdateUserPreferencesInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind preferences input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate preferences input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Update user preferences
	preferences, err := h.preferencesService.Update(c.Request.Context(), uint(userID), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update user preferences")
		if err.Error() == "preferences not found" {
			response.NotFound(c.Writer, "User preferences not found")
			return
		}
		response.InternalError(c.Writer, "Failed to update user preferences")
		return
	}

	response.OK(c.Writer, preferences)
}

// UpdateNotificationPreferences updates notification preferences
// @Summary Update notification preferences
// @Description Update notification preferences for a user
// @Tags user-preferences
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param preferences body services.NotificationPreferencesInput true "Notification preferences data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/user/{user_id}/notifications [put]
func (h *UserPreferencesHandler) UpdateNotificationPreferences(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input services.NotificationPreferencesInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind notification preferences input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate notification preferences input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Update notification preferences
	if err := h.preferencesService.UpdateNotificationPreferences(c.Request.Context(), uint(userID), input); err != nil {
		h.logger.WithError(err).Error("Failed to update notification preferences")
		response.InternalError(c.Writer, "Failed to update notification preferences")
		return
	}

	response.NoContent(c.Writer)
}

// UpdatePrivacyPreferences updates privacy preferences
// @Summary Update privacy preferences
// @Description Update privacy preferences for a user
// @Tags user-preferences
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param preferences body services.PrivacyPreferencesInput true "Privacy preferences data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/user/{user_id}/privacy [put]
func (h *UserPreferencesHandler) UpdatePrivacyPreferences(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input services.PrivacyPreferencesInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind privacy preferences input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate privacy preferences input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Update privacy preferences
	if err := h.preferencesService.UpdatePrivacyPreferences(c.Request.Context(), uint(userID), input); err != nil {
		h.logger.WithError(err).Error("Failed to update privacy preferences")
		response.InternalError(c.Writer, "Failed to update privacy preferences")
		return
	}

	response.NoContent(c.Writer)
}

// UpdateUIPreferences updates UI preferences
// @Summary Update UI preferences
// @Description Update UI preferences for a user
// @Tags user-preferences
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param preferences body services.UIPreferencesInput true "UI preferences data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/user/{user_id}/ui [put]
func (h *UserPreferencesHandler) UpdateUIPreferences(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input services.UIPreferencesInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind UI preferences input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate UI preferences input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Update UI preferences
	if err := h.preferencesService.UpdateUIPreferences(c.Request.Context(), uint(userID), input); err != nil {
		h.logger.WithError(err).Error("Failed to update UI preferences")
		response.InternalError(c.Writer, "Failed to update UI preferences")
		return
	}

	response.NoContent(c.Writer)
}

// GetDefaultPreferences retrieves default preferences for tenant
// @Summary Get default preferences
// @Description Retrieve default preferences for the current tenant
// @Tags user-preferences
// @Accept json
// @Produce json
// @Success 200 {object} models.UserPreferences
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/defaults [get]
func (h *UserPreferencesHandler) GetDefaultPreferences(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get default preferences
	preferences, err := h.preferencesService.GetDefaultPreferences(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get default preferences")
		response.InternalError(c.Writer, "Failed to get default preferences")
		return
	}

	response.OK(c.Writer, preferences)
}

// ValidatePreferences validates preferences data
// @Summary Validate preferences data
// @Description Validate user preferences data without saving
// @Tags user-preferences
// @Accept json
// @Produce json
// @Param preferences body services.UpdateUserPreferencesInput true "User preferences data to validate"
// @Success 200 {object} map[string]bool
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/validate [post]
func (h *UserPreferencesHandler) ValidatePreferences(c *gin.Context) {
	var input services.UpdateUserPreferencesInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind preferences input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate preferences data
	if err := h.preferencesService.ValidatePreferences(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Preferences validation failed")
		response.ValidationError(c.Writer, err)
		return
	}

	response.OK(c.Writer, map[string]bool{"valid": true})
}

// ExportPreferences exports user preferences
// @Summary Export user preferences
// @Description Export user preferences to a portable format
// @Tags user-preferences
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/user/{user_id}/export [get]
func (h *UserPreferencesHandler) ExportPreferences(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Export preferences
	preferences, err := h.preferencesService.ExportPreferences(c.Request.Context(), uint(userID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to export preferences")
		response.InternalError(c.Writer, "Failed to export preferences")
		return
	}

	response.OK(c.Writer, preferences)
}

// ImportPreferences imports user preferences
// @Summary Import user preferences
// @Description Import user preferences from a portable format
// @Tags user-preferences
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param preferences body map[string]interface{} true "Preferences data to import"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/user/{user_id}/import [post]
func (h *UserPreferencesHandler) ImportPreferences(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		response.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var preferences map[string]interface{}
	if err := c.ShouldBindJSON(&preferences); err != nil {
		h.logger.WithError(err).Error("Failed to bind preferences input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Import preferences
	if err := h.preferencesService.ImportPreferences(c.Request.Context(), uint(userID), preferences); err != nil {
		h.logger.WithError(err).Error("Failed to import preferences")
		response.InternalError(c.Writer, "Failed to import preferences")
		return
	}

	response.NoContent(c.Writer)
}

// GetPreferencesStats retrieves preferences statistics
// @Summary Get preferences statistics
// @Description Retrieve statistics about user preferences for the current tenant
// @Tags user-preferences
// @Accept json
// @Produce json
// @Success 200 {object} services.PreferencesStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-preferences/stats [get]
func (h *UserPreferencesHandler) GetPreferencesStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get preferences statistics
	stats, err := h.preferencesService.GetPreferencesStats(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get preferences statistics")
		response.InternalError(c.Writer, "Failed to get preferences statistics")
		return
	}

	response.OK(c.Writer, stats)
}
