package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// BlogPostAutosaveHandler handles blog post auto-save operations
type BlogPostAutosaveHandler struct {
	autosaveService services.BlogPostAutosaveService
	logger          utils.Logger
}

// NewBlogPostAutosaveHandler creates a new blog post auto-save handler
func NewBlogPostAutosaveHandler(autosaveService services.BlogPostAutosaveService, logger utils.Logger) *BlogPostAutosaveHandler {
	return &BlogPostAutosaveHandler{
		autosaveService: autosaveService,
		logger:          logger,
	}
}

// AutoSave handles the auto-save request for a blog post
// @Summary Auto-save blog post
// @Description Auto-saves the current state of a blog post
// @Tags Blog Post Auto-save
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Param request body models.BlogPostAutosaveCreateRequest true "Auto-save request"
// @Success 200 {object} models.BlogPostAutosaveResponse
// @Failure 400 {object} response.ErrorResponse
// @Failure 401 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /blog/posts/{id}/autosave [post]
func (h *BlogPostAutosaveHandler) AutoSave(c *gin.Context) {
	// Get post ID from path
	postIDStr := c.Param("id")
	postID, err := strconv.ParseUint(postIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	// Get tenant ID and user ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User ID not found")
		return
	}

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid website ID")
		return
	}
	if websiteID == 0 {
		response.BadRequestWithContext(c, "Website ID is required")
		return
	}

	// Parse request body
	var req models.BlogPostAutosaveCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, err.Error())
		return
	}

	// Set post ID from path
	req.PostID = uint(postID)

	// Call service
	result, err := h.autosaveService.AutoSave(
		c.Request.Context(),
		tenantID.(uint),
		uint(postID),
		userID.(uint),
		websiteID,
		&req,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to auto-save blog post")
		response.InternalServerErrorWithContext(c, "Failed to auto-save")
		return
	}

	response.SuccessWithContext(c, result)
}

// GetAutosave retrieves the auto-save for a blog post
// @Summary Get blog post auto-save
// @Description Retrieves the auto-saved content for a blog post
// @Tags Blog Post Auto-save
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} models.BlogPostAutosaveResponse
// @Failure 400 {object} response.ErrorResponse
// @Failure 404 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /blog/posts/{id}/autosave [get]
func (h *BlogPostAutosaveHandler) GetAutosave(c *gin.Context) {
	// Get post ID from path
	postIDStr := c.Param("id")
	postID, err := strconv.ParseUint(postIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	// Get tenant ID and user ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User ID not found")
		return
	}

	// Call service
	result, err := h.autosaveService.GetAutosave(
		c.Request.Context(),
		tenantID.(uint),
		uint(postID),
		userID.(uint),
	)
	if err != nil {
		response.NotFoundWithContext(c, "Auto-save not found")
		return
	}

	response.SuccessWithContext(c, result)
}

// DeleteAutosave removes the auto-save for a blog post
// @Summary Delete blog post auto-save
// @Description Removes the auto-saved content for a blog post
// @Tags Blog Post Auto-save
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} response.SuccessResponse
// @Failure 400 {object} response.ErrorResponse
// @Failure 404 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /blog/posts/{id}/autosave [delete]
func (h *BlogPostAutosaveHandler) DeleteAutosave(c *gin.Context) {
	// Get post ID from path
	postIDStr := c.Param("id")
	postID, err := strconv.ParseUint(postIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	// Get tenant ID and user ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User ID not found")
		return
	}

	// Call service
	err = h.autosaveService.DeleteAutosave(
		c.Request.Context(),
		tenantID.(uint),
		uint(postID),
		userID.(uint),
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to delete auto-save")
		return
	}

	response.SuccessWithContext(c, gin.H{"message": "Auto-save deleted successfully"})
}

// GetAutosaveStatus retrieves the auto-save status for a blog post
// @Summary Get blog post auto-save status
// @Description Retrieves the auto-save status for a blog post
// @Tags Blog Post Auto-save
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} models.BlogPostAutosaveStatus
// @Failure 400 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /blog/posts/{id}/autosave/status [get]
func (h *BlogPostAutosaveHandler) GetAutosaveStatus(c *gin.Context) {
	// Get post ID from path
	postIDStr := c.Param("id")
	postID, err := strconv.ParseUint(postIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	// Get tenant ID and user ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User ID not found")
		return
	}

	// Call service
	status, err := h.autosaveService.GetAutosaveStatus(
		c.Request.Context(),
		tenantID.(uint),
		uint(postID),
		userID.(uint),
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get auto-save status")
		return
	}

	response.SuccessWithContext(c, status)
}

// ResolveConflict handles conflict resolution for auto-saves
// @Summary Resolve auto-save conflict
// @Description Resolves conflicts between auto-saved and current post content
// @Tags Blog Post Auto-save
// @Accept json
// @Produce json
// @Param request body models.BlogPostAutosaveConflictRequest true "Conflict resolution request"
// @Success 200 {object} response.SuccessResponse
// @Failure 400 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /blog/posts/autosave/resolve-conflict [post]
func (h *BlogPostAutosaveHandler) ResolveConflict(c *gin.Context) {
	// Get tenant ID and user ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User ID not found")
		return
	}

	// Parse request body
	var req models.BlogPostAutosaveConflictRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, err.Error())
		return
	}

	// Call service
	err := h.autosaveService.ResolveConflict(
		c.Request.Context(),
		tenantID.(uint),
		userID.(uint),
		&req,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to resolve auto-save conflict")
		response.InternalServerErrorWithContext(c, "Failed to resolve conflict")
		return
	}

	response.SuccessWithContext(c, gin.H{"message": "Conflict resolved successfully"})
}

// GetConflictedAutosaves retrieves all conflicted auto-saves for the tenant
// @Summary Get conflicted auto-saves
// @Description Retrieves all auto-saves with conflicts for the tenant
// @Tags Blog Post Auto-save
// @Accept json
// @Produce json
// @Success 200 {array} models.BlogPostAutosaveResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /blog/posts/autosave/conflicted [get]
func (h *BlogPostAutosaveHandler) GetConflictedAutosaves(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	// Call service
	autosaves, err := h.autosaveService.GetConflictedAutosaves(
		c.Request.Context(),
		tenantID.(uint),
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get conflicted auto-saves")
		return
	}

	response.SuccessWithContext(c, autosaves)
}

// RestoreFromAutosave restores a blog post from auto-save
// @Summary Restore from auto-save
// @Description Restores a blog post content from its auto-save
// @Tags Blog Post Auto-save
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} models.BlogPostResponse
// @Failure 400 {object} response.ErrorResponse
// @Failure 404 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /blog/posts/{id}/autosave/restore [post]
func (h *BlogPostAutosaveHandler) RestoreFromAutosave(c *gin.Context) {
	// Get post ID from path
	postIDStr := c.Param("id")
	postID, err := strconv.ParseUint(postIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	// Get tenant ID and user ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User ID not found")
		return
	}

	// Call service
	post, err := h.autosaveService.RestoreFromAutosave(
		c.Request.Context(),
		tenantID.(uint),
		uint(postID),
		userID.(uint),
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to restore from auto-save")
		return
	}

	response.SuccessWithContext(c, post)
}
