package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

type BlogCategoryHandler struct {
	categoryService services.BlogCategoryService
}

func NewBlogCategoryHandler(categoryService services.BlogCategoryService) *BlogCategoryHandler {
	return &BlogCategoryHandler{
		categoryService: categoryService,
	}
}

// CreateCategory creates a new blog category
// @Summary Create a new blog category
// @Description Create a new blog category with optional parent relationship. The id_path field will be automatically generated based on the parent hierarchy.
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string true "Website ID"
// @Param category body models.BlogCategoryCreateRequest true "Category details"
// @Success 201 {object} response.Response{data=models.BlogCategoryResponse} "Category created successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/categories [post]
// @Security BearerAuth
func (h *BlogCategoryHandler) CreateCategory(c *gin.Context) {
	var req models.BlogCategoryCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}
	req.TenantID = tenantID.(uint)

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid website ID", err.Error())
		return
	}
	if websiteID == 0 {
		response.BadRequestWithContext(c, "Website ID is required")
		return
	}
	req.WebsiteID = websiteID

	category, err := h.categoryService.Create(c.Request.Context(), &req)
	if err != nil {
		// Provide more specific error details based on error type
		response.InternalServerErrorWithContext(c, "Failed to create category", err.Error())
		return
	}

	response.CreatedWithContext(c, category)
}

// GetCategory retrieves a blog category by ID
// @Summary Get category by ID
// @Description Get a blog category by its ID. Response includes id_path field showing the full hierarchy path.
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path string true "Category ID"
// @Success 200 {object} response.Response{data=models.BlogCategoryResponse} "Category retrieved successfully"
// @Failure 400 {object} response.Response "Invalid category ID"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Category not found"
// @Router /blog/categories/{id} [get]
// @Security BearerAuth
func (h *BlogCategoryHandler) GetCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid category ID", map[string]interface{}{
			"provided_id": idStr,
			"error": "ID must be a positive integer",
		})
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	category, err := h.categoryService.GetByID(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		response.NotFoundWithContext(c, "Category not found")
		return
	}

	response.SuccessWithContext(c, category)
}

// GetCategoryBySlug retrieves a blog category by slug
// @Summary Get category by slug
// @Description Get a blog category by its slug. Response includes id_path field showing the full hierarchy path.
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string true "Website ID"
// @Param slug path string true "Category slug"
// @Success 200 {object} response.Response{data=models.BlogCategoryResponse} "Category retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Category not found"
// @Router /blog/categories/slug/{slug} [get]
// @Security BearerAuth
func (h *BlogCategoryHandler) GetCategoryBySlug(c *gin.Context) {
	slug := c.Param("slug")

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid website ID", err.Error())
		return
	}
	if websiteID == 0 {
		response.BadRequestWithContext(c, "Website ID is required")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	category, err := h.categoryService.GetBySlug(c.Request.Context(), tenantID.(uint), websiteID, slug)
	if err != nil {
		response.NotFoundWithContext(c, "Category not found")
		return
	}

	response.SuccessWithContext(c, category)
}

// UpdateCategory updates a blog category
// @Summary Update a blog category
// @Description Update a blog category. If parent_id is changed, id_path will be automatically recalculated for the category and all its descendants.
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path string true "Category ID"
// @Param category body models.BlogCategoryUpdateRequest true "Category update details"
// @Success 200 {object} response.Response{data=models.BlogCategoryResponse} "Category updated successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/categories/{id} [put]
// @Security BearerAuth
func (h *BlogCategoryHandler) UpdateCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid category ID", map[string]interface{}{
			"provided_id": idStr,
			"error": "ID must be a positive integer",
		})
		return
	}

	var req models.BlogCategoryUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	category, err := h.categoryService.Update(c.Request.Context(), tenantID.(uint), uint(id), &req)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to update category", err.Error())
		return
	}

	response.SuccessWithContext(c, category)
}

// DeleteCategory deletes a blog category
func (h *BlogCategoryHandler) DeleteCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid category ID", map[string]interface{}{
			"provided_id": idStr,
			"error": "ID must be a positive integer",
		})
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	err = h.categoryService.Delete(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to delete category", err.Error())
		return
	}

	response.SuccessWithContext(c, nil)
}

// ListCategories lists blog categories with filtering and supports both page and cursor pagination
// @Summary List blog categories
// @Description List blog categories with filtering and cursor-based pagination. Each category includes id_path field showing its position in the hierarchy.
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string false "Website ID for filtering"
// @Param search query string false "Search term for name or description"
// @Param is_active query boolean false "Filter by active status"
// @Param parent_id query integer false "Filter by parent category ID"
// @Param sort_by query string false "Sort field (default: sort_order)"
// @Param sort_order query string false "Sort order: ASC or DESC (default: ASC)"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query integer false "Number of items per page (default: 20)"
// @Success 200 {object} response.CursorPaginatedResponse{data=[]models.BlogCategoryResponse} "Categories retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/categories [get]
// @Security BearerAuth
func (h *BlogCategoryHandler) ListCategories(c *gin.Context) {
	var filter dto.BlogCategoryFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"query_parameters": "Invalid filter parameters: " + err.Error(),
		})
		return
	}

	// Set tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	// Use cursor-based pagination
	filters := map[string]interface{}{
		"website_id": filter.WebsiteID,
		"search":     filter.Search,
		"is_active":  filter.IsActive,
		"parent_id":  filter.ParentID,
		"sort_by":    filter.SortBy,
		"sort_order": filter.SortOrder,
	}

	result, cursorResp, err := h.categoryService.ListWithCursor(c.Request.Context(), tenantID.(uint), &filter.CursorRequest, filters)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to list categories with cursor", err.Error())
		return
	}

	response.CursorPaginatedWithContext(c, result, *cursorResp)
}

// GetCategoryHierarchy retrieves the category hierarchy
// @Summary Get category hierarchy
// @Description Get the complete category hierarchy for a website. Categories are returned in hierarchical order with id_path field for easy traversal.
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string true "Website ID"
// @Success 200 {object} response.Response{data=[]models.BlogCategoryResponse} "Category hierarchy retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/categories/hierarchy [get]
// @Security BearerAuth
func (h *BlogCategoryHandler) GetCategoryHierarchy(c *gin.Context) {
	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid website ID", err.Error())
		return
	}
	if websiteID == 0 {
		response.BadRequestWithContext(c, "Website ID is required")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	categories, err := h.categoryService.GetHierarchy(c.Request.Context(), tenantID.(uint), websiteID)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get category hierarchy", err.Error())
		return
	}

	response.SuccessWithContext(c, categories)
}

// MoveCategory moves a category to a new parent
// @Summary Move category to new parent
// @Description Move a category to a new parent. This will update the nested set values (lft, rgt) and recalculate id_path for the category and all its descendants.
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path string true "Category ID to move"
// @Param move body object{new_parent_id=integer} true "New parent category ID"
// @Success 200 {object} response.Response "Category moved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/categories/{id}/move [post]
// @Security BearerAuth
func (h *BlogCategoryHandler) MoveCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid category ID", map[string]interface{}{
			"provided_id": idStr,
			"error": "ID must be a positive integer",
		})
		return
	}

	var req struct {
		NewParentID uint `json:"new_parent_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	err = h.categoryService.MoveCategory(c.Request.Context(), tenantID.(uint), uint(id), req.NewParentID)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to move category", err.Error())
		return
	}

	response.SuccessWithContext(c, nil)
}

// UpdateCategoryPositions updates category positions for reordering
func (h *BlogCategoryHandler) UpdateCategoryPositions(c *gin.Context) {
	var req struct {
		Positions []services.CategoryPosition `json:"positions"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	err := h.categoryService.UpdatePositions(c.Request.Context(), tenantID.(uint), req.Positions)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to update category positions", err.Error())
		return
	}

	response.SuccessWithContext(c, nil)
}

// GetAncestors retrieves all ancestor categories for a given category
// @Summary Get category ancestors
// @Description Get all ancestor categories using the id_path field for fast retrieval. Returns ancestors in hierarchical order from root to parent.
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path string true "Category ID"
// @Success 200 {object} response.Response{data=[]models.BlogCategoryResponse} "Ancestors retrieved successfully"
// @Failure 400 {object} response.Response "Invalid category ID"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Category not found"
// @Router /blog/categories/{id}/ancestors [get]
// @Security BearerAuth
func (h *BlogCategoryHandler) GetAncestors(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid category ID", map[string]interface{}{
			"provided_id": idStr,
			"error": "ID must be a positive integer",
		})
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	ancestors, err := h.categoryService.GetAncestors(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		if err.Error() == "category not found" {
			response.NotFoundWithContext(c, "Category not found")
		} else {
			response.InternalServerErrorWithContext(c, "Failed to get ancestors", err.Error())
		}
		return
	}

	response.SuccessWithContext(c, ancestors)
}

// GetBreadcrumb retrieves the breadcrumb path for a category
// @Summary Get category breadcrumb
// @Description Get the full breadcrumb path from root to the specified category. Uses id_path for efficient traversal.
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path string true "Category ID"
// @Success 200 {object} response.Response{data=models.BlogCategoryBreadcrumb} "Breadcrumb retrieved successfully"
// @Failure 400 {object} response.Response "Invalid category ID"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Category not found"
// @Router /blog/categories/{id}/breadcrumb [get]
// @Security BearerAuth
func (h *BlogCategoryHandler) GetBreadcrumb(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid category ID", map[string]interface{}{
			"provided_id": idStr,
			"error": "ID must be a positive integer",
		})
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	breadcrumb, err := h.categoryService.GetBreadcrumb(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		if err.Error() == "category not found" {
			response.NotFoundWithContext(c, "Category not found")
		} else {
			response.InternalServerErrorWithContext(c, "Failed to get breadcrumb", err.Error())
		}
		return
	}

	response.SuccessWithContext(c, breadcrumb)
}

// ListForSelect returns categories formatted for select/dropdown UI components
// @Summary Get categories for select dropdown
// @Description Get categories formatted for select/dropdown UI components with hierarchy support
// @Tags Blog Categories
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string true "Website ID"
// @Param parent_id query integer false "Filter by parent category ID"
// @Param show_tree query boolean false "Show hierarchical tree structure with indentation"
// @Param include_disabled query boolean false "Include inactive categories"
// @Success 200 {object} response.Response{data=dto.SelectResponse} "Categories for select retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/categories/select [get]
// @Security BearerAuth
func (h *BlogCategoryHandler) ListForSelect(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.UnauthorizedWithContext(c, "Tenant ID not found")
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid website ID", err.Error())
		return
	}
	if websiteID == 0 {
		response.BadRequestWithContext(c, "Website ID is required")
		return
	}

	// Parse query parameters
	var parentID *uint
	if pid := c.Query("parent_id"); pid != "" {
		if id, err := strconv.ParseUint(pid, 10, 32); err == nil {
			parentIDVal := uint(id)
			parentID = &parentIDVal
		}
	}

	showTree := c.Query("show_tree") == "true"
	includeDisabled := c.Query("include_disabled") == "true"

	// Get categories for select
	options, err := h.categoryService.GetSelectOptions(c.Request.Context(), tenantID.(uint), websiteID, parentID, showTree, includeDisabled)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get categories for select", err.Error())
		return
	}

	response.SuccessWithContext(c, dto.SelectResponse{
		Options: options,
		Total:   len(options),
	})
}
