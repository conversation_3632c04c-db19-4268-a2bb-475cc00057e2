package blog

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	notificationrepos "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	notificationservices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	seoServices "github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// RegisterRoutes registers all blog module routes and dependencies
func RegisterRoutes(r *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger) {
	// Create repositories
	repos := mysql.NewRepositories(db, logger)

	// Initialize notification services
	notificationRepo := notificationrepos.NewNotificationRepository(db)
	templateRepo := notificationrepos.NewTemplateRepository(db)
	recipientRepo := notificationrepos.NewRecipientRepository(db)
	logRepo := notificationrepos.NewLogRepository(db)
	templateService := notificationservices.NewTemplateService(templateRepo)
	emailProvider := notificationservices.NewSMTPProvider(
		utils.GetEnv("SMTP_HOST", "localhost"),
		utils.GetEnvAsInt("SMTP_PORT", 1025),
		"", // No username needed for MailCatcher
		"", // No password needed for MailCatcher
		"Blog API",
		utils.GetEnv("SMTP_FROM", "<EMAIL>"),
		logger,
	)
	socketProvider := notificationservices.NewMockSocketProvider(logger)
	smsProvider := notificationservices.NewMockSMSProvider(logger)
	pushProvider := notificationservices.NewMockPushProvider(logger)
	deliveryService := notificationservices.NewDeliveryService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		templateService,
		emailProvider,
		socketProvider,
		smsProvider,
		pushProvider,
		logger,
	)
	notificationService := notificationservices.NewNotificationService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		deliveryService,
	)

	// Create autosave, template and reading progress repositories
	autosaveRepo := mysql.NewBlogPostAutosaveRepository(db)
	blogTemplateRepo := mysql.NewBlogPostTemplateRepository(db)
	progressRepo := mysql.NewBlogPostReadingProgressRepository(db)

	// Create services
	blogServices := services.NewBlogServices(
		repos.Category,
		repos.Tag,
		repos.Post,
		repos.Schedule,
		repos.Revision,
		autosaveRepo,
		blogTemplateRepo,
		repos.HomepageBlock,
		repos.BlockTemplate,
		repos.Newsletter,
		progressRepo,
		repos.Workflow,
		repos.Rating,
		repos.Author,
		repos.Royalty,
		notificationService,
		logger,
		db,
	)

	// Register blog routes (without SEO - use RegisterRoutesWithSEO for SEO integration)
	RegisterBlogRoutes(r, blogServices, nil, logger, db)
}

// RegisterRoutesWithSEO registers all blog module routes with SEO integration
func RegisterRoutesWithSEO(r *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger, seoMetaService seoServices.SEOMetaService) {
	// Create repositories
	repos := mysql.NewRepositories(db, logger)

	// Initialize notification services
	notificationRepo := notificationrepos.NewNotificationRepository(db)
	templateRepo := notificationrepos.NewTemplateRepository(db)
	recipientRepo := notificationrepos.NewRecipientRepository(db)
	logRepo := notificationrepos.NewLogRepository(db)
	templateService := notificationservices.NewTemplateService(templateRepo)
	emailProvider := notificationservices.NewSMTPProvider(
		utils.GetEnv("SMTP_HOST", "localhost"),
		utils.GetEnvAsInt("SMTP_PORT", 1025),
		"", // No username needed for MailCatcher
		"", // No password needed for MailCatcher
		"Blog API",
		utils.GetEnv("SMTP_FROM", "<EMAIL>"),
		logger,
	)
	socketProvider := notificationservices.NewMockSocketProvider(logger)
	smsProvider := notificationservices.NewMockSMSProvider(logger)
	pushProvider := notificationservices.NewMockPushProvider(logger)
	deliveryService := notificationservices.NewDeliveryService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		templateService,
		emailProvider,
		socketProvider,
		smsProvider,
		pushProvider,
		logger,
	)
	notificationService := notificationservices.NewNotificationService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		deliveryService,
	)

	// Create autosave, template and reading progress repositories
	autosaveRepo := mysql.NewBlogPostAutosaveRepository(db)
	blogTemplateRepo := mysql.NewBlogPostTemplateRepository(db)
	progressRepo := mysql.NewBlogPostReadingProgressRepository(db)

	// Create services
	blogServices := services.NewBlogServices(
		repos.Category,
		repos.Tag,
		repos.Post,
		repos.Schedule,
		repos.Revision,
		autosaveRepo,
		blogTemplateRepo,
		repos.HomepageBlock,
		repos.BlockTemplate,
		repos.Newsletter,
		progressRepo,
		repos.Workflow,
		repos.Rating,
		repos.Author,
		repos.Royalty,
		notificationService,
		logger,
		db,
	)

	// Register blog routes with SEO integration
	RegisterBlogRoutes(r, blogServices, seoMetaService, logger, db)
}
