package models

import (
	"time"
)

// DeviceType represents the type of device used for reading
// @Enum mobile,desktop,tablet,unknown
type DeviceType string

const (
	DeviceTypeMobile  DeviceType = "mobile"
	DeviceTypeDesktop DeviceType = "desktop"
	DeviceTypeTablet  DeviceType = "tablet"
	DeviceTypeUnknown DeviceType = "unknown"
)

// BlogPostReadingProgress represents a user's reading progress for a blog post
type BlogPostReadingProgress struct {
	ID        uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint `gorm:"not null;index" json:"tenant_id"`
	WebsiteID uint `gorm:"not null;index" json:"website_id"`

	// Post and User Information
	BlogPostID uint `gorm:"not null;index" json:"blog_post_id" validate:"required,min=1"`
	UserID     uint `gorm:"not null;index" json:"user_id" validate:"required,min=1"`

	// Reading Progress Data
	ReadingProgressPercentage float64 `gorm:"type:decimal(5,2);not null;default:0.00" json:"reading_progress_percentage" validate:"min=0,max=100"`
	TimeSpentSeconds          uint    `gorm:"not null;default:0" json:"time_spent_seconds" validate:"min=0"`
	LastReadPosition          uint    `gorm:"not null;default:0" json:"last_read_position" validate:"min=0"`
	ScrollDepthPercentage     float64 `gorm:"type:decimal(5,2);not null;default:0.00" json:"scroll_depth_percentage" validate:"min=0,max=100"`

	// Reading Session Data
	SessionCount uint       `gorm:"not null;default:1" json:"session_count" validate:"min=1"`
	FirstReadAt  *time.Time `json:"first_read_at,omitempty"`
	LastReadAt   time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"last_read_at"`

	// Reading Behavior Analytics
	IsCompleted  bool       `gorm:"not null;default:false" json:"is_completed"`
	CompletedAt  *time.Time `json:"completed_at,omitempty"`
	ReadSpeedWPM *float64   `gorm:"type:decimal(6,2)" json:"read_speed_wpm,omitempty" validate:"omitempty,min=0"`

	// Device and Context
	DeviceType DeviceType `gorm:"type:varchar(20);not null;default:'unknown'" json:"device_type" validate:"oneof=mobile desktop tablet unknown"`
	IPAddress  string     `gorm:"varchar(45)" json:"ip_address,omitempty"`
	UserAgent  string     `gorm:"type:text" json:"user_agent,omitempty"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	BlogPost *BlogPost `gorm:"foreignKey:BlogPostID" json:"blog_post,omitempty"`
}

// TableName returns the table name for the BlogPostReadingProgress model
func (BlogPostReadingProgress) TableName() string {
	return "blog_post_reading_progress"
}

// BlogPostReadingProgressCreateRequest represents the request to track reading progress
type BlogPostReadingProgressCreateRequest struct {
	TenantID                  uint       `json:"tenant_id" validate:"required,min=1" example:"1"`
	BlogPostID                uint       `json:"blog_post_id" validate:"required,min=1" example:"1"`
	UserID                    uint       `json:"user_id" validate:"required,min=1" example:"1"`
	ReadingProgressPercentage float64    `json:"reading_progress_percentage" validate:"min=0,max=100" example:"45.5"`
	TimeSpentSeconds          uint       `json:"time_spent_seconds" validate:"min=0" example:"120"`
	LastReadPosition          uint       `json:"last_read_position" validate:"min=0" example:"1500"`
	ScrollDepthPercentage     float64    `json:"scroll_depth_percentage" validate:"min=0,max=100" example:"67.8"`
	DeviceType                DeviceType `json:"device_type" validate:"oneof=mobile desktop tablet unknown" example:"desktop"`
	IPAddress                 string     `json:"-"`
	UserAgent                 string     `json:"-"`
}

// BlogPostReadingProgressUpdateRequest represents the request to update reading progress
type BlogPostReadingProgressUpdateRequest struct {
	ReadingProgressPercentage float64 `json:"reading_progress_percentage" validate:"min=0,max=100" example:"67.5"`
	TimeSpentSeconds          uint    `json:"time_spent_seconds" validate:"min=0" example:"180"`
	LastReadPosition          uint    `json:"last_read_position" validate:"min=0" example:"2100"`
	ScrollDepthPercentage     float64 `json:"scroll_depth_percentage" validate:"min=0,max=100" example:"85.2"`
	IsCompleted               *bool   `json:"is_completed,omitempty" example:"false"`
}

// BlogPostReadingProgressResponse represents the response when returning reading progress data
type BlogPostReadingProgressResponse struct {
	ID                        uint              `json:"id"`
	TenantID                  uint              `json:"tenant_id"`
	BlogPostID                uint              `json:"blog_post_id"`
	UserID                    uint              `json:"user_id"`
	ReadingProgressPercentage float64           `json:"reading_progress_percentage"`
	TimeSpentSeconds          uint              `json:"time_spent_seconds"`
	LastReadPosition          uint              `json:"last_read_position"`
	ScrollDepthPercentage     float64           `json:"scroll_depth_percentage"`
	SessionCount              uint              `json:"session_count"`
	FirstReadAt               *time.Time        `json:"first_read_at,omitempty"`
	LastReadAt                time.Time         `json:"last_read_at"`
	IsCompleted               bool              `json:"is_completed"`
	CompletedAt               *time.Time        `json:"completed_at,omitempty"`
	ReadSpeedWPM              *float64          `json:"read_speed_wpm,omitempty"`
	DeviceType                DeviceType        `json:"device_type"`
	CreatedAt                 time.Time         `json:"created_at"`
	UpdatedAt                 time.Time         `json:"updated_at"`
	BlogPost                  *BlogPostResponse `json:"blog_post,omitempty"`
}

// FromBlogPostReadingProgress converts a BlogPostReadingProgress model to BlogPostReadingProgressResponse
func (bprr *BlogPostReadingProgressResponse) FromBlogPostReadingProgress(progress *BlogPostReadingProgress) {
	bprr.ID = progress.ID
	bprr.TenantID = progress.TenantID
	bprr.BlogPostID = progress.BlogPostID
	bprr.UserID = progress.UserID
	bprr.ReadingProgressPercentage = progress.ReadingProgressPercentage
	bprr.TimeSpentSeconds = progress.TimeSpentSeconds
	bprr.LastReadPosition = progress.LastReadPosition
	bprr.ScrollDepthPercentage = progress.ScrollDepthPercentage
	bprr.SessionCount = progress.SessionCount
	bprr.FirstReadAt = progress.FirstReadAt
	bprr.LastReadAt = progress.LastReadAt
	bprr.IsCompleted = progress.IsCompleted
	bprr.CompletedAt = progress.CompletedAt
	bprr.ReadSpeedWPM = progress.ReadSpeedWPM
	bprr.DeviceType = progress.DeviceType
	bprr.CreatedAt = progress.CreatedAt
	bprr.UpdatedAt = progress.UpdatedAt

	// Convert relationship
	if progress.BlogPost != nil {
		blogPostResponse := &BlogPostResponse{}
		blogPostResponse.FromBlogPost(progress.BlogPost)
		bprr.BlogPost = blogPostResponse
	}
}

// BlogPostReadingProgressFilter represents filters for querying reading progress records
type BlogPostReadingProgressFilter struct {
	TenantID    uint       `json:"tenant_id,omitempty"`
	BlogPostID  uint       `json:"blog_post_id,omitempty"`
	UserID      uint       `json:"user_id,omitempty"`
	IsCompleted *bool      `json:"is_completed,omitempty"`
	DeviceType  DeviceType `json:"device_type,omitempty"`
	MinProgress *float64   `json:"min_progress,omitempty"`
	MaxProgress *float64   `json:"max_progress,omitempty"`
	DateFrom    *time.Time `json:"date_from,omitempty"`
	DateTo      *time.Time `json:"date_to,omitempty"`
	Page        int        `json:"page,omitempty"`
	PageSize    int        `json:"page_size,omitempty"`
	SortBy      string     `json:"sort_by,omitempty"`
	SortOrder   string     `json:"sort_order,omitempty"`
}

// BlogPostReadingStats represents reading statistics for a blog post
type BlogPostReadingStats struct {
	BlogPostID          uint    `json:"blog_post_id"`
	TotalReaders        int     `json:"total_readers"`
	CompletedReaders    int     `json:"completed_readers"`
	AverageProgress     float64 `json:"average_progress"`
	AverageTimeSpent    float64 `json:"average_time_spent"`
	AverageReadSpeedWPM float64 `json:"average_read_speed_wpm"`
	AverageScrollDepth  float64 `json:"average_scroll_depth"`
	CompletionRate      float64 `json:"completion_rate"`
	MobileReaders       int     `json:"mobile_readers"`
	DesktopReaders      int     `json:"desktop_readers"`
	TabletReaders       int     `json:"tablet_readers"`
}

// UserReadingStats represents reading statistics for a specific user
type UserReadingStats struct {
	UserID                 uint       `json:"user_id"`
	TotalPostsRead         int        `json:"total_posts_read"`
	TotalPostsCompleted    int        `json:"total_posts_completed"`
	TotalTimeSpent         uint       `json:"total_time_spent"`
	AverageReadingProgress float64    `json:"average_reading_progress"`
	AverageReadSpeedWPM    float64    `json:"average_read_speed_wpm"`
	CompletionRate         float64    `json:"completion_rate"`
	PreferredDeviceType    DeviceType `json:"preferred_device_type"`
	TotalReadingSessions   int        `json:"total_reading_sessions"`
}
