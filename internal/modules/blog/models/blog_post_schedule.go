package models

import (
	"encoding/json"
	"time"

	"gorm.io/datatypes"
)

// BlogPostScheduleType represents the type of scheduling
// @Enum one-time,daily,weekly,monthly,custom
type BlogPostScheduleType string

const (
	BlogPostScheduleTypeOneTime BlogPostScheduleType = "one-time"
	BlogPostScheduleTypeDaily   BlogPostScheduleType = "daily"
	BlogPostScheduleTypeWeekly  BlogPostScheduleType = "weekly"
	BlogPostScheduleTypeMonthly BlogPostScheduleType = "monthly"
	BlogPostScheduleTypeCustom  BlogPostScheduleType = "custom"
)

// BlogPostScheduleStatus represents the status of a schedule
// @Enum pending,processing,completed,failed,cancelled,deleted
type BlogPostScheduleStatus string

const (
	BlogPostScheduleStatusPending    BlogPostScheduleStatus = "pending"
	BlogPostScheduleStatusProcessing BlogPostScheduleStatus = "processing"
	BlogPostScheduleStatusCompleted  BlogPostScheduleStatus = "completed"
	BlogPostScheduleStatusFailed     BlogPostScheduleStatus = "failed"
	BlogPostScheduleStatusCancelled  BlogPostScheduleStatus = "cancelled"
	BlogPostScheduleStatusDeleted    BlogPostScheduleStatus = "deleted"
)

// BlogPostSchedule represents a blog post schedule
type BlogPostSchedule struct {
	ID        uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint `gorm:"not null;index" json:"tenant_id"`
	WebsiteID uint `gorm:"not null;index" json:"website_id"`
	PostID    uint `gorm:"not null;index" json:"post_id"`

	// Schedule Configuration
	ScheduleType    BlogPostScheduleType `gorm:"type:varchar(20);not null;default:'one-time'" json:"schedule_type" validate:"oneof=one-time daily weekly monthly custom"`
	ScheduledAt     time.Time            `gorm:"not null;index" json:"scheduled_at" validate:"required"`
	Timezone        string               `gorm:"type:varchar(50);not null;default:'UTC'" json:"timezone" validate:"required"`
	RecurringConfig datatypes.JSON       `gorm:"type:json;default:'{}'" json:"recurring_config,omitempty"`

	// Status and Error Handling
	Status       BlogPostScheduleStatus `gorm:"type:varchar(20);not null;default:'pending'" json:"status" validate:"oneof=pending processing completed failed cancelled deleted"`
	ErrorMessage string                 `gorm:"type:text" json:"error_message,omitempty"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	Post *BlogPost `gorm:"foreignKey:PostID" json:"post,omitempty"`
}

// TableName returns the table name for the BlogPostSchedule model
func (BlogPostSchedule) TableName() string {
	return "blog_post_schedules"
}

// BlogPostScheduleCreateRequest represents the request to create a blog post schedule
type BlogPostScheduleCreateRequest struct {
	TenantID        uint                   `json:"tenant_id" validate:"required,min=1"`
	WebsiteID       uint                   `json:"website_id" validate:"required,min=1"`
	PostID          uint                   `json:"post_id" validate:"required,min=1"`
	ScheduleType    BlogPostScheduleType   `json:"schedule_type" validate:"oneof=one-time daily weekly monthly custom"`
	ScheduledAt     time.Time              `json:"scheduled_at" validate:"required"`
	Timezone        string                 `json:"timezone" validate:"required"`
	RecurringConfig map[string]interface{} `json:"recurring_config,omitempty"`
}

// BlogPostScheduleUpdateRequest represents the request to update a blog post schedule
type BlogPostScheduleUpdateRequest struct {
	ScheduleType    BlogPostScheduleType   `json:"schedule_type" validate:"oneof=one-time daily weekly monthly custom"`
	ScheduledAt     time.Time              `json:"scheduled_at" validate:"required"`
	Timezone        string                 `json:"timezone" validate:"required"`
	RecurringConfig map[string]interface{} `json:"recurring_config,omitempty"`
	Status          BlogPostScheduleStatus `json:"status" validate:"oneof=pending processing completed failed cancelled deleted"`
}

// BlogPostScheduleResponse represents the response when returning blog post schedule data
type BlogPostScheduleResponse struct {
	ID              uint                   `json:"id"`
	TenantID        uint                   `json:"tenant_id"`
	WebsiteID       uint                   `json:"website_id"`
	PostID          uint                   `json:"post_id"`
	ScheduleType    BlogPostScheduleType   `json:"schedule_type"`
	ScheduledAt     time.Time              `json:"scheduled_at"`
	Timezone        string                 `json:"timezone"`
	RecurringConfig map[string]interface{} `json:"recurring_config,omitempty"`
	Status          BlogPostScheduleStatus `json:"status"`
	ErrorMessage    string                 `json:"error_message,omitempty"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	Post            *BlogPostResponse      `json:"post,omitempty"`
}

// FromBlogPostSchedule converts a BlogPostSchedule model to BlogPostScheduleResponse
func (bpsr *BlogPostScheduleResponse) FromBlogPostSchedule(schedule *BlogPostSchedule) {
	bpsr.ID = schedule.ID
	bpsr.TenantID = schedule.TenantID
	bpsr.WebsiteID = schedule.WebsiteID
	bpsr.PostID = schedule.PostID
	bpsr.ScheduleType = schedule.ScheduleType
	bpsr.ScheduledAt = schedule.ScheduledAt
	bpsr.Timezone = schedule.Timezone
	bpsr.Status = schedule.Status
	bpsr.ErrorMessage = schedule.ErrorMessage
	bpsr.CreatedAt = schedule.CreatedAt
	bpsr.UpdatedAt = schedule.UpdatedAt

	// Parse JSON data
	if schedule.RecurringConfig != nil {
		var recurringConfig map[string]interface{}
		if err := json.Unmarshal(schedule.RecurringConfig, &recurringConfig); err == nil {
			bpsr.RecurringConfig = recurringConfig
		}
	}

	// Convert relationships
	if schedule.Post != nil {
		postResponse := &BlogPostResponse{}
		postResponse.FromBlogPost(schedule.Post)
		bpsr.Post = postResponse
	}
}

// BlogPostScheduleFilter represents filters for querying blog post schedules
type BlogPostScheduleFilter struct {
	TenantID     uint                   `json:"tenant_id,omitempty"`
	WebsiteID    uint                   `json:"website_id,omitempty"`
	PostID       uint                   `json:"post_id,omitempty"`
	ScheduleType BlogPostScheduleType   `json:"schedule_type,omitempty"`
	Status       BlogPostScheduleStatus `json:"status,omitempty"`
	DateFrom     *time.Time             `json:"date_from,omitempty"`
	DateTo       *time.Time             `json:"date_to,omitempty"`
	Page         int                    `json:"page,omitempty"`
	PageSize     int                    `json:"page_size,omitempty"`
	SortBy       string                 `json:"sort_by,omitempty"`
	SortOrder    string                 `json:"sort_order,omitempty"`
}

// RecurringConfig represents the configuration for recurring schedules
type RecurringConfig struct {
	Interval       int        `json:"interval"`        // Every N days/weeks/months
	DaysOfWeek     []int      `json:"days_of_week"`    // For weekly: 0=Sunday, 1=Monday, etc.
	DayOfMonth     int        `json:"day_of_month"`    // For monthly: 1-31
	EndDate        *time.Time `json:"end_date"`        // When to stop recurring
	MaxOccurrences int        `json:"max_occurrences"` // Maximum number of occurrences
}
