package models

import (
	"time"
)

// BlogPostType represents the type of blog post
// @Enum post,page,announcement
type BlogPostType string

const (
	BlogPostTypePost         BlogPostType = "post"
	BlogPostTypePage         BlogPostType = "page"
	BlogPostTypeAnnouncement BlogPostType = "announcement"
)

// BlogPostStatus represents the content state of a blog post
// @Enum draft,published,scheduled,archived,deleted
type BlogPostStatus string

const (
	BlogPostStatusDraft     BlogPostStatus = "draft"     // Nội dung nháp
	BlogPostStatusPublished BlogPostStatus = "published" // Đ<PERSON> xuất bản công khai
	BlogPostStatusScheduled BlogPostStatus = "scheduled" // Hẹn giờ xuất bản
	BlogPostStatusArchived  BlogPostStatus = "archived"  // Lưu trữ
	BlogPostStatusDeleted   BlogPostStatus = "deleted"   // Đã xóa
)

// SEOMetadata represents SEO metadata for blog posts with flat structure
type SEOMetadata struct {
	// Basic Meta Tags
	MetaTitle       string   `json:"meta_title,omitempty" validate:"omitempty,max=60" example:"10 React Best Practices You Should Know"`
	MetaDescription string   `json:"meta_description,omitempty" validate:"omitempty,max=160" example:"Discover the top 10 React development best practices"`
	MetaKeywords    []string `json:"meta_keywords,omitempty" validate:"omitempty,max=10,dive,max=50" example:"[\"react\", \"javascript\", \"best practices\"]"`
	MetaRobots      string   `json:"meta_robots,omitempty" validate:"omitempty,max=50" example:"index,follow"`
	CanonicalURL    string   `json:"canonical_url,omitempty" validate:"omitempty,url" example:"https://blog.example.com/react-best-practices"`

	// Open Graph Meta Tags
	OGTitle       string `json:"og_title,omitempty" validate:"omitempty,max=60" example:"10 React Best Practices"`
	OGDescription string `json:"og_description,omitempty" validate:"omitempty,max=160" example:"Learn React best practices"`
	OGImage       string `json:"og_image,omitempty" validate:"omitempty,url" example:"https://example.com/og-image.jpg"`
	OGType        string `json:"og_type,omitempty" validate:"omitempty,max=50" example:"article"`
	OGURL         string `json:"og_url,omitempty" validate:"omitempty,url" example:"https://blog.example.com/react-best-practices"`
	OGSiteName    string `json:"og_site_name,omitempty" validate:"omitempty,max=100" example:"My Tech Blog"`
	OGLocale      string `json:"og_locale,omitempty" validate:"omitempty,max=10" example:"en_US"`

	// Twitter Card Meta Tags
	TwitterCard        string `json:"twitter_card,omitempty" validate:"omitempty,oneof=summary summary_large_image app player" example:"summary_large_image"`
	TwitterTitle       string `json:"twitter_title,omitempty" validate:"omitempty,max=70" example:"10 React Best Practices"`
	TwitterDescription string `json:"twitter_description,omitempty" validate:"omitempty,max=200" example:"Learn React best practices"`
	TwitterImage       string `json:"twitter_image,omitempty" validate:"omitempty,url" example:"https://example.com/twitter-image.jpg"`
	TwitterCreator     string `json:"twitter_creator,omitempty" validate:"omitempty,max=50" example:"@johndoe"`
	TwitterSite        string `json:"twitter_site,omitempty" validate:"omitempty,max=50" example:"@mytechblog"`

	// Schema.org Structured Data
	SchemaType string                 `json:"schema_type,omitempty" validate:"omitempty,max=50" example:"BlogPosting"`
	SchemaData map[string]interface{} `json:"schema_data,omitempty" example:"{\"@type\":\"BlogPosting\",\"headline\":\"10 React Best Practices\"}"`

	// SEO Settings
	FocusKeyword string `json:"focus_keyword,omitempty" validate:"omitempty,max=100" example:"react best practices"`
}

// WorkflowState represents the editorial workflow state
// @Enum creation,pending_review,in_review,pending_approval,pending_eic,approved,returned,rejected,completed
type WorkflowState string

const (
	WorkflowStateCreation        WorkflowState = "creation"         // Đang soạn thảo
	WorkflowStatePendingReview   WorkflowState = "pending_review"   // Chờ biên tập
	WorkflowStateInReview        WorkflowState = "in_review"        // Đang biên tập
	WorkflowStatePendingApproval WorkflowState = "pending_approval" // Chờ duyệt xuất bản
	WorkflowStatePendingEIC      WorkflowState = "pending_eic"      // Chờ TBT duyệt
	WorkflowStateApproved        WorkflowState = "approved"         // Đã duyệt
	WorkflowStateReturned        WorkflowState = "returned"         // Bị trả lại
	WorkflowStateRejected        WorkflowState = "rejected"         // Bị từ chối
	WorkflowStateCompleted       WorkflowState = "completed"        // Hoàn thành
)

// BlogPost represents a blog post
type BlogPost struct {
	ID        uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint `gorm:"not null;index" json:"tenant_id"`
	WebsiteID uint `gorm:"not null;index" json:"website_id"`

	// Basic Information
	Slug    string `gorm:"type:varchar(255);not null" json:"slug" validate:"required,min=1,max=255"`
	Title   string `gorm:"type:varchar(255);not null" json:"title" validate:"required,min=1,max=255"`
	Content string `gorm:"type:longtext;not null" json:"content" validate:"required"`
	Excerpt string `gorm:"type:text" json:"excerpt,omitempty"`

	// Author and Category
	AuthorID   uint  `gorm:"not null;index" json:"author_id" validate:"required,min=1"`
	CategoryID *uint `gorm:"index" json:"category_id,omitempty" validate:"omitempty,min=1"`

	// Post Configuration
	Type          BlogPostType `gorm:"type:varchar(20);not null;default:'post'" json:"type" validate:"oneof=post page announcement"`
	IsFeatured    bool         `gorm:"default:false" json:"is_featured"`
	IsSticky      bool         `gorm:"default:false" json:"is_sticky"` // New field to match DTO
	AllowComments bool         `gorm:"default:true" json:"allow_comments"`
	Password      string       `gorm:"type:varchar(255)" json:"password,omitempty"`
	FeaturedImage string       `gorm:"type:varchar(500)" json:"featured_image,omitempty"`

	// Statistics
	ViewCount    uint `gorm:"default:0" json:"view_count"`
	CommentCount uint `gorm:"default:0" json:"comment_count"` // New field for comment count

	// Publishing
	ScheduledAt *time.Time     `gorm:"index" json:"scheduled_at,omitempty"`
	PublishedAt *time.Time     `json:"published_at,omitempty"`
	Status      BlogPostStatus `gorm:"type:varchar(20);not null;default:'draft'" json:"status" validate:"oneof=draft published scheduled archived deleted"`

	// SEO Metadata (JSON field for flat structure)
	SEOMetadata JSON `gorm:"type:json;default:'{}'" json:"seo_metadata,omitempty"`

	// Related Posts (JSON array to store manual related post IDs)
	RelatedPostIDs JSON `gorm:"type:json;default:'[]'" json:"related_post_ids,omitempty"`

	// Workflow Management
	WorkflowState      WorkflowState `gorm:"type:varchar(50);default:'creation'" json:"workflow_state"`
	WorkflowAssignedTo *uint         `gorm:"index" json:"workflow_assigned_to,omitempty"`
	WorkflowAssignedAt *time.Time    `json:"workflow_assigned_at,omitempty"`
	WorkflowDueAt      *time.Time    `json:"workflow_due_at,omitempty"`
	WorkflowNotes      string        `gorm:"type:text" json:"workflow_notes,omitempty"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	Category *BlogCategory `gorm:"foreignKey:CategoryID" json:"category,omitempty"`
	Tags     []BlogTag     `gorm:"many2many:blog_post_tags;" json:"tags,omitempty"`
}

// TableName returns the table name for the BlogPost model
func (BlogPost) TableName() string {
	return "blog_posts"
}

// BlogPostCreateRequest represents the request to create a blog post
type BlogPostCreateRequest struct {
	TenantID       uint           `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID      uint           `json:"website_id" validate:"required,min=1" example:"1"`
	Slug           string         `json:"slug" validate:"required,min=1,max=255" example:"my-first-blog-post"`
	Title          string         `json:"title" validate:"required,min=1,max=255" example:"My First Blog Post"`
	Content        string         `json:"content" validate:"required" example:"This is the comprehensive content of my first blog post. It covers programming concepts, best practices, and includes code examples..."`
	Excerpt        string         `json:"excerpt,omitempty" validate:"max=1000" example:"A brief summary of my first blog post covering programming fundamentals"`
	AuthorID       uint           `json:"author_id" validate:"required,min=1" example:"1"`
	CategoryID     *uint          `json:"category_id,omitempty" validate:"omitempty,min=1" example:"1"`
	Type           BlogPostType   `json:"type" validate:"oneof=post page announcement" example:"post"`
	IsFeatured     bool           `json:"is_featured" example:"false"`
	IsSticky       bool           `json:"is_sticky" example:"false"` // New field to match DTO
	AllowComments  bool           `json:"allow_comments" example:"true"`
	Password       string         `json:"password,omitempty" validate:"max=255" example:""`
	FeaturedImage  string         `json:"featured_image,omitempty" validate:"omitempty,url" example:"https://example.com/images/featured-image.jpg"`
	ScheduledAt    *time.Time     `json:"scheduled_at,omitempty" example:"2025-01-20T15:00:00Z"`
	Status         BlogPostStatus `json:"status" validate:"oneof=draft published scheduled" example:"draft"`
	WorkflowState  WorkflowState  `json:"workflow_state,omitempty" validate:"omitempty,oneof=creation pending_review in_review pending_approval pending_eic approved returned rejected completed" example:"creation"`
	TagIDs         []uint         `json:"tag_ids,omitempty" validate:"max=10" example:"1,2,3"`            // Updated validation to max 10
	RelatedPostIDs []uint         `json:"related_post_ids,omitempty" validate:"max=10" example:"100,101"` // New field for manual related posts
	SEOMetadata    *SEOMetadata   `json:"seo,omitempty" validate:"omitempty,dive"`                        // New field for SEO metadata
}

// BlogPostUpdateRequest represents the request to update a blog post
type BlogPostUpdateRequest struct {
	Slug               *string         `json:"slug,omitempty" validate:"omitempty,min=1,max=255"`
	Title              *string         `json:"title,omitempty" validate:"omitempty,min=1,max=255"`
	Content            *string         `json:"content,omitempty" validate:"omitempty"`
	Excerpt            *string         `json:"excerpt,omitempty" validate:"omitempty,max=1000"`
	CategoryID         *uint           `json:"category_id,omitempty" validate:"omitempty,min=1"`
	Type               *BlogPostType   `json:"type,omitempty" validate:"omitempty,oneof=post page announcement"`
	IsFeatured         *bool           `json:"is_featured,omitempty"`
	IsSticky           *bool           `json:"is_sticky,omitempty"` // New field to match DTO
	AllowComments      *bool           `json:"allow_comments,omitempty"`
	Password           *string         `json:"password,omitempty" validate:"omitempty,max=255"`
	FeaturedImage      *string         `json:"featured_image,omitempty" validate:"omitempty,url"`
	ScheduledAt        *time.Time      `json:"scheduled_at,omitempty"`
	Status             *BlogPostStatus `json:"status,omitempty" validate:"omitempty,oneof=draft published scheduled archived"`
	WorkflowState      *WorkflowState  `json:"workflow_state,omitempty" validate:"omitempty,oneof=creation pending_review in_review pending_approval pending_eic approved returned rejected completed"`
	WorkflowAssignedTo *uint           `json:"workflow_assigned_to,omitempty"`
	WorkflowAssignedAt *time.Time      `json:"workflow_assigned_at,omitempty"`
	WorkflowDueAt      *time.Time      `json:"workflow_due_at,omitempty"`
	WorkflowNotes      *string         `json:"workflow_notes,omitempty"`
	TagIDs             *[]uint         `json:"tag_ids,omitempty" validate:"omitempty,max=10"`          // Complete tag list for sync, max 10
	RelatedPostIDs     *[]uint         `json:"related_post_ids,omitempty" validate:"omitempty,max=10"` // Manual related posts sync, max 10
	SEOMetadata        *SEOMetadata    `json:"seo,omitempty" validate:"omitempty,dive"`                // SEO metadata update
}

// BlogPostResponse represents the response when returning blog post data
type BlogPostResponse struct {
	ID                 uint                  `json:"id"`
	TenantID           uint                  `json:"tenant_id"`
	WebsiteID          uint                  `json:"website_id"`
	Slug               string                `json:"slug"`
	Title              string                `json:"title"`
	Content            string                `json:"content"`
	Excerpt            string                `json:"excerpt,omitempty"`
	AuthorID           uint                  `json:"author_id"`
	CategoryID         *uint                 `json:"category_id,omitempty"`
	Type               BlogPostType          `json:"type"`
	IsFeatured         bool                  `json:"is_featured"`
	IsSticky           bool                  `json:"is_sticky"` // New field to match DTO
	AllowComments      bool                  `json:"allow_comments"`
	FeaturedImage      string                `json:"featured_image,omitempty"`
	ViewCount          uint                  `json:"view_count"`
	CommentCount       uint                  `json:"comment_count"` // New field for comment count
	ScheduledAt        *time.Time            `json:"scheduled_at,omitempty"`
	PublishedAt        *time.Time            `json:"published_at,omitempty"`
	Status             BlogPostStatus        `json:"status"`
	WorkflowState      WorkflowState         `json:"workflow_state"`
	WorkflowAssignedTo *uint                 `json:"workflow_assigned_to,omitempty"`
	WorkflowAssignedAt *time.Time            `json:"workflow_assigned_at,omitempty"`
	WorkflowDueAt      *time.Time            `json:"workflow_due_at,omitempty"`
	WorkflowNotes      string                `json:"workflow_notes,omitempty"`
	RelatedPostIDs     []uint                `json:"related_post_ids"` // Always return array of manual related post IDs
	SEOMetadata        *SEOMetadata          `json:"seo,omitempty"`    // SEO metadata
	CreatedAt          time.Time             `json:"created_at"`
	UpdatedAt          time.Time             `json:"updated_at"`
	Category           *BlogCategoryResponse `json:"category,omitempty"`
	Tags               []BlogTagResponse     `json:"tags,omitempty"`
}

// FromBlogPost converts a BlogPost model to BlogPostResponse
func (bpr *BlogPostResponse) FromBlogPost(post *BlogPost) {
	bpr.ID = post.ID
	bpr.TenantID = post.TenantID
	bpr.WebsiteID = post.WebsiteID
	bpr.Slug = post.Slug
	bpr.Title = post.Title
	bpr.Content = post.Content
	bpr.Excerpt = post.Excerpt
	bpr.AuthorID = post.AuthorID
	bpr.CategoryID = post.CategoryID
	bpr.Type = post.Type
	bpr.IsFeatured = post.IsFeatured
	bpr.AllowComments = post.AllowComments
	bpr.FeaturedImage = post.FeaturedImage
	bpr.ViewCount = post.ViewCount
	bpr.ScheduledAt = post.ScheduledAt
	bpr.PublishedAt = post.PublishedAt
	bpr.Status = post.Status
	bpr.WorkflowState = post.WorkflowState
	bpr.WorkflowAssignedTo = post.WorkflowAssignedTo
	bpr.WorkflowAssignedAt = post.WorkflowAssignedAt
	bpr.WorkflowDueAt = post.WorkflowDueAt
	bpr.WorkflowNotes = post.WorkflowNotes
	bpr.CreatedAt = post.CreatedAt
	bpr.UpdatedAt = post.UpdatedAt

	// Convert relationships
	if post.Category != nil {
		categoryResponse := &BlogCategoryResponse{}
		categoryResponse.FromBlogCategory(post.Category)
		bpr.Category = categoryResponse
	}

	if len(post.Tags) > 0 {
		bpr.Tags = make([]BlogTagResponse, len(post.Tags))
		for i, tag := range post.Tags {
			bpr.Tags[i].FromBlogTag(&tag)
		}
	}
}

// BlogPostFilter represents filters for querying blog posts
type BlogPostFilter struct {
	TenantID      uint           `json:"tenant_id,omitempty"`
	WebsiteID     uint           `json:"website_id,omitempty"`
	AuthorID      uint           `json:"author_id,omitempty"`
	CategoryID    *uint          `json:"category_id,omitempty"`
	Type          BlogPostType   `json:"type,omitempty"`
	Status        BlogPostStatus `json:"status,omitempty"`
	IsFeatured    *bool          `json:"is_featured,omitempty"`
	AllowComments *bool          `json:"allow_comments,omitempty"`
	TagIDs        []uint         `json:"tag_ids,omitempty"`
	Search        string         `json:"search,omitempty"`
	DateFrom      *time.Time     `json:"date_from,omitempty"`
	DateTo        *time.Time     `json:"date_to,omitempty"`
	Page          int            `json:"page,omitempty"`
	PageSize      int            `json:"page_size,omitempty"`
	SortBy        string         `json:"sort_by,omitempty"`
	SortOrder     string         `json:"sort_order,omitempty"`
}

// BlogPostStats represents blog post statistics
type BlogPostStats struct {
	TotalPosts     int `json:"total_posts"`
	PublishedPosts int `json:"published_posts"`
	DraftPosts     int `json:"draft_posts"`
	ScheduledPosts int `json:"scheduled_posts"`
	FeaturedPosts  int `json:"featured_posts"`
	TotalViews     int `json:"total_views"`
}

// BlogPostWorkflowHistory represents the workflow history for a blog post
type BlogPostWorkflowHistory struct {
	ID         uint          `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID   uint          `gorm:"not null;index" json:"tenant_id"`
	PostID     uint          `gorm:"not null;index" json:"post_id"`
	FromState  WorkflowState `gorm:"type:varchar(50)" json:"from_state"`
	ToState    WorkflowState `gorm:"type:varchar(50);not null" json:"to_state"`
	UserID     uint          `gorm:"not null;index" json:"user_id"`
	AssignedTo *uint         `gorm:"index" json:"assigned_to,omitempty"`
	Notes      string        `gorm:"type:text" json:"notes,omitempty"`
	CreatedAt  time.Time     `json:"created_at"`

	// Relationships
	Post *BlogPost `gorm:"foreignKey:PostID" json:"post,omitempty"`
}
