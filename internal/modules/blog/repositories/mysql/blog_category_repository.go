package mysql

import (
	"context"
	"fmt"
	"strconv"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogCategoryRepository implements the blog category repository interface using MySQL
type BlogCategoryRepository struct {
	db *gorm.DB
}

// NewBlogCategoryRepository creates a new instance of BlogCategoryRepository
func NewBlogCategoryRepository(db *gorm.DB) repositories.BlogCategoryRepository {
	return &BlogCategoryRepository{db: db}
}

// Create creates a new blog category
func (r *BlogCategoryRepository) Create(ctx context.Context, category *models.BlogCategory) error {
	return r.db.WithContext(ctx).Create(category).Error
}

// GetByID gets a blog category by its ID and tenant ID
func (r *BlogCategoryRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogCategory, error) {
	var category models.BlogCategory
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, "deleted").
		Preload("Parent").
		Preload("Children", "status != ?", "deleted").
		First(&category).Error

	if err != nil {
		return nil, err
	}
	return &category, nil
}

// GetBySlug gets a blog category by its slug, tenant ID, and website ID
func (r *BlogCategoryRepository) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogCategory, error) {
	var category models.BlogCategory
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND slug = ? AND status != ?", tenantID, websiteID, slug, "deleted").
		Preload("Parent").
		Preload("Children", "status != ?", "deleted").
		First(&category).Error

	if err != nil {
		return nil, err
	}
	return &category, nil
}

// Update updates a blog category
func (r *BlogCategoryRepository) Update(ctx context.Context, tenantID, id uint, category *models.BlogCategory) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(category).Error
}

// Delete soft deletes a blog category by setting status to 'deleted'
func (r *BlogCategoryRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogCategory{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("status", "deleted").Error
}

// List retrieves blog categories with filters and pagination
func (r *BlogCategoryRepository) List(ctx context.Context, filter *models.BlogCategoryFilter) ([]models.BlogCategory, int64, error) {
	var categories []models.BlogCategory
	var total int64

	query := r.db.WithContext(ctx).Model(&models.BlogCategory{}).
		Where("status != ?", "deleted")

	// Apply filters
	if filter.TenantID != 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID != 0 {
		query = query.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.ParentID != nil {
		query = query.Where("parent_id = ?", *filter.ParentID)
	}
	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.Search != "" {
		searchTerm := "%" + filter.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchTerm, searchTerm)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := "sort_order"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}
	sortOrder := "ASC"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// Execute query with preloads
	err := query.
		Preload("Parent").
		Preload("Children", "status != ?", "deleted").
		Find(&categories).Error

	return categories, total, err
}

// ListWithCursor retrieves blog categories with cursor-based pagination
func (r *BlogCategoryRepository) ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogCategory, *pagination.CursorResponse, error) {
	var categories []models.BlogCategory

	// Base query with tenant scoping
	query := r.db.WithContext(ctx).Model(&models.BlogCategory{}).
		Where("tenant_id = ? AND status != ?", tenantID, "deleted")

	// Apply filters
	if websiteID, ok := filters["website_id"].(uint); ok && websiteID > 0 {
		query = query.Where("website_id = ?", websiteID)
	}
	if parentID, ok := filters["parent_id"].(*uint); ok && parentID != nil {
		query = query.Where("parent_id = ?", *parentID)
	}
	if isActive, ok := filters["is_active"].(*bool); ok && isActive != nil {
		query = query.Where("is_active = ?", *isActive)
	}
	if status, ok := filters["status"].(string); ok && status != "" {
		query = query.Where("status = ?", status)
	}
	if search, ok := filters["search"].(string); ok && search != "" {
		searchTerm := "%" + search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchTerm, searchTerm)
	}

	// Apply cursor pagination - using created_at DESC, id DESC for consistent ordering
	sortBy := "created_at"
	sortOrder := "DESC"
	if sortByFilter, ok := filters["sort_by"].(string); ok && sortByFilter != "" {
		sortBy = sortByFilter
	}
	if sortOrderFilter, ok := filters["sort_order"].(string); ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter
	}

	// Apply cursor logic
	if req.Cursor != "" {
		if sortOrder == "DESC" {
			query = query.Where("created_at < ? OR (created_at = ? AND id < ?)",
				req.Cursor, req.Cursor, req.Cursor)
		} else {
			query = query.Where("created_at > ? OR (created_at = ? AND id > ?)",
				req.Cursor, req.Cursor, req.Cursor)
		}
	}

	// Apply sorting and limit
	query = query.Order(fmt.Sprintf("%s %s, id %s", sortBy, sortOrder, sortOrder)).
		Limit(req.Limit + 1) // +1 to check if there are more results

	// Execute query with preloads
	err := query.
		Preload("Parent").
		Preload("Children", "status != ?", "deleted").
		Find(&categories).Error

	if err != nil {
		return nil, nil, err
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(categories) > req.Limit,
		Limit:   req.Limit,
	}

	if response.HasNext && len(categories) > 0 {
		categories = categories[:req.Limit]
		if len(categories) > 0 {
			lastCategory := categories[len(categories)-1]
			response.NextCursor = strconv.FormatUint(uint64(lastCategory.ID), 10)
		}
	}

	return categories, response, nil
}

// GetByParentID gets categories by parent ID
func (r *BlogCategoryRepository) GetByParentID(ctx context.Context, tenantID, websiteID uint, parentID *uint) ([]models.BlogCategory, error) {
	var categories []models.BlogCategory
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted").
		Order("sort_order ASC")

	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}

	err := query.Find(&categories).Error
	return categories, err
}

// GetHierarchy gets the complete category hierarchy for a website
func (r *BlogCategoryRepository) GetHierarchy(ctx context.Context, tenantID, websiteID uint) ([]models.BlogCategory, error) {
	var categories []models.BlogCategory
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted").
		Order("lft ASC").
		Preload("Children", "status != ?", "deleted").
		Find(&categories).Error

	return categories, err
}

// MoveCategory moves a category to a new parent (Nested Set Model implementation)
func (r *BlogCategoryRepository) MoveCategory(ctx context.Context, tenantID, categoryID, newParentID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get the category to move
		var category models.BlogCategory
		if err := tx.Where("tenant_id = ? AND id = ?", tenantID, categoryID).First(&category).Error; err != nil {
			return err
		}

		// Get the new parent category
		var newParent models.BlogCategory
		if err := tx.Where("tenant_id = ? AND id = ?", tenantID, newParentID).First(&newParent).Error; err != nil {
			return err
		}

		// Calculate width of the category subtree
		width := category.Rgt - category.Lft + 1

		// Create space for the category subtree at the new location
		if err := tx.Model(&models.BlogCategory{}).
			Where("tenant_id = ? AND rgt >= ?", tenantID, newParent.Rgt).
			Update("rgt", gorm.Expr("rgt + ?", width)).Error; err != nil {
			return err
		}

		if err := tx.Model(&models.BlogCategory{}).
			Where("tenant_id = ? AND lft > ?", tenantID, newParent.Rgt).
			Update("lft", gorm.Expr("lft + ?", width)).Error; err != nil {
			return err
		}

		// Calculate the offset for moving the category subtree
		offset := newParent.Rgt - category.Lft

		// Move the category subtree to the new location
		if err := tx.Model(&models.BlogCategory{}).
			Where("tenant_id = ? AND lft >= ? AND rgt <= ?", tenantID, category.Lft, category.Rgt).
			Updates(map[string]interface{}{
				"lft":       gorm.Expr("lft + ?", offset),
				"rgt":       gorm.Expr("rgt + ?", offset),
				"level":     gorm.Expr("level + ?", newParent.Level+1-category.Level),
				"parent_id": newParentID,
			}).Error; err != nil {
			return err
		}

		// Close the gap left by the moved category subtree
		if err := tx.Model(&models.BlogCategory{}).
			Where("tenant_id = ? AND lft > ?", tenantID, category.Rgt).
			Update("lft", gorm.Expr("lft - ?", width)).Error; err != nil {
			return err
		}

		if err := tx.Model(&models.BlogCategory{}).
			Where("tenant_id = ? AND rgt > ?", tenantID, category.Rgt).
			Update("rgt", gorm.Expr("rgt - ?", width)).Error; err != nil {
			return err
		}

		return nil
	})
}

// GetDescendants gets all descendants of a category
func (r *BlogCategoryRepository) GetDescendants(ctx context.Context, tenantID, categoryID uint) ([]models.BlogCategory, error) {
	var category models.BlogCategory
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, categoryID).
		First(&category).Error; err != nil {
		return nil, err
	}

	var descendants []models.BlogCategory
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND lft > ? AND rgt < ? AND status != ?", tenantID, category.Lft, category.Rgt, "deleted").
		Order("lft ASC").
		Find(&descendants).Error

	return descendants, err
}

// UpdatePositions updates category positions for reordering
func (r *BlogCategoryRepository) UpdatePositions(ctx context.Context, tenantID uint, positions []repositories.CategoryPosition) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, pos := range positions {
			if err := tx.Model(&models.BlogCategory{}).
				Where("tenant_id = ? AND id = ?", tenantID, pos.ID).
				Updates(map[string]interface{}{
					"parent_id":  pos.ParentID,
					"sort_order": pos.Position,
				}).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
