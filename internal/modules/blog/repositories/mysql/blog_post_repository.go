package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogPostRepository implements the blog post repository interface using MySQL
type BlogPostRepository struct {
	db *gorm.DB
}

// NewBlogPostRepository creates a new instance of BlogPostRepository
func NewBlogPostRepository(db *gorm.DB) repositories.BlogPostRepository {
	return &BlogPostRepository{db: db}
}

// Create creates a new blog post
func (r *BlogPostRepository) Create(ctx context.Context, post *models.BlogPost) error {
	return r.db.WithContext(ctx).Create(post).Error
}

// GetByID gets a blog post by its ID and tenant ID
func (r *BlogPostRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPost, error) {
	var post models.BlogPost
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, "deleted").
		Preload("Category").
		//Preload("Tags").
		First(&post).Error

	if err != nil {
		return nil, err
	}
	return &post, nil
}

// GetBySlug gets a blog post by its slug, tenant ID, and website ID
func (r *BlogPostRepository) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogPost, error) {
	var post models.BlogPost
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND slug = ? AND status != ?", tenantID, websiteID, slug, "deleted").
		Preload("Category").
		Preload("Tags").
		First(&post).Error

	if err != nil {
		return nil, err
	}
	return &post, nil
}

// Update updates a blog post
func (r *BlogPostRepository) Update(ctx context.Context, tenantID, id uint, post *models.BlogPost) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(post).Error
}

// Delete soft deletes a blog post by setting status to 'deleted'
func (r *BlogPostRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogPost{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("status", "deleted").Error
}

// List retrieves blog posts with filters and pagination
func (r *BlogPostRepository) List(ctx context.Context, filter *models.BlogPostFilter) ([]models.BlogPost, int64, error) {
	var posts []models.BlogPost
	var total int64

	query := r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("status != ?", "deleted")

	// Apply filters
	if filter.TenantID != 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID != 0 {
		query = query.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.AuthorID != 0 {
		query = query.Where("author_id = ?", filter.AuthorID)
	}
	if filter.CategoryID != nil {
		query = query.Where("category_id = ?", *filter.CategoryID)
	}
	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.IsFeatured != nil {
		query = query.Where("is_featured = ?", *filter.IsFeatured)
	}
	if filter.AllowComments != nil {
		query = query.Where("allow_comments = ?", *filter.AllowComments)
	}
	if filter.Search != "" {
		searchTerm := "%" + filter.Search + "%"
		query = query.Where("title LIKE ? OR content LIKE ? OR excerpt LIKE ?", searchTerm, searchTerm, searchTerm)
	}
	if filter.DateFrom != nil {
		query = query.Where("created_at >= ?", *filter.DateFrom)
	}
	if filter.DateTo != nil {
		query = query.Where("created_at <= ?", *filter.DateTo)
	}

	// Handle tag filtering
	if len(filter.TagIDs) > 0 {
		query = query.Joins("JOIN blog_post_tags ON blog_posts.id = blog_post_tags.post_id").
			Where("blog_post_tags.tag_id IN ?", filter.TagIDs).
			Distinct("blog_posts.id")
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := "created_at"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}
	sortOrder := "DESC"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// Execute query with preloads
	err := query.
		Preload("Category").
		Preload("Tags").
		Find(&posts).Error

	return posts, total, err
}

// ListWithCursor retrieves blog posts using cursor-based pagination
func (r *BlogPostRepository) ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPost, *pagination.CursorResponse, error) {
	var posts []models.BlogPost

	query := r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted")

	// Apply filters
	if title, ok := filters["title"].(string); ok && title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}
	if status, ok := filters["status"].(models.BlogPostStatus); ok {
		query = query.Where("status = ?", status)
	}
	if postType, ok := filters["type"].(models.BlogPostType); ok {
		query = query.Where("type = ?", postType)
	}
	if categoryID, ok := filters["category_id"].(uint); ok && categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}
	if authorID, ok := filters["author_id"].(uint); ok && authorID > 0 {
		query = query.Where("author_id = ?", authorID)
	}
	if isFeatured, ok := filters["is_featured"].(bool); ok {
		query = query.Where("is_featured = ?", isFeatured)
	}
	if tagID, ok := filters["tag_id"].(uint); ok && tagID > 0 {
		query = query.Joins("JOIN blog_post_tags ON blog_posts.id = blog_post_tags.post_id").
			Where("blog_post_tags.tag_id = ?", tagID)
	}
	if dateFrom, ok := filters["date_from"].(time.Time); ok {
		query = query.Where("created_at >= ?", dateFrom)
	}
	if dateTo, ok := filters["date_to"].(time.Time); ok {
		query = query.Where("created_at <= ?", dateTo)
	}

	// Apply sorting
	sortBy := "created_at"
	if sortByFilter, ok := filters["sort_by"].(string); ok && sortByFilter != "" {
		sortBy = sortByFilter
	}
	sortOrder := "desc"
	if sortOrderFilter, ok := filters["sort_order"].(string); ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter
	}

	// Parse cursor for pagination
	var cursorID uint
	var cursorTime time.Time
	if req.Cursor != "" {
		if parsed, err := pagination.ParseCursor(req.Cursor); err == nil {
			cursorID = parsed.ID
			cursorTime = parsed.CreatedAt
		}
	}

	// Apply cursor-based pagination
	if cursorID > 0 {
		if sortBy == "created_at" {
			if sortOrder == "desc" {
				query = query.Where("created_at < ?", cursorTime)
			} else {
				query = query.Where("created_at > ?", cursorTime)
			}
		} else {
			// For other sort fields, use ID as fallback
			if sortOrder == "desc" {
				query = query.Where("id < ?", cursorID)
			} else {
				query = query.Where("id > ?", cursorID)
			}
		}
	}

	// Add one extra to check if there are more results
	limit := req.Limit + 1
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder)).Limit(limit)

	// Execute query with preloads
	err := query.
		Preload("Category").
		//Preload("Tags").
		Find(&posts).Error

	if err != nil {
		return nil, nil, err
	}

	// Check if there are more results
	hasNext := len(posts) > req.Limit
	if hasNext {
		posts = posts[:req.Limit] // Remove the extra item
	}

	// Build pagination response
	var nextCursor string
	if hasNext && len(posts) > 0 {
		lastPost := posts[len(posts)-1]
		nextCursor, _ = pagination.EncodeCursor(lastPost.ID, lastPost.CreatedAt)
	}

	paginationResp := &pagination.CursorResponse{
		NextCursor: nextCursor,
		HasNext:    hasNext,
		Limit:      req.Limit,
	}

	return posts, paginationResp, nil
}

// GetPublished gets published blog posts for public consumption
func (r *BlogPostRepository) GetPublished(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]models.BlogPost, int64, error) {
	var posts []models.BlogPost
	var total int64

	query := r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, "published")

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get posts with pagination
	err := query.
		Order("published_at DESC").
		Offset(offset).
		Limit(limit).
		Preload("Category").
		Preload("Tags").
		Find(&posts).Error

	return posts, total, err
}

// GetFeatured gets featured blog posts
func (r *BlogPostRepository) GetFeatured(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogPost, error) {
	var posts []models.BlogPost
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ? AND is_featured = ?", tenantID, websiteID, "published", true).
		Order("published_at DESC").
		Limit(limit).
		Preload("Category").
		Preload("Tags").
		Find(&posts).Error

	return posts, err
}

// GetRelated gets related blog posts based on tags and categories
func (r *BlogPostRepository) GetRelated(ctx context.Context, tenantID, postID uint, limit int) ([]models.BlogPost, error) {
	// First get the current post to find its tags and category
	var currentPost models.BlogPost
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		Preload("Tags").
		First(&currentPost).Error; err != nil {
		return nil, err
	}

	var posts []models.BlogPost
	query := r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("tenant_id = ? AND id != ? AND status = ?", tenantID, postID, "published")

	// Find posts with shared tags or same category
	if len(currentPost.Tags) > 0 {
		tagIDs := make([]uint, len(currentPost.Tags))
		for i, tag := range currentPost.Tags {
			tagIDs[i] = tag.ID
		}
		query = query.Joins("JOIN blog_post_tags ON blog_posts.id = blog_post_tags.post_id").
			Where("blog_post_tags.tag_id IN ?", tagIDs)
	} else if currentPost.CategoryID != nil {
		query = query.Where("category_id = ?", *currentPost.CategoryID)
	}

	err := query.
		Distinct("blog_posts.*").
		Order("published_at DESC").
		Limit(limit).
		Preload("Category").
		Preload("Tags").
		Find(&posts).Error

	return posts, err
}

// AttachTags attaches tags to a blog post
func (r *BlogPostRepository) AttachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	if len(tagIDs) == 0 {
		return nil
	}

	var post models.BlogPost
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		First(&post).Error; err != nil {
		return err
	}

	var tags []models.BlogTag
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id IN ?", tenantID, tagIDs).
		Find(&tags).Error; err != nil {
		return err
	}

	return r.db.WithContext(ctx).Model(&post).Association("Tags").Append(&tags)
}

// DetachTags detaches tags from a blog post
func (r *BlogPostRepository) DetachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	if len(tagIDs) == 0 {
		return nil
	}

	var post models.BlogPost
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		First(&post).Error; err != nil {
		return err
	}

	var tags []models.BlogTag
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id IN ?", tenantID, tagIDs).
		Find(&tags).Error; err != nil {
		return err
	}

	return r.db.WithContext(ctx).Model(&post).Association("Tags").Delete(&tags)
}

// SyncTags synchronizes tags for a blog post (replaces all existing tags)
func (r *BlogPostRepository) SyncTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	var post models.BlogPost
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		First(&post).Error; err != nil {
		return err
	}

	var tags []models.BlogTag
	if len(tagIDs) > 0 {
		if err := r.db.WithContext(ctx).
			Where("tenant_id = ? AND id IN ?", tenantID, tagIDs).
			Find(&tags).Error; err != nil {
			return err
		}
	}

	return r.db.WithContext(ctx).Model(&post).Association("Tags").Replace(&tags)
}

// GetPostTags gets all tags for a specific blog post
func (r *BlogPostRepository) GetPostTags(ctx context.Context, tenantID, postID uint) ([]models.BlogTag, error) {
	var post models.BlogPost
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		Preload("Tags").
		First(&post).Error; err != nil {
		return nil, err
	}

	return post.Tags, nil
}

// GetRelatedPostIDs gets the manual related post IDs for a specific blog post
func (r *BlogPostRepository) GetRelatedPostIDs(ctx context.Context, tenantID, postID uint) ([]uint, error) {
	var post models.BlogPost
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		Select("related_post_ids").
		First(&post).Error; err != nil {
		return nil, err
	}

	// Parse JSON to []uint
	var relatedIDs []uint
	if post.RelatedPostIDs != nil {
		// Handle the case where JSON is stored as a map with an array value
		// or we need to use proper JSON unmarshaling
		// For now, return empty slice - this should be properly implemented with JSON handling
		relatedIDs = make([]uint, 0)
	}

	return relatedIDs, nil
}

// SyncRelatedPosts synchronizes manual related post IDs for a blog post
func (r *BlogPostRepository) SyncRelatedPosts(ctx context.Context, tenantID, postID uint, relatedPostIDs []uint) error {
	// Convert []uint to JSON format for storage
	var jsonData interface{}
	if len(relatedPostIDs) > 0 {
		jsonData = relatedPostIDs
	} else {
		jsonData = []uint{} // Empty array
	}

	return r.db.WithContext(ctx).
		Model(&models.BlogPost{}).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		Update("related_post_ids", jsonData).Error
}

// GetRelatedPostsByIDs gets blog posts by their IDs (for manual related posts)
func (r *BlogPostRepository) GetRelatedPostsByIDs(ctx context.Context, tenantID uint, postIDs []uint) ([]models.BlogPost, error) {
	if len(postIDs) == 0 {
		return []models.BlogPost{}, nil
	}

	var posts []models.BlogPost
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id IN ? AND status = ?", tenantID, postIDs, "published").
		Order("published_at DESC").
		Preload("Category").
		Preload("Tags").
		Find(&posts).Error

	return posts, err
}

// IncrementViewCount increments the view count of a blog post
func (r *BlogPostRepository) IncrementViewCount(ctx context.Context, tenantID, postID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogPost{}).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		Update("view_count", gorm.Expr("view_count + 1")).Error
}

// GetStats gets blog post statistics for a website
func (r *BlogPostRepository) GetStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogPostStats, error) {
	var stats models.BlogPostStats

	// Total posts
	r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted").
		Count(&[]int64{int64(stats.TotalPosts)}[0])

	// Published posts
	r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, "published").
		Count(&[]int64{int64(stats.PublishedPosts)}[0])

	// Draft posts
	r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, "draft").
		Count(&[]int64{int64(stats.DraftPosts)}[0])

	// Scheduled posts
	r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, "scheduled").
		Count(&[]int64{int64(stats.ScheduledPosts)}[0])

	// Featured posts
	r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("tenant_id = ? AND website_id = ? AND status = ? AND is_featured = ?", tenantID, websiteID, "published", true).
		Count(&[]int64{int64(stats.FeaturedPosts)}[0])

	// Total views
	var totalViews int64
	r.db.WithContext(ctx).Model(&models.BlogPost{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted").
		Select("COALESCE(SUM(view_count), 0)").
		Scan(&totalViews)
	stats.TotalViews = int(totalViews)

	return &stats, nil
}

// GetPopular gets popular blog posts based on view count over a period
func (r *BlogPostRepository) GetPopular(ctx context.Context, tenantID, websiteID uint, days int, limit int) ([]models.BlogPost, error) {
	var posts []models.BlogPost
	sinceDate := time.Now().AddDate(0, 0, -days)

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ? AND published_at >= ?",
			tenantID, websiteID, "published", sinceDate).
		Order("view_count DESC").
		Limit(limit).
		Preload("Category").
		Preload("Tags").
		Find(&posts).Error

	return posts, err
}

// Publish publishes a blog post
func (r *BlogPostRepository) Publish(ctx context.Context, tenantID, postID uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&models.BlogPost{}).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		Updates(map[string]interface{}{
			"status":       "published",
			"published_at": now,
		}).Error
}

// Unpublish unpublishes a blog post
func (r *BlogPostRepository) Unpublish(ctx context.Context, tenantID, postID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogPost{}).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		Updates(map[string]interface{}{
			"status":       "draft",
			"published_at": nil,
		}).Error
}

// Schedule schedules a blog post for future publication
func (r *BlogPostRepository) Schedule(ctx context.Context, tenantID, postID uint, scheduledAt *time.Time) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogPost{}).
		Where("tenant_id = ? AND id = ?", tenantID, postID).
		Updates(map[string]interface{}{
			"status":       "scheduled",
			"scheduled_at": scheduledAt,
		}).Error
}

// GetScheduled gets all scheduled blog posts that are ready to be published
func (r *BlogPostRepository) GetScheduled(ctx context.Context) ([]models.BlogPost, error) {
	var posts []models.BlogPost
	now := time.Now()

	err := r.db.WithContext(ctx).
		Where("status = ? AND scheduled_at <= ?", "scheduled", now).
		Find(&posts).Error

	return posts, err
}

// FindByWorkflowFilter retrieves posts based on workflow filters with cursor pagination
func (r *BlogPostRepository) FindByWorkflowFilter(filter *models.WorkflowQueueFilter) ([]*models.BlogPost, string, error) {
	var posts []models.BlogPost

	// Validate and set default limit
	limit := filter.Limit
	if limit <= 0 {
		limit = pagination.DefaultLimit
	}
	if limit > pagination.MaxLimit {
		limit = pagination.MaxLimit
	}

	// Build base query
	query := r.db.Where("tenant_id = ? AND status != ?", filter.TenantID, models.BlogPostStatusDeleted)

	// Apply workflow state filter
	if filter.WorkflowState != "" {
		query = query.Where("workflow_state = ?", filter.WorkflowState)
	}

	// Apply assigned to filter
	if filter.AssignedTo != nil {
		query = query.Where("workflow_assigned_to = ?", *filter.AssignedTo)
	}

	// Apply overdue filter
	if filter.OverdueOnly {
		query = query.Where("workflow_due_at < ? AND workflow_state NOT IN (?)",
			time.Now(), []string{
				string(models.WorkflowStateCompleted),
				string(models.WorkflowStateRejected),
			})
	}

	// Apply unassigned filter
	if filter.UnassignedOnly {
		query = query.Where("workflow_assigned_to IS NULL")
	}

	// Apply cursor filter if provided
	if filter.Cursor != "" {
		cursorData, err := pagination.ParseCursor(filter.Cursor)
		if err != nil {
			return nil, "", err
		}

		// Apply cursor condition (posts after this cursor)
		query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))",
			cursorData.CreatedAt, cursorData.CreatedAt, cursorData.ID)
	}

	// Order by created_at desc, id desc for consistent pagination
	query = query.Order("created_at DESC, id DESC").Limit(limit + 1)

	// Execute query with preloads
	if err := query.Preload("Category").Preload("Tags").Find(&posts).Error; err != nil {
		return nil, "", err
	}

	// Check if there are more results
	hasMore := len(posts) > limit
	if hasMore {
		posts = posts[:limit] // Remove the extra item
	}

	// Generate next cursor if there are more results
	var nextCursor string
	if hasMore && len(posts) > 0 {
		lastPost := posts[len(posts)-1]
		nextCursor, _ = pagination.EncodeCursor(uint(lastPost.ID), lastPost.CreatedAt)
	}

	// Convert to pointer slice
	postPtrs := make([]*models.BlogPost, len(posts))
	for i := range posts {
		postPtrs[i] = &posts[i]
	}

	return postPtrs, nextCursor, nil
}

// GetByWorkflowState retrieves posts in a specific workflow state with cursor pagination
func (r *BlogPostRepository) GetByWorkflowState(ctx context.Context, tenantID uint, state models.WorkflowState, cursor string, limit int) ([]*models.BlogPost, string, error) {
	filter := &models.WorkflowQueueFilter{
		TenantID:      tenantID,
		WorkflowState: state,
		Cursor:        cursor,
		Limit:         limit,
	}

	return r.FindByWorkflowFilter(filter)
}

// GetAssignedTasks retrieves tasks assigned to a specific user with cursor pagination
func (r *BlogPostRepository) GetAssignedTasks(ctx context.Context, tenantID uint, userID uint, cursor string, limit int) ([]*models.BlogPost, string, error) {
	filter := &models.WorkflowQueueFilter{
		TenantID:   tenantID,
		AssignedTo: &userID,
		Cursor:     cursor,
		Limit:      limit,
	}

	return r.FindByWorkflowFilter(filter)
}

// GetOverdueTasks retrieves overdue tasks with cursor pagination
func (r *BlogPostRepository) GetOverdueTasks(ctx context.Context, tenantID uint, cursor string, limit int) ([]*models.BlogPost, string, error) {
	filter := &models.WorkflowQueueFilter{
		TenantID:    tenantID,
		OverdueOnly: true,
		Cursor:      cursor,
		Limit:       limit,
	}

	return r.FindByWorkflowFilter(filter)
}

// GetBatch gets a batch of blog posts for search indexing
func (r *BlogPostRepository) GetBatch(ctx context.Context, offset, limit int) ([]*models.BlogPost, error) {
	var posts []*models.BlogPost
	err := r.db.WithContext(ctx).
		Where("status != ?", "deleted").
		Preload("Category").
		Preload("Tags").
		Offset(offset).
		Limit(limit).
		Order("id ASC").
		Find(&posts).Error

	return posts, err
}

// GetUpdatedSince gets blog posts updated since a specific time
func (r *BlogPostRepository) GetUpdatedSince(ctx context.Context, since time.Time) ([]*models.BlogPost, error) {
	var posts []*models.BlogPost
	err := r.db.WithContext(ctx).
		Where("updated_at > ? AND status != ?", since, "deleted").
		Preload("Category").
		Preload("Tags").
		Order("updated_at ASC").
		Find(&posts).Error

	return posts, err
}

// Count gets the total count of blog posts
func (r *BlogPostRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.BlogPost{}).
		Where("status != ?", "deleted").
		Count(&count).Error

	return count, err
}

// === Blog Post Related Table Methods ===

// CreateRelatedPostEntry creates a new entry in the blog_post_related table
func (r *BlogPostRepository) CreateRelatedPostEntry(ctx context.Context, relation *models.BlogPostRelated) error {
	return r.db.WithContext(ctx).Create(relation).Error
}

// GetRelatedPostEntries gets related post entries from blog_post_related table
func (r *BlogPostRepository) GetRelatedPostEntries(ctx context.Context, tenantID, postID uint) ([]models.BlogPostRelated, error) {
	var relations []models.BlogPostRelated
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND post_id = ?", tenantID, postID).
		Preload("RelatedPost").
		Find(&relations).Error

	return relations, err
}

// GetRelatedPostEntriesByType gets related post entries by relation type
func (r *BlogPostRepository) GetRelatedPostEntriesByType(ctx context.Context, tenantID, postID uint, relationType models.BlogPostRelationType) ([]models.BlogPostRelated, error) {
	var relations []models.BlogPostRelated
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND post_id = ? AND relation_type = ?", tenantID, postID, relationType).
		Preload("RelatedPost").
		Find(&relations).Error

	return relations, err
}

// UpdateRelatedPostEntry updates a related post entry
func (r *BlogPostRepository) UpdateRelatedPostEntry(ctx context.Context, tenantID, postID, relatedPostID uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogPostRelated{}).
		Where("tenant_id = ? AND post_id = ? AND related_post_id = ?", tenantID, postID, relatedPostID).
		Updates(updates).Error
}

// DeleteRelatedPostEntry deletes a specific related post entry
func (r *BlogPostRepository) DeleteRelatedPostEntry(ctx context.Context, tenantID, postID, relatedPostID uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND post_id = ? AND related_post_id = ?", tenantID, postID, relatedPostID).
		Delete(&models.BlogPostRelated{}).Error
}

// DeleteAllRelatedPostEntries deletes all related post entries for a post
func (r *BlogPostRepository) DeleteAllRelatedPostEntries(ctx context.Context, tenantID, postID uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND post_id = ?", tenantID, postID).
		Delete(&models.BlogPostRelated{}).Error
}

// SyncRelatedPostEntries synchronizes related post entries with the given relations
func (r *BlogPostRepository) SyncRelatedPostEntries(ctx context.Context, tenantID, postID uint, relations []models.BlogPostRelated) error {
	// Start transaction
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Delete existing relations
	if err := tx.Where("tenant_id = ? AND post_id = ?", tenantID, postID).Delete(&models.BlogPostRelated{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create new relations
	if len(relations) > 0 {
		if err := tx.Create(&relations).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}
