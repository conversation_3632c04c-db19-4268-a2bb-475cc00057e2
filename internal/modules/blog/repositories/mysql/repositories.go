package mysql

import (
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// Repositories contains all blog repository implementations
type Repositories struct {
	Category      repositories.BlogCategoryRepository
	Tag           repositories.BlogTagRepository
	Post          repositories.BlogPostRepository
	Schedule      repositories.BlogPostScheduleRepository
	Revision      repositories.BlogPostRevisionRepository
	HomepageBlock repositories.HomepageBlockRepository
	BlockTemplate repositories.BlockTemplateRepository
	Newsletter    repositories.BlogNewsletterSubscriptionRepository
	Workflow      repositories.WorkflowRepository
	Rating        repositories.BlogPostRatingRepository
	Author        repositories.BlogPostAuthorRepository
	Royalty       repositories.BlogPostRoyaltyRepository
}

// NewRepositories creates a new instance of blog repositories
func NewRepositories(db *gorm.DB, logger utils.Logger) *Repositories {
	return &Repositories{
		Category:      NewBlogCategoryRepository(db),
		Tag:           NewBlogTagRepository(db),
		Post:          NewBlogPostRepository(db),
		Schedule:      NewBlogPostScheduleRepository(db),
		Revision:      NewBlogPostRevisionRepository(db),
		HomepageBlock: NewHomepageBlockRepository(db, logger),
		BlockTemplate: NewBlockTemplateRepository(db, logger),
		Newsletter:    NewBlogNewsletterSubscriptionRepository(db),
		Workflow:      NewWorkflowRepository(db),
		Rating:        NewBlogPostRatingRepository(db),
		Author:        NewBlogPostAuthorRepository(db),
		Royalty:       NewBlogPostRoyaltyRepository(db),
	}
}
