package mysql

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

// BlogPostTemplateRepository implements the BlogPostTemplateRepository interface
type BlogPostTemplateRepository struct {
	db *gorm.DB
}

// NewBlogPostTemplateRepository creates a new instance of BlogPostTemplateRepository
func NewBlogPostTemplateRepository(db *gorm.DB) repositories.BlogPostTemplateRepository {
	return &BlogPostTemplateRepository{db: db}
}

// Create creates a new blog post template
func (r *BlogPostTemplateRepository) Create(ctx context.Context, template *models.BlogPostTemplate) error {
	// DefaultTags is already models.JSON type (map[string]interface{}) which handles JSON conversion automatically

	return r.db.WithContext(ctx).Create(template).Error
}

// GetByID retrieves a blog post template by ID
func (r *BlogPostTemplateRepository) GetByID(ctx context.Context, id uint) (*models.BlogPostTemplate, error) {
	var template models.BlogPostTemplate
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// GetBySlug retrieves a blog post template by slug and scope
func (r *BlogPostTemplateRepository) GetBySlug(ctx context.Context, slug string, scope models.BlogPostTemplateScope, scopeID *uint) (*models.BlogPostTemplate, error) {
	var template models.BlogPostTemplate
	query := r.db.WithContext(ctx).Where("slug = ? AND scope = ?", slug, scope)

	switch scope {
	case models.BlogPostTemplateScopeSystem:
		query = query.Where("tenant_id IS NULL AND website_id IS NULL")
	case models.BlogPostTemplateScopeTenant:
		if scopeID == nil {
			return nil, errors.New("tenant ID required for tenant scope")
		}
		query = query.Where("tenant_id = ? AND website_id IS NULL", *scopeID)
	case models.BlogPostTemplateScopeWebsite:
		if scopeID == nil {
			return nil, errors.New("website ID required for website scope")
		}
		query = query.Where("website_id = ?", *scopeID)
	case models.BlogPostTemplateScopeUser:
		if scopeID == nil {
			return nil, errors.New("user ID required for user scope")
		}
		query = query.Where("created_by = ?", *scopeID)
	}

	err := query.First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// Update updates an existing blog post template
func (r *BlogPostTemplateRepository) Update(ctx context.Context, id uint, template *models.BlogPostTemplate) error {
	// DefaultTags is already models.JSON type (map[string]interface{}) which handles JSON conversion automatically

	return r.db.WithContext(ctx).Where("id = ?", id).Updates(template).Error
}

// Delete soft deletes a blog post template
func (r *BlogPostTemplateRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.BlogPostTemplate{}, id).Error
}

// List retrieves blog post templates based on filter
func (r *BlogPostTemplateRepository) List(ctx context.Context, filter *models.BlogPostTemplateFilter) ([]models.BlogPostTemplate, int64, error) {
	var templates []models.BlogPostTemplate
	var total int64

	query := r.db.WithContext(ctx).Model(&models.BlogPostTemplate{})

	// Apply filters
	if filter.TenantID != nil {
		query = query.Where("tenant_id = ?", *filter.TenantID)
	}
	if filter.WebsiteID != nil {
		query = query.Where("website_id = ?", *filter.WebsiteID)
	}
	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}
	if filter.Scope != "" {
		query = query.Where("scope = ?", filter.Scope)
	}
	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}
	if filter.IsFeatured != nil {
		query = query.Where("is_featured = ?", *filter.IsFeatured)
	}
	if filter.CreatedBy != nil {
		query = query.Where("created_by = ?", *filter.CreatedBy)
	}
	if filter.Search != "" {
		searchPattern := "%" + filter.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchPattern, searchPattern)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	// Order by featured first, then by usage count
	query = query.Order("is_featured DESC, usage_count DESC, created_at DESC")

	// Execute query
	if err := query.Find(&templates).Error; err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

// ListWithCursor retrieves blog post templates using cursor-based pagination
func (r *BlogPostTemplateRepository) ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPostTemplate, *pagination.CursorResponse, error) {
	var templates []models.BlogPostTemplate

	query := r.db.WithContext(ctx).Model(&models.BlogPostTemplate{})

	// Apply tenant and website filters
	if tenantID > 0 {
		query = query.Where("tenant_id = ? OR tenant_id IS NULL", tenantID)
	}
	if websiteID > 0 {
		query = query.Where("website_id = ? OR website_id IS NULL", websiteID)
	}

	// Apply filters
	if templateType, ok := filters["type"].(models.BlogPostTemplateType); ok {
		query = query.Where("type = ?", templateType)
	}
	if scope, ok := filters["scope"].(models.BlogPostTemplateScope); ok {
		query = query.Where("scope = ?", scope)
	}
	if isActive, ok := filters["is_active"].(bool); ok {
		query = query.Where("is_active = ?", isActive)
	}
	if isFeatured, ok := filters["is_featured"].(bool); ok {
		query = query.Where("is_featured = ?", isFeatured)
	}
	if createdBy, ok := filters["created_by"].(uint); ok && createdBy > 0 {
		query = query.Where("created_by = ?", createdBy)
	}
	if search, ok := filters["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchPattern, searchPattern)
	}

	// Apply sorting
	sortBy := "created_at"
	if sortByFilter, ok := filters["sort_by"].(string); ok && sortByFilter != "" {
		sortBy = sortByFilter
	}
	sortOrder := "desc"
	if sortOrderFilter, ok := filters["sort_order"].(string); ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter
	}

	// Parse cursor for pagination
	var cursorID uint
	var cursorTime time.Time
	if req.Cursor != "" {
		if parsed, err := pagination.ParseCursor(req.Cursor); err == nil {
			cursorID = parsed.ID
			cursorTime = parsed.CreatedAt
		}
	}

	// Apply cursor-based pagination
	if cursorID > 0 {
		if sortBy == "created_at" {
			if sortOrder == "desc" {
				query = query.Where("created_at < ?", cursorTime)
			} else {
				query = query.Where("created_at > ?", cursorTime)
			}
		} else {
			// For other sort fields, use ID as fallback
			if sortOrder == "desc" {
				query = query.Where("id < ?", cursorID)
			} else {
				query = query.Where("id > ?", cursorID)
			}
		}
	}

	// Add one extra to check if there are more results
	limit := req.Limit + 1
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder)).Limit(limit)

	// Execute query
	err := query.Find(&templates).Error
	if err != nil {
		return nil, nil, err
	}

	// Check if there are more results
	hasNext := len(templates) > req.Limit
	if hasNext {
		templates = templates[:req.Limit] // Remove the extra item
	}

	// Build pagination response
	var nextCursor string
	if hasNext && len(templates) > 0 {
		lastTemplate := templates[len(templates)-1]
		nextCursor, _ = pagination.EncodeCursor(lastTemplate.ID, lastTemplate.CreatedAt)
	}

	paginationResp := &pagination.CursorResponse{
		NextCursor: nextCursor,
		HasNext:    hasNext,
		Limit:      req.Limit,
	}

	return templates, paginationResp, nil
}

// ListAccessible retrieves all templates accessible by a user
func (r *BlogPostTemplateRepository) ListAccessible(ctx context.Context, tenantID, websiteID, userID uint) ([]models.BlogPostTemplate, error) {
	var templates []models.BlogPostTemplate

	// Build query for accessible templates
	query := r.db.WithContext(ctx).Where("is_active = ?", true).
		Where("(scope = ? OR (scope = ? AND tenant_id = ?) OR (scope = ? AND website_id = ?) OR (scope = ? AND created_by = ?))",
			models.BlogPostTemplateScopeSystem,
			models.BlogPostTemplateScopeTenant, tenantID,
			models.BlogPostTemplateScopeWebsite, websiteID,
			models.BlogPostTemplateScopeUser, userID)

	// Order by scope priority: user > website > tenant > system
	query = query.Order(`
		CASE 
			WHEN scope = 'user' THEN 1
			WHEN scope = 'website' THEN 2
			WHEN scope = 'tenant' THEN 3
			WHEN scope = 'system' THEN 4
		END,
		is_featured DESC,
		usage_count DESC,
		created_at DESC
	`)

	if err := query.Find(&templates).Error; err != nil {
		return nil, err
	}

	return templates, nil
}

// ListAccessibleWithCursor retrieves all templates accessible by a user using cursor-based pagination
func (r *BlogPostTemplateRepository) ListAccessibleWithCursor(ctx context.Context, tenantID, websiteID, userID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPostTemplate, *pagination.CursorResponse, error) {
	var templates []models.BlogPostTemplate

	// Build query for accessible templates
	query := r.db.WithContext(ctx).Where("is_active = ?", true).
		Where("(scope = ? OR (scope = ? AND tenant_id = ?) OR (scope = ? AND website_id = ?) OR (scope = ? AND created_by = ?))",
			models.BlogPostTemplateScopeSystem,
			models.BlogPostTemplateScopeTenant, tenantID,
			models.BlogPostTemplateScopeWebsite, websiteID,
			models.BlogPostTemplateScopeUser, userID)

	// Apply additional filters
	if templateType, ok := filters["type"].(models.BlogPostTemplateType); ok {
		query = query.Where("type = ?", templateType)
	}
	if isFeatured, ok := filters["is_featured"].(bool); ok {
		query = query.Where("is_featured = ?", isFeatured)
	}
	if search, ok := filters["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchPattern, searchPattern)
	}

	// Apply sorting
	sortBy := "created_at"
	if sortByFilter, ok := filters["sort_by"].(string); ok && sortByFilter != "" {
		sortBy = sortByFilter
	}
	sortOrder := "desc"
	if sortOrderFilter, ok := filters["sort_order"].(string); ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter
	}

	// Parse cursor for pagination
	var cursorID uint
	var cursorTime time.Time
	if req.Cursor != "" {
		if parsed, err := pagination.ParseCursor(req.Cursor); err == nil {
			cursorID = parsed.ID
			cursorTime = parsed.CreatedAt
		}
	}

	// Apply cursor-based pagination
	if cursorID > 0 {
		if sortBy == "created_at" {
			if sortOrder == "desc" {
				query = query.Where("created_at < ?", cursorTime)
			} else {
				query = query.Where("created_at > ?", cursorTime)
			}
		} else {
			// For other sort fields, use ID as fallback
			if sortOrder == "desc" {
				query = query.Where("id < ?", cursorID)
			} else {
				query = query.Where("id > ?", cursorID)
			}
		}
	}

	// Add one extra to check if there are more results
	limit := req.Limit + 1

	// Order by scope priority: user > website > tenant > system, then by other criteria
	query = query.Order(fmt.Sprintf(`
		CASE 
			WHEN scope = 'user' THEN 1
			WHEN scope = 'website' THEN 2
			WHEN scope = 'tenant' THEN 3
			WHEN scope = 'system' THEN 4
		END,
		is_featured DESC,
		usage_count DESC,
		%s %s
	`, sortBy, sortOrder)).Limit(limit)

	// Execute query
	err := query.Find(&templates).Error
	if err != nil {
		return nil, nil, err
	}

	// Check if there are more results
	hasNext := len(templates) > req.Limit
	if hasNext {
		templates = templates[:req.Limit] // Remove the extra item
	}

	// Build pagination response
	var nextCursor string
	if hasNext && len(templates) > 0 {
		lastTemplate := templates[len(templates)-1]
		nextCursor, _ = pagination.EncodeCursor(lastTemplate.ID, lastTemplate.CreatedAt)
	}

	paginationResp := &pagination.CursorResponse{
		NextCursor: nextCursor,
		HasNext:    hasNext,
		Limit:      req.Limit,
	}

	return templates, paginationResp, nil
}

// GetByType retrieves templates by type
func (r *BlogPostTemplateRepository) GetByType(ctx context.Context, templateType models.BlogPostTemplateType, tenantID *uint) ([]models.BlogPostTemplate, error) {
	var templates []models.BlogPostTemplate

	query := r.db.WithContext(ctx).Where("type = ? AND is_active = ?", templateType, true)

	if tenantID != nil {
		query = query.Where("(tenant_id = ? OR tenant_id IS NULL)", *tenantID)
	} else {
		query = query.Where("scope = ?", models.BlogPostTemplateScopeSystem)
	}

	query = query.Order("is_featured DESC, usage_count DESC")

	if err := query.Find(&templates).Error; err != nil {
		return nil, err
	}

	return templates, nil
}

// GetFeatured retrieves featured templates
func (r *BlogPostTemplateRepository) GetFeatured(ctx context.Context, tenantID *uint, limit int) ([]models.BlogPostTemplate, error) {
	var templates []models.BlogPostTemplate

	query := r.db.WithContext(ctx).Where("is_featured = ? AND is_active = ?", true, true)

	if tenantID != nil {
		query = query.Where("(tenant_id = ? OR tenant_id IS NULL)", *tenantID)
	} else {
		query = query.Where("scope = ?", models.BlogPostTemplateScopeSystem)
	}

	query = query.Order("usage_count DESC, created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&templates).Error; err != nil {
		return nil, err
	}

	return templates, nil
}

// IncrementUsage increments the usage count and updates last used time
func (r *BlogPostTemplateRepository) IncrementUsage(ctx context.Context, id uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&models.BlogPostTemplate{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"usage_count":  gorm.Expr("usage_count + ?", 1),
			"last_used_at": now,
		}).Error
}

// GetPopularTemplates retrieves the most used templates
func (r *BlogPostTemplateRepository) GetPopularTemplates(ctx context.Context, tenantID *uint, limit int) ([]models.BlogPostTemplate, error) {
	var templates []models.BlogPostTemplate

	query := r.db.WithContext(ctx).Where("is_active = ? AND usage_count > ?", true, 0)

	if tenantID != nil {
		query = query.Where("(tenant_id = ? OR tenant_id IS NULL)", *tenantID)
	} else {
		query = query.Where("scope = ?", models.BlogPostTemplateScopeSystem)
	}

	query = query.Order("usage_count DESC, last_used_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&templates).Error; err != nil {
		return nil, err
	}

	return templates, nil
}

// Duplicate creates a copy of an existing template
func (r *BlogPostTemplateRepository) Duplicate(ctx context.Context, id uint, newName, newSlug string, targetScope models.BlogPostTemplateScope, targetScopeID *uint) (*models.BlogPostTemplate, error) {
	// Get the original template
	original, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get original template: %w", err)
	}

	// Create a new template based on the original
	newTemplate := &models.BlogPostTemplate{
		Name:                 newName,
		Slug:                 newSlug,
		Description:          original.Description + " (Copy)",
		Type:                 original.Type,
		Scope:                targetScope,
		Icon:                 original.Icon,
		Color:                original.Color,
		IsActive:             true,
		IsFeatured:           false,
		TitleTemplate:        original.TitleTemplate,
		ContentTemplate:      original.ContentTemplate,
		ExcerptTemplate:      original.ExcerptTemplate,
		Structure:            original.Structure,
		DefaultType:          original.DefaultType,
		DefaultStatus:        original.DefaultStatus,
		DefaultCategoryID:    original.DefaultCategoryID,
		DefaultTags:          original.DefaultTags,
		DefaultAllowComments: original.DefaultAllowComments,

		UsageCount: 0,
		LastUsedAt: nil,
	}

	// Set scope-specific IDs
	switch targetScope {
	case models.BlogPostTemplateScopeTenant:
		newTemplate.TenantID = targetScopeID
	case models.BlogPostTemplateScopeWebsite:
		newTemplate.WebsiteID = targetScopeID
	case models.BlogPostTemplateScopeUser:
		if targetScopeID != nil {
			newTemplate.CreatedBy = *targetScopeID
		}
	}

	// Create the new template
	if err := r.Create(ctx, newTemplate); err != nil {
		return nil, fmt.Errorf("failed to create duplicate template: %w", err)
	}

	return newTemplate, nil
}
