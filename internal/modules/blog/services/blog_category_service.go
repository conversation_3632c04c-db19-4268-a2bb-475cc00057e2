package services

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

// blogCategoryService implements BlogCategoryService interface
type blogCategoryService struct {
	repo repositories.BlogCategoryRepository
}

// NewBlogCategoryService creates a new blog category service
func NewBlogCategoryService(repo repositories.BlogCategoryRepository) BlogCategoryService {
	return &blogCategoryService{
		repo: repo,
	}
}

// C<PERSON> creates a new blog category
func (s *blogCategoryService) Create(ctx context.Context, req *models.BlogCategoryCreateRequest) (*models.BlogCategoryResponse, error) {
	var parentIdPath string
	
	// Validate parent category if specified
	if req.ParentID != nil {
		parent, err := s.repo.GetByID(ctx, req.TenantID, *req.ParentID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("parent category not found")
			}
			return nil, fmt.Errorf("failed to validate parent category: %w", err)
		}
		if parent.WebsiteID != req.WebsiteID {
			return nil, fmt.Errorf("parent category must belong to the same website")
		}
		_ = parent
		parentIdPath = parent.IdPath
	}

	// Check for duplicate slug within the same website
	existing, err := s.repo.GetBySlug(ctx, req.TenantID, req.WebsiteID, req.Slug)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("category with slug '%s' already exists", req.Slug)
	}

	// Create the category model
	category := &models.BlogCategory{
		TenantID:    req.TenantID,
		WebsiteID:   req.WebsiteID,
		Slug:        req.Slug,
		Name:        req.Name,
		Description: req.Description,
		ParentID:    req.ParentID,
		Color:       req.Color,
		IsActive:    req.IsActive,
		SortOrder:   req.SortOrder,
		Status:      "active",
	}

	// Set default color if not provided
	if category.Color == "" {
		category.Color = "#000000"
	}

	// Create the category
	err = s.repo.Create(ctx, category)
	if err != nil {
		return nil, fmt.Errorf("failed to create category: %w", err)
	}
	
	// Build id_path after creation
	if parentIdPath != "" {
		category.IdPath = fmt.Sprintf("%s,%d", parentIdPath, category.ID)
	} else {
		category.IdPath = fmt.Sprintf("%d", category.ID)
	}
	
	// Update the category with id_path
	err = s.repo.Update(ctx, req.TenantID, category.ID, category)
	if err != nil {
		return nil, fmt.Errorf("failed to update category id_path: %w", err)
	}

	// Convert to response
	response := &models.BlogCategoryResponse{}
	response.FromBlogCategory(category)
	return response, nil
}

// GetByID retrieves a blog category by ID
func (s *blogCategoryService) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogCategoryResponse, error) {
	category, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("category not found")
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	response := &models.BlogCategoryResponse{}
	response.FromBlogCategory(category)
	return response, nil
}

// GetBySlug retrieves a blog category by slug
func (s *blogCategoryService) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogCategoryResponse, error) {
	category, err := s.repo.GetBySlug(ctx, tenantID, websiteID, slug)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("category not found")
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	response := &models.BlogCategoryResponse{}
	response.FromBlogCategory(category)
	return response, nil
}

// Update updates a blog category
func (s *blogCategoryService) Update(ctx context.Context, tenantID, id uint, req *models.BlogCategoryUpdateRequest) (*models.BlogCategoryResponse, error) {
	// Get existing category
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("category not found")
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	var parentChanged bool
	var newParentIdPath string
	
	// Validate parent category if specified and changed
	if req.ParentID != nil && (existing.ParentID == nil || *req.ParentID != *existing.ParentID) {
		parentChanged = true
		if *req.ParentID == existing.ID {
			return nil, fmt.Errorf("category cannot be its own parent")
		}

		parent, err := s.repo.GetByID(ctx, tenantID, *req.ParentID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("parent category not found")
			}
			return nil, fmt.Errorf("failed to validate parent category: %w", err)
		}
		if parent.WebsiteID != existing.WebsiteID {
			return nil, fmt.Errorf("parent category must belong to the same website")
		}

		// Check for circular reference
		if err := s.checkCircularReference(ctx, tenantID, existing.ID, *req.ParentID); err != nil {
			return nil, err
		}
		
		newParentIdPath = parent.IdPath
	} else if req.ParentID == nil && existing.ParentID != nil {
		// Moving to root
		parentChanged = true
		newParentIdPath = ""
	}

	// Check for duplicate slug if slug is being changed
	if req.Slug != existing.Slug {
		existingBySlug, err := s.repo.GetBySlug(ctx, tenantID, existing.WebsiteID, req.Slug)
		if err == nil && existingBySlug != nil && existingBySlug.ID != existing.ID {
			return nil, fmt.Errorf("category with slug '%s' already exists", req.Slug)
		}
	}

	// Update the category
	existing.Slug = req.Slug
	existing.Name = req.Name
	existing.Description = req.Description
	existing.ParentID = req.ParentID
	existing.Color = req.Color
	existing.IsActive = req.IsActive
	existing.SortOrder = req.SortOrder

	// Set default color if empty
	if existing.Color == "" {
		existing.Color = "#000000"
	}

	// Update id_path if parent changed
	if parentChanged {
		if newParentIdPath != "" {
			existing.IdPath = fmt.Sprintf("%s,%d", newParentIdPath, existing.ID)
		} else {
			existing.IdPath = fmt.Sprintf("%d", existing.ID)
		}
		
		// Need to update all descendants' id_path
		if err := s.rebuildDescendantsIdPath(ctx, tenantID, existing.ID, existing.IdPath); err != nil {
			return nil, fmt.Errorf("failed to rebuild descendants id_path: %w", err)
		}
	}
	
	err = s.repo.Update(ctx, tenantID, id, existing)
	if err != nil {
		return nil, fmt.Errorf("failed to update category: %w", err)
	}

	response := &models.BlogCategoryResponse{}
	response.FromBlogCategory(existing)
	return response, nil
}

// Delete soft deletes a blog category
func (s *blogCategoryService) Delete(ctx context.Context, tenantID, id uint) error {
	// Check if category exists
	_, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("category not found")
		}
		return fmt.Errorf("failed to get category: %w", err)
	}

	// Check if category has children using GetDescendants
	children, err := s.repo.GetDescendants(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to check for children: %w", err)
	}
	if len(children) > 0 {
		return fmt.Errorf("cannot delete category with children")
	}

	// Soft delete using repository delete method
	err = s.repo.Delete(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to delete category: %w", err)
	}

	return nil
}

// List retrieves a list of blog categories with filtering
func (s *blogCategoryService) List(ctx context.Context, filter *models.BlogCategoryFilter) ([]models.BlogCategoryResponse, int64, error) {
	categories, total, err := s.repo.List(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list categories: %w", err)
	}

	responses := make([]models.BlogCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i].FromBlogCategory(&category)
	}

	return responses, total, nil
}

// ListWithCursor retrieves categories with cursor-based pagination
func (s *blogCategoryService) ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogCategoryResponse, *pagination.CursorResponse, error) {
	// Call repository method that should return categories and cursor response
	categories, cursorResponse, err := s.repo.ListWithCursor(ctx, tenantID, req, filters)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list categories with cursor: %w", err)
	}

	// Convert to response DTOs
	responses := make([]models.BlogCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i].FromBlogCategory(&category)
	}

	return responses, cursorResponse, nil
}

// GetHierarchy retrieves the hierarchical structure of categories
func (s *blogCategoryService) GetHierarchy(ctx context.Context, tenantID, websiteID uint) ([]models.BlogCategoryResponse, error) {
	categories, err := s.repo.GetHierarchy(ctx, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category hierarchy: %w", err)
	}

	responses := make([]models.BlogCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i].FromBlogCategory(&category)
	}

	return responses, nil
}

// MoveCategory moves a category to a new parent
func (s *blogCategoryService) MoveCategory(ctx context.Context, tenantID, categoryID, newParentID uint) error {
	// Get the category to move
	category, err := s.repo.GetByID(ctx, tenantID, categoryID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("category not found")
		}
		return fmt.Errorf("failed to get category: %w", err)
	}

	var newParentIdPath string
	
	// Validate new parent
	if newParentID != 0 {
		parent, err := s.repo.GetByID(ctx, tenantID, newParentID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("parent category not found")
			}
			return fmt.Errorf("failed to get parent category: %w", err)
		}
		if parent.WebsiteID != category.WebsiteID {
			return fmt.Errorf("parent category must belong to the same website")
		}

		// Check for circular reference
		if err := s.checkCircularReference(ctx, tenantID, categoryID, newParentID); err != nil {
			return err
		}
		
		newParentIdPath = parent.IdPath
	}

	// Update parent
	if newParentID == 0 {
		category.ParentID = nil
		category.IdPath = fmt.Sprintf("%d", category.ID)
	} else {
		category.ParentID = &newParentID
		category.IdPath = fmt.Sprintf("%s,%d", newParentIdPath, category.ID)
	}

	err = s.repo.Update(ctx, tenantID, categoryID, category)
	if err != nil {
		return fmt.Errorf("failed to move category: %w", err)
	}
	
	// Rebuild id_path for all descendants
	if err := s.rebuildDescendantsIdPath(ctx, tenantID, categoryID, category.IdPath); err != nil {
		return fmt.Errorf("failed to rebuild descendants id_path: %w", err)
	}

	return nil
}

// UpdatePositions updates the sort order of multiple categories
func (s *blogCategoryService) UpdatePositions(ctx context.Context, tenantID uint, positions []CategoryPosition) error {
	for _, pos := range positions {
		category, err := s.repo.GetByID(ctx, tenantID, pos.ID)
		if err != nil {
			return fmt.Errorf("failed to get category %d: %w", pos.ID, err)
		}

		category.ParentID = pos.ParentID
		category.SortOrder = pos.Position

		err = s.repo.Update(ctx, tenantID, pos.ID, category)
		if err != nil {
			return fmt.Errorf("failed to update category %d position: %w", pos.ID, err)
		}
	}

	return nil
}

// checkCircularReference checks if creating the parent-child relationship would create a circular reference
func (s *blogCategoryService) checkCircularReference(ctx context.Context, tenantID, categoryID, newParentID uint) error {
	current := newParentID
	for current != 0 {
		if current == categoryID {
			return fmt.Errorf("circular reference detected: category cannot be ancestor of itself")
		}

		parent, err := s.repo.GetByID(ctx, tenantID, current)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break
			}
			return fmt.Errorf("failed to check circular reference: %w", err)
		}

		if parent.ParentID == nil {
			break
		}
		current = *parent.ParentID
	}

	return nil
}

// rebuildDescendantsIdPath recursively updates id_path for all descendants of a category
func (s *blogCategoryService) rebuildDescendantsIdPath(ctx context.Context, tenantID, categoryID uint, parentIdPath string) error {
	// Get all direct children
	children, err := s.repo.GetDescendants(ctx, tenantID, categoryID)
	if err != nil {
		return fmt.Errorf("failed to get descendants: %w", err)
	}
	
	// Update each child's id_path
	for _, child := range children {
		// Only process direct children (level = parent.level + 1)
		parent, err := s.repo.GetByID(ctx, tenantID, categoryID)
		if err != nil {
			return fmt.Errorf("failed to get parent category: %w", err)
		}
		
		// Check if this is a direct child
		if child.Level == parent.Level + 1 && child.ParentID != nil && *child.ParentID == categoryID {
			// Build new id_path for child
			child.IdPath = fmt.Sprintf("%s,%d", parentIdPath, child.ID)
			
			// Update child
			err = s.repo.Update(ctx, tenantID, child.ID, &child)
			if err != nil {
				return fmt.Errorf("failed to update child category %d: %w", child.ID, err)
			}
			
			// Recursively update this child's descendants
			if err := s.rebuildDescendantsIdPath(ctx, tenantID, child.ID, child.IdPath); err != nil {
				return fmt.Errorf("failed to rebuild descendants for category %d: %w", child.ID, err)
			}
		}
	}
	
	return nil
}

// GetAncestors retrieves all ancestor categories using id_path
func (s *blogCategoryService) GetAncestors(ctx context.Context, tenantID, categoryID uint) ([]models.BlogCategoryResponse, error) {
	// Get the category
	category, err := s.repo.GetByID(ctx, tenantID, categoryID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("category not found")
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}
	
	// If no id_path or it's root, return empty array
	if category.IdPath == "" || category.IdPath == fmt.Sprintf("%d", categoryID) {
		return []models.BlogCategoryResponse{}, nil
	}
	
	// Parse id_path to get ancestor IDs
	ancestorIDs := []uint{}
	parts := strings.Split(category.IdPath, ",")
	for _, part := range parts {
		if part != "" && part != fmt.Sprintf("%d", categoryID) {
			id, err := strconv.ParseUint(part, 10, 32)
			if err == nil {
				ancestorIDs = append(ancestorIDs, uint(id))
			}
		}
	}
	
	// Get all ancestors in batch
	ancestors := []models.BlogCategoryResponse{}
	for _, ancestorID := range ancestorIDs {
		ancestor, err := s.repo.GetByID(ctx, tenantID, ancestorID)
		if err == nil {
			var response models.BlogCategoryResponse
			response.FromBlogCategory(ancestor)
			ancestors = append(ancestors, response)
		}
	}
	
	return ancestors, nil
}

// GetBreadcrumb returns the breadcrumb path for a category
func (s *blogCategoryService) GetBreadcrumb(ctx context.Context, tenantID, categoryID uint) (*models.BlogCategoryBreadcrumb, error) {
	// Get ancestors
	ancestors, err := s.GetAncestors(ctx, tenantID, categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get ancestors: %w", err)
	}
	
	// Get current category
	current, err := s.repo.GetByID(ctx, tenantID, categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}
	
	// Build breadcrumb items
	items := make([]models.BlogCategoryBreadcrumbItem, 0, len(ancestors)+1)
	
	// Add ancestors
	for _, ancestor := range ancestors {
		items = append(items, models.BlogCategoryBreadcrumbItem{
			ID:   ancestor.ID,
			Name: ancestor.Name,
			Slug: ancestor.Slug,
		})
	}
	
	// Add current category
	items = append(items, models.BlogCategoryBreadcrumbItem{
		ID:   current.ID,
		Name: current.Name,
		Slug: current.Slug,
	})
	
	return &models.BlogCategoryBreadcrumb{
		Items: items,
		Count: len(items),
	}, nil
}

// validateIdPathConsistency validates that id_path is consistent with parent-child relationships
func (s *blogCategoryService) validateIdPathConsistency(ctx context.Context, tenantID, categoryID uint) error {
	category, err := s.repo.GetByID(ctx, tenantID, categoryID)
	if err != nil {
		return fmt.Errorf("failed to get category: %w", err)
	}
	
	// Validate id_path format
	if category.IdPath == "" {
		return fmt.Errorf("category %d has empty id_path", categoryID)
	}
	
	// Parse id_path
	parts := strings.Split(category.IdPath, ",")
	if len(parts) == 0 {
		return fmt.Errorf("category %d has invalid id_path format", categoryID)
	}
	
	// Last part should be the category's own ID
	lastPart := parts[len(parts)-1]
	lastID, err := strconv.ParseUint(lastPart, 10, 32)
	if err != nil || uint(lastID) != categoryID {
		return fmt.Errorf("category %d id_path does not end with its own ID", categoryID)
	}
	
	// If it's root category
	if category.ParentID == nil {
		if len(parts) != 1 {
			return fmt.Errorf("root category %d should have id_path with single ID", categoryID)
		}
		return nil
	}
	
	// For non-root categories, validate parent relationship
	parent, err := s.repo.GetByID(ctx, tenantID, *category.ParentID)
	if err != nil {
		return fmt.Errorf("failed to get parent category: %w", err)
	}
	
	// Parent's id_path should be a prefix of this category's id_path
	expectedIdPath := fmt.Sprintf("%s,%d", parent.IdPath, categoryID)
	if category.IdPath != expectedIdPath {
		return fmt.Errorf("category %d id_path mismatch: expected '%s', got '%s'", categoryID, expectedIdPath, category.IdPath)
	}
	
	// Validate all ancestors exist
	for i, part := range parts[:len(parts)-1] {
		ancestorID, err := strconv.ParseUint(part, 10, 32)
		if err != nil {
			return fmt.Errorf("invalid ancestor ID in id_path at position %d", i)
		}
		
		_, err = s.repo.GetByID(ctx, tenantID, uint(ancestorID))
		if err != nil {
			return fmt.Errorf("ancestor %d in id_path does not exist", ancestorID)
		}
	}
	
	return nil
}

// validateAndFixIdPath validates and fixes id_path for a category and its descendants
func (s *blogCategoryService) validateAndFixIdPath(ctx context.Context, tenantID, categoryID uint) error {
	category, err := s.repo.GetByID(ctx, tenantID, categoryID)
	if err != nil {
		return fmt.Errorf("failed to get category: %w", err)
	}
	
	// Build correct id_path
	var correctIdPath string
	if category.ParentID == nil {
		correctIdPath = fmt.Sprintf("%d", categoryID)
	} else {
		parent, err := s.repo.GetByID(ctx, tenantID, *category.ParentID)
		if err != nil {
			return fmt.Errorf("failed to get parent: %w", err)
		}
		
		// Recursively fix parent first
		if err := s.validateAndFixIdPath(ctx, tenantID, *category.ParentID); err != nil {
			return fmt.Errorf("failed to fix parent id_path: %w", err)
		}
		
		// Get updated parent
		parent, _ = s.repo.GetByID(ctx, tenantID, *category.ParentID)
		correctIdPath = fmt.Sprintf("%s,%d", parent.IdPath, categoryID)
	}
	
	// Update if different
	if category.IdPath != correctIdPath {
		category.IdPath = correctIdPath
		if err := s.repo.Update(ctx, tenantID, categoryID, category); err != nil {
			return fmt.Errorf("failed to update category id_path: %w", err)
		}
		
		// Update all descendants
		if err := s.rebuildDescendantsIdPath(ctx, tenantID, categoryID, correctIdPath); err != nil {
			return fmt.Errorf("failed to rebuild descendants: %w", err)
		}
	}
	
	return nil
}

// GetSelectOptions returns categories formatted for select/dropdown UI
func (s *blogCategoryService) GetSelectOptions(ctx context.Context, tenantID, websiteID uint, parentID *uint, showTree, includeDisabled bool) ([]interface{}, error) {
	// Build filter
	filter := &models.BlogCategoryFilter{
		TenantID:  tenantID,
		WebsiteID: websiteID,
		ParentID:  parentID,
	}
	
	// Only include active categories unless specifically requested
	if !includeDisabled {
		isActive := true
		filter.IsActive = &isActive
	}

	// Get categories based on request type
	var categories []models.BlogCategory
	var err error
	
	if showTree {
		// Get full hierarchy for tree view
		categories, err = s.repo.GetHierarchy(ctx, tenantID, websiteID)
	} else if parentID != nil {
		// Get children of specific parent
		categories, err = s.repo.GetByParentID(ctx, tenantID, websiteID, parentID)
	} else {
		// Get all categories
		categories, _, err = s.repo.List(ctx, filter)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	// Convert to select options
	options := make([]interface{}, 0, len(categories))
	
	for _, category := range categories {
		if !includeDisabled && !category.IsActive {
			continue
		}
		
		option := dto.CategorySelectOption{
			Value:    category.ID,
			Label:    category.Name,
			Disabled: !category.IsActive,
			ParentID: category.ParentID,
			Level:    category.Level,
			Color:    category.Color,
		}
		
		// Add path for tree view
		if showTree && category.Level > 0 {
			// Build indented label based on level
			indent := ""
			for i := uint(0); i < category.Level; i++ {
				indent += "　　" // Using full-width space for better visual
			}
			option.Label = indent + category.Name
			
			// Get full path if id_path is available
			if category.IdPath != "" {
				ancestors, _ := s.GetAncestors(ctx, tenantID, category.ID)
				if len(ancestors) > 0 {
					path := ""
					for i, ancestor := range ancestors {
						if i > 0 {
							path += " > "
						}
						path += ancestor.Name
					}
					path += " > " + category.Name
					option.Path = path
				}
			}
		}
		
		options = append(options, option)
	}
		
	return options, nil
}
