package services

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogCategoryService defines the interface for blog category business logic
type BlogCategoryService interface {
	// Basic CRUD operations
	Create(ctx context.Context, req *models.BlogCategoryCreateRequest) (*models.BlogCategoryResponse, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogCategoryResponse, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogCategoryResponse, error)
	Update(ctx context.Context, tenantID, id uint, req *models.BlogCategoryUpdateRequest) (*models.BlogCategoryResponse, error)
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogCategoryFilter) ([]models.BlogCategoryResponse, int64, error)
	ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogCategoryResponse, *pagination.CursorResponse, error)
	GetHierarchy(ctx context.Context, tenantID, websiteID uint) ([]models.BlogCategoryResponse, error)

	// Hierarchical operations
	MoveCategory(ctx context.Context, tenantID, categoryID, newParentID uint) error
	UpdatePositions(ctx context.Context, tenantID uint, positions []CategoryPosition) error
	GetAncestors(ctx context.Context, tenantID, categoryID uint) ([]models.BlogCategoryResponse, error)
	GetBreadcrumb(ctx context.Context, tenantID, categoryID uint) (*models.BlogCategoryBreadcrumb, error)

	// UI operations
	GetSelectOptions(ctx context.Context, tenantID, websiteID uint, parentID *uint, showTree, includeDisabled bool) ([]interface{}, error)
}

// BlogTagService defines the interface for blog tag business logic
type BlogTagService interface {
	// Basic CRUD operations
	Create(ctx context.Context, req *models.BlogTagCreateRequest) (*models.BlogTagResponse, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogTagResponse, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogTagResponse, error)
	Update(ctx context.Context, tenantID, id uint, req *models.BlogTagUpdateRequest) (*models.BlogTagResponse, error)
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogTagFilter) ([]models.BlogTagResponse, int64, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, cursor *pagination.CursorRequest, filters map[string]interface{}) ([]dto.BlogTagResponse, *pagination.CursorResponse, error)
	GetMostUsed(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogTagResponse, error)
	GetSuggestions(ctx context.Context, tenantID, websiteID uint, query string, limit int) ([]models.BlogTagResponse, error)

	// Utility operations
	CreateOrFindTags(ctx context.Context, tenantID, websiteID uint, tagNames []string) ([]models.BlogTag, error)
	GetStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogTagStats, error)

	// UI operations
	GetSelectOptions(ctx context.Context, tenantID, websiteID uint, includeCount bool) ([]interface{}, error)
}

// BlogPostService defines the interface for blog post business logic
type BlogPostService interface {
	// Basic CRUD operations
	Create(ctx context.Context, req *models.BlogPostCreateRequest) (*models.BlogPostResponse, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostResponse, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogPostResponse, error)
	Update(ctx context.Context, tenantID, id uint, req *models.BlogPostUpdateRequest) (*models.BlogPostResponse, error)
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogPostFilter) ([]models.BlogPostResponse, int64, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.BlogPostResponse, *pagination.CursorResponse, error)
	GetPublished(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]models.BlogPostResponse, int64, error)
	GetFeatured(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogPostResponse, error)
	GetRelated(ctx context.Context, tenantID, postID uint, limit int) ([]models.BlogPostResponse, error)

	// Publishing operations
	Publish(ctx context.Context, tenantID, postID uint) (*models.BlogPostResponse, error)
	Unpublish(ctx context.Context, tenantID, postID uint) (*models.BlogPostResponse, error)
	Schedule(ctx context.Context, tenantID, postID uint, req *models.BlogPostScheduleCreateRequest) (*models.BlogPostResponse, error)

	// Tag operations
	AttachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error
	DetachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error
	SyncTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error
	GetPostTags(ctx context.Context, tenantID, postID uint) ([]models.BlogTag, error)

	// Related posts operations
	GetRelatedPostIDs(ctx context.Context, tenantID, postID uint) ([]uint, error)
	SyncRelatedPosts(ctx context.Context, tenantID, postID uint, relatedPostIDs []uint) error
	GetRelatedPostsByIDs(ctx context.Context, tenantID uint, postIDs []uint) ([]models.BlogPost, error)

	// Analytics and statistics
	IncrementViewCount(ctx context.Context, tenantID, postID uint) error
	GetStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogPostStats, error)
	GetPopular(ctx context.Context, tenantID, websiteID uint, days int, limit int) ([]models.BlogPostResponse, error)

	// Revision management
	CreateRevision(ctx context.Context, tenantID, postID, userID uint, changesSummary string) (*models.BlogPostRevisionResponse, error)

	// Workflow management
	AssignToReviewer(ctx context.Context, tenantID, postID, assignedTo uint, notes string) error
	SubmitForReview(ctx context.Context, tenantID, postID, userID uint) error
	ApprovePost(ctx context.Context, tenantID, postID, userID uint, notes string) error
	RejectPost(ctx context.Context, tenantID, postID, userID uint, reason string) error
	ReturnToAuthor(ctx context.Context, tenantID, postID, userID uint, feedback string) error
	GetWorkflowHistory(ctx context.Context, tenantID, postID uint) ([]models.BlogPostWorkflowHistory, error)
	GetPostsByWorkflowState(ctx context.Context, tenantID, websiteID uint, state models.WorkflowState, assignedTo *uint) ([]models.BlogPostResponse, error)
}

// BlogPostScheduleService defines the interface for blog post scheduling business logic
type BlogPostScheduleService interface {
	Create(ctx context.Context, req *models.BlogPostScheduleCreateRequest) (*models.BlogPostScheduleResponse, error)
	GetByPostID(ctx context.Context, tenantID, postID uint) (*models.BlogPostScheduleResponse, error)
	Update(ctx context.Context, tenantID, id uint, req *models.BlogPostScheduleUpdateRequest) (*models.BlogPostScheduleResponse, error)
	Delete(ctx context.Context, tenantID, id uint) error
	List(ctx context.Context, filter *models.BlogPostScheduleFilter) ([]models.BlogPostScheduleResponse, int64, error)

	// Schedule processing
	ProcessPendingSchedules(ctx context.Context) error
	MarkAsExecuted(ctx context.Context, tenantID, scheduleID uint) error
	MarkAsFailed(ctx context.Context, tenantID, scheduleID uint, errorMessage string) error
}

// BlogPostRevisionService defines the interface for blog post revision business logic
type BlogPostRevisionService interface {
	Create(ctx context.Context, req *models.BlogPostRevisionCreateRequest) (*models.BlogPostRevisionResponse, error)
	GetByPostID(ctx context.Context, tenantID, postID uint, limit, offset int) ([]models.BlogPostRevisionResponse, int64, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostRevisionResponse, error)
	Delete(ctx context.Context, tenantID, id uint) error
	List(ctx context.Context, filter *models.BlogPostRevisionFilter) ([]models.BlogPostRevisionResponse, int64, error)

	// Revision management
	GetLatest(ctx context.Context, tenantID, postID uint) (*models.BlogPostRevisionResponse, error)
	CompareRevisions(ctx context.Context, tenantID, fromRevisionID, toRevisionID uint) (*models.BlogPostRevisionComparison, error)
	DeleteOldRevisions(ctx context.Context, tenantID, postID uint, keepCount int) error
	GetStats(ctx context.Context, tenantID uint) (*models.BlogPostRevisionStats, error)
}

// HomepageBlockService defines the interface for homepage block business logic
type HomepageBlockService interface {
	// Basic CRUD operations
	Create(ctx context.Context, req *models.HomepageBlockCreateRequest) (*models.HomepageBlockResponse, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.HomepageBlockResponse, error)
	Update(ctx context.Context, tenantID, id uint, req *models.HomepageBlockUpdateRequest) (*models.HomepageBlockResponse, error)
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.HomepageBlockFilter) ([]models.HomepageBlockResponse, int64, error)
	GetByWebsite(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlockResponse, error)
	GetActiveBlocks(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlockResponse, error)

	// Block organization
	Reorder(ctx context.Context, tenantID uint, blockIDs []uint) error
	UpdateSortOrder(ctx context.Context, tenantID, blockID uint, sortOrder uint) error

	// Block status management
	Activate(ctx context.Context, tenantID, blockID uint) error
	Deactivate(ctx context.Context, tenantID, blockID uint) error

	// Template operations
	CreateFromTemplate(ctx context.Context, tenantID, templateID, websiteID uint, customizations models.JSON) (*models.HomepageBlockResponse, error)
	SaveAsTemplate(ctx context.Context, tenantID, blockID uint, templateName, templateDescription string, isPublic bool) (*models.BlockTemplateResponse, error)
}

// BlockTemplateService defines the interface for block template business logic
type BlockTemplateService interface {
	// Basic CRUD operations
	Create(ctx context.Context, req *models.BlockTemplateCreateRequest) (*models.BlockTemplateResponse, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlockTemplateResponse, error)
	Update(ctx context.Context, tenantID, id uint, req *models.BlockTemplateUpdateRequest) (*models.BlockTemplateResponse, error)
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlockTemplateFilter) ([]models.BlockTemplateResponse, int64, error)
	GetPublicTemplates(ctx context.Context, blockType models.HomepageBlockType) ([]models.BlockTemplateResponse, error)
	GetByTenant(ctx context.Context, tenantID uint) ([]models.BlockTemplateResponse, error)

	// Template management
	Clone(ctx context.Context, tenantID, templateID uint, newName string) (*models.BlockTemplateResponse, error)
	UpdateConfiguration(ctx context.Context, tenantID, templateID uint, configuration models.JSON) (*models.BlockTemplateResponse, error)
}

// BlogPostTemplateService defines the interface for blog post template business logic
type BlogPostTemplateService interface {
	// Basic CRUD operations
	Create(ctx context.Context, req *models.BlogPostTemplateCreateRequest) (*models.BlogPostTemplateResponse, error)
	GetByID(ctx context.Context, id uint) (*models.BlogPostTemplateResponse, error)
	GetBySlug(ctx context.Context, slug string, scope models.BlogPostTemplateScope, scopeID *uint) (*models.BlogPostTemplateResponse, error)
	Update(ctx context.Context, id uint, req *models.BlogPostTemplateUpdateRequest) (*models.BlogPostTemplateResponse, error)
	Delete(ctx context.Context, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogPostTemplateFilter) ([]models.BlogPostTemplateResponse, int64, error)
	ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPostTemplateResponse, *pagination.CursorResponse, error)
	ListAccessible(ctx context.Context, tenantID, websiteID, userID uint) ([]models.BlogPostTemplateResponse, error)
	ListAccessibleWithCursor(ctx context.Context, tenantID, websiteID, userID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPostTemplateResponse, *pagination.CursorResponse, error)
	GetByType(ctx context.Context, templateType models.BlogPostTemplateType, tenantID *uint) ([]models.BlogPostTemplateResponse, error)
	GetFeatured(ctx context.Context, tenantID *uint, limit int) ([]models.BlogPostTemplateResponse, error)
	GetPopular(ctx context.Context, tenantID *uint, limit int) ([]models.BlogPostTemplateResponse, error)

	// Template operations
	Duplicate(ctx context.Context, id uint, newName, newSlug string, targetScope models.BlogPostTemplateScope, targetScopeID *uint, userID uint) (*models.BlogPostTemplateResponse, error)
	CreatePostFromTemplate(ctx context.Context, tenantID, websiteID, userID uint, req *models.BlogPostFromTemplateRequest) (*models.BlogPostResponse, error)
	ValidateTemplateFields(ctx context.Context, templateID uint, fieldValues map[string]interface{}) error
}

// BlogNewsletterSubscriptionService defines the interface for newsletter subscription business logic
type BlogNewsletterSubscriptionService interface {
	// Subscription management
	Subscribe(ctx context.Context, tenantID, websiteID uint, req *dto.CreateNewsletterSubscriptionRequest) (*dto.NewsletterSubscriptionResponse, error)
	ConfirmSubscription(ctx context.Context, token string) (*dto.NewsletterSubscriptionResponse, error)
	Unsubscribe(ctx context.Context, token string) error

	// CRUD operations
	GetByID(ctx context.Context, tenantID, id uint) (*dto.NewsletterSubscriptionResponse, error)
	GetByEmail(ctx context.Context, tenantID, websiteID uint, email string) (*dto.NewsletterSubscriptionResponse, error)
	Update(ctx context.Context, tenantID, id uint, req *dto.UpdateNewsletterSubscriptionRequest) (*dto.NewsletterSubscriptionResponse, error)
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogNewsletterSubscriptionFilter) ([]dto.NewsletterSubscriptionListResponse, int64, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.NewsletterSubscriptionListResponse, *pagination.CursorResponse, error)

	// Subscriber queries
	GetActiveSubscribers(ctx context.Context, tenantID, websiteID uint, frequency models.NewsletterFrequency) ([]dto.NewsletterSubscriptionResponse, error)
	GetSubscribersByCategories(ctx context.Context, tenantID, websiteID uint, categoryIDs []uint) ([]dto.NewsletterSubscriptionResponse, error)
	GetSubscribersByTags(ctx context.Context, tenantID, websiteID uint, tagIDs []uint) ([]dto.NewsletterSubscriptionResponse, error)

	// Newsletter sending
	MarkAsSent(ctx context.Context, tenantID uint, subscriptionIDs []uint) error

	// Statistics
	GetStatsByWebsite(ctx context.Context, tenantID, websiteID uint) (*models.NewsletterSubscriptionStats, error)
	GetGrowthData(ctx context.Context, tenantID, websiteID uint, days int) ([]models.NewsletterGrowthData, error)

	// Admin operations
	ImportSubscribers(ctx context.Context, tenantID, websiteID uint, subscribers []dto.CreateNewsletterSubscriptionRequest) (int, error)
	ExportSubscribers(ctx context.Context, tenantID, websiteID uint, status models.NewsletterSubscriptionStatus) ([]dto.NewsletterSubscriptionResponse, error)
	CleanupInactive(ctx context.Context, days int) error
}

// WorkflowService defines the interface for workflow business logic
type WorkflowService interface {
	// Workflow transitions
	GetAllowedTransitions(currentState models.WorkflowState, userRole string) []models.WorkflowState
	TransitionWorkflow(tenantID uint, postID uint, userID uint, userRole string, action string, request *models.WorkflowTransitionRequest) error

	// Workflow checks
	CanEditPost(post *models.BlogPostResponse, userID uint, userRole string) bool
	CanPublishPost(post *models.BlogPostResponse, userRole string) bool
	GetAvailableActions(post *models.BlogPostResponse, userRole string, userID uint) []string

	// Assignment management
	AssignPost(tenantID uint, postID uint, assignToID uint, notes string, dueAt *time.Time) error
	UpdateWorkflowNotes(tenantID uint, postID uint, notes string) error

	// Workflow data
	GetWorkflowHistory(tenantID uint, postID uint, cursor string, limit int) ([]models.WorkflowLog, string, error)
	GetMyTasks(tenantID uint, userID uint, cursor string, limit int) ([]*models.BlogPost, string, error)
	GetWorkflowQueue(tenantID uint, state models.WorkflowState, cursor string, limit int) ([]*models.BlogPost, string, error)
	GetWorkflowStats(tenantID uint) (*models.WorkflowStats, error)
}

// BlogPostRatingService defines the interface for blog post rating business logic
type BlogPostRatingService interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenantID, userID uint, req *models.BlogPostRatingCreateRequest, ipAddress, userAgent string) (*models.BlogPostRatingResponse, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostRatingResponse, error)
	GetUserRatingForPost(ctx context.Context, tenantID, userID, postID uint) (*models.BlogPostRatingResponse, error)
	Update(ctx context.Context, tenantID, userID, id uint, req *models.BlogPostRatingUpdateRequest) (*models.BlogPostRatingResponse, error)
	Delete(ctx context.Context, tenantID, userID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogPostRatingFilter) ([]models.BlogPostRatingResponse, int64, error)
	ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPostRatingResponse, *pagination.CursorResponse, error)
	ListByPost(ctx context.Context, tenantID, postID uint, limit, offset int) ([]models.BlogPostRatingResponse, int64, error)
	ListByUser(ctx context.Context, tenantID, userID uint, limit, offset int) ([]models.BlogPostRatingResponse, int64, error)

	// Statistics and analytics
	GetRatingStats(ctx context.Context, tenantID, postID uint) (*models.BlogPostRatingStats, error)
	GetAverageRating(ctx context.Context, tenantID, postID uint) (float64, error)
	GetRatingBreakdown(ctx context.Context, tenantID, postID uint) (map[uint8]int, error)
	GetUserRatingsByPosts(ctx context.Context, tenantID, userID uint, postIDs []uint) (map[uint]uint8, error)

	// Bulk operations
	GetRatingsByPosts(ctx context.Context, tenantID uint, postIDs []uint) (map[uint]*models.BlogPostRatingAggregate, error)
	DeleteByPost(ctx context.Context, tenantID, postID uint) error
	DeleteByUser(ctx context.Context, tenantID, userID uint) error

	// Validation
	ValidateRatingPermission(ctx context.Context, tenantID, userID, postID uint) error
	CanUserRate(ctx context.Context, tenantID, userID, postID uint) (bool, error)
}

// BlogPostAuthorService defines the interface for blog post author business logic
type BlogPostAuthorService interface {
	// Basic CRUD operations
	AddAuthor(ctx context.Context, postID, userID uint, role models.BlogPostAuthorRole) (*models.BlogPostAuthorResponse, error)
	GetByPostAndUser(ctx context.Context, postID, userID uint) (*models.BlogPostAuthorResponse, error)
	UpdateAuthorRole(ctx context.Context, postID, userID uint, role models.BlogPostAuthorRole) (*models.BlogPostAuthorResponse, error)
	RemoveAuthor(ctx context.Context, postID, userID uint) error

	// List operations
	ListByPost(ctx context.Context, postID uint, filter *models.BlogPostAuthorFilter) ([]models.BlogPostAuthorResponse, int64, error)
	ListByUser(ctx context.Context, userID uint, limit, offset int) ([]models.BlogPostAuthorResponse, int64, error)
	List(ctx context.Context, filter *models.BlogPostAuthorFilter) ([]models.BlogPostAuthorResponse, int64, error)
	ListForSelect(ctx context.Context, tenantID uint, cursor *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPostAuthorSelectResponse, *pagination.CursorResponse, error)

	// Author management
	GetPrimaryAuthor(ctx context.Context, postID uint) (*models.BlogPostAuthorResponse, error)
	GetAuthorsByRole(ctx context.Context, postID uint, role models.BlogPostAuthorRole) ([]models.BlogPostAuthorResponse, error)

	// Bulk operations
	BulkAddAuthors(ctx context.Context, postID uint, authors []models.BlogPostAuthorCreateRequest) ([]models.BlogPostAuthorResponse, error)
	BulkRemoveAuthors(ctx context.Context, postID uint, userIDs []uint) error
	ReplaceAuthors(ctx context.Context, postID uint, authors []models.BlogPostAuthorCreateRequest) ([]models.BlogPostAuthorResponse, error)

	// Statistics and analytics
	GetAuthorStats(ctx context.Context, postID uint) (*models.BlogPostAuthorStats, error)
	CountAuthorsByPost(ctx context.Context, postID uint) (int64, error)

	// Validation and permissions
	IsAuthor(ctx context.Context, postID, userID uint) (bool, error)
	HasRole(ctx context.Context, postID, userID uint, role models.BlogPostAuthorRole) (bool, error)
	CanUserEditPost(ctx context.Context, postID, userID uint) (bool, error)
	ValidateAuthorPermission(ctx context.Context, postID, userID uint) error

	// Business logic
	EnsurePrimaryAuthor(ctx context.Context, postID uint, fallbackUserID uint) error
	TransferPrimaryAuthorship(ctx context.Context, postID, fromUserID, toUserID uint) error
	SyncWithPostAuthor(ctx context.Context, postID uint) error
}

// BlogPostRoyaltyService defines the interface for blog post royalty business logic
type BlogPostRoyaltyService interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenantID, websiteID uint, req *models.BlogPostRoyaltyCreateRequest) (*models.BlogPostRoyaltyResponse, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostRoyaltyResponse, error)
	GetByPostID(ctx context.Context, tenantID, postID uint) (*models.BlogPostRoyaltyResponse, error)
	Update(ctx context.Context, tenantID, id uint, req *models.BlogPostRoyaltyUpdateRequest) (*models.BlogPostRoyaltyResponse, error)
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogPostRoyaltyFilter) (*models.BlogPostRoyaltyListResponse, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPostRoyaltyResponse, *pagination.CursorResponse, error)

	// Royalty calculations
	CalculateRoyalty(ctx context.Context, tenantID, postID uint, baseAmount float64) (*models.BlogPostRoyaltyCalculateResponse, error)
	UpdateCMSRoyalty(ctx context.Context, tenantID, postID uint, amount float64) (*models.BlogPostRoyaltyResponse, error)
	UpdateEditorRoyalty(ctx context.Context, tenantID, postID uint, amount float64) (*models.BlogPostRoyaltyResponse, error)
	UpdateFinalRoyalty(ctx context.Context, tenantID, postID uint, amount float64) (*models.BlogPostRoyaltyResponse, error)

	// Coefficient management
	UpdateCoefficients(ctx context.Context, tenantID, postID uint, baseCoeff, categoryCoeff, typeCoeff float64) (*models.BlogPostRoyaltyResponse, error)
	BatchUpdateCoefficients(ctx context.Context, tenantID uint, updates []models.BlogPostRoyalty) error

	// Bulk operations
	GetRoyaltiesByPosts(ctx context.Context, tenantID uint, postIDs []uint) (map[uint]*models.BlogPostRoyalty, error)

	// Statistics and analytics
	GetRoyaltyStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogPostRoyaltyStats, error)
	GetTotalRoyaltyByPeriod(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time) (float64, error)

	// Business logic
	InitializeRoyalty(ctx context.Context, tenantID, websiteID, postID uint, wordCount uint) (*models.BlogPostRoyaltyResponse, error)
	RecalculateAllRoyalties(ctx context.Context, tenantID, websiteID uint, baseAmount float64) error
	ProcessRoyaltyWorkflow(ctx context.Context, tenantID, postID uint, step string, userID uint, amount *float64) (*models.BlogPostRoyaltyResponse, error)

	// Cleanup operations
	DeleteByPost(ctx context.Context, tenantID, postID uint) error
}

// CategoryPosition represents a category position for reordering
type CategoryPosition struct {
	ID       uint  `json:"id"`
	ParentID *uint `json:"parent_id"`
	Position uint  `json:"position"`
}
