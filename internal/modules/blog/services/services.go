package services

import (
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// BlogServices contains all blog services
type BlogServices struct {
	CategoryService               BlogCategoryService
	TagService                    BlogTagService
	PostService                   BlogPostService
	PostScheduleService           BlogPostScheduleService
	PostRevisionService           BlogPostRevisionService
	PostAutosaveService           BlogPostAutosaveService
	PostTemplateService           BlogPostTemplateService
	HomepageBlockService          HomepageBlockService
	BlockTemplateService          BlockTemplateService
	NewsletterSubscriptionService BlogNewsletterSubscriptionService
	ReadingProgressService        BlogPostReadingProgressService
	WorkflowService               WorkflowService
	RatingService                 BlogPostRatingService
	AuthorService                 BlogPostAuthorService
	RoyaltyService                BlogPostRoyaltyService
	SearchService                 *BlogSearchService
}

// NewBlogServices creates a new instance of blog services
func NewBlogServices(
	categoryRepo repositories.BlogCategoryRepository,
	tagRepo repositories.BlogTagRepository,
	postRepo repositories.BlogPostRepository,
	scheduleRepo repositories.BlogPostScheduleRepository,
	revisionRepo repositories.BlogPostRevisionRepository,
	autosaveRepo repositories.BlogPostAutosaveRepository,
	templateRepo repositories.BlogPostTemplateRepository,
	homepageBlockRepo repositories.HomepageBlockRepository,
	blockTemplateRepo repositories.BlockTemplateRepository,
	newsletterRepo repositories.BlogNewsletterSubscriptionRepository,
	progressRepo repositories.BlogPostReadingProgressRepository,
	workflowRepo repositories.WorkflowRepository,
	ratingRepo repositories.BlogPostRatingRepository,
	authorRepo repositories.BlogPostAuthorRepository,
	royaltyRepo repositories.BlogPostRoyaltyRepository,
	notificationService notificationServices.NotificationService,
	logger utils.Logger,
	db *gorm.DB,
) *BlogServices {
	// Create tag service first as it's needed by post service
	tagService := NewBlogTagService(tagRepo)

	// Create workflow service
	workflowService := NewWorkflowService(postRepo, workflowRepo, db)

	// Create search service
	searchService, err := NewBlogSearchService(postRepo, logger)
	if err != nil {
		logger.Error("Failed to initialize blog search service", utils.Fields{
			"error": err.Error(),
		})
		// Continue without search service for now
		searchService = nil
	}

	return &BlogServices{
		CategoryService:               NewBlogCategoryService(categoryRepo),
		TagService:                    tagService,
		PostService:                   NewBlogPostService(postRepo, tagRepo, authorRepo, notificationService, logger),
		PostScheduleService:           NewBlogPostScheduleService(scheduleRepo, postRepo),
		PostRevisionService:           NewBlogPostRevisionService(revisionRepo, postRepo),
		PostAutosaveService:           NewBlogPostAutosaveService(autosaveRepo, postRepo, logger),
		PostTemplateService:           NewBlogPostTemplateService(templateRepo, postRepo, tagRepo, logger),
		HomepageBlockService:          NewHomepageBlockService(homepageBlockRepo, blockTemplateRepo, logger),
		BlockTemplateService:          NewBlockTemplateService(blockTemplateRepo, logger),
		NewsletterSubscriptionService: NewBlogNewsletterSubscriptionService(newsletterRepo, notificationService, logger),
		ReadingProgressService:        NewBlogPostReadingProgressService(progressRepo, postRepo),
		WorkflowService:               workflowService,
		RatingService:                 NewBlogPostRatingService(ratingRepo, postRepo, logger),
		AuthorService:                 NewBlogPostAuthorService(authorRepo, postRepo, logger),
		RoyaltyService:                NewBlogPostRoyaltyService(royaltyRepo, postRepo, logger),
		SearchService:                 searchService,
	}
}
