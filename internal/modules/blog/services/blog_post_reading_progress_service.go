package services

import (
	"context"
	"fmt"
	"math"
	"net"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogPostReadingProgressService interface defines the service for reading progress tracking
type BlogPostReadingProgressService interface {
	// Progress tracking
	UpdateReadingProgress(ctx context.Context, tenantID, userID, blogPostID uint, req *dto.UpdateReadingProgressRequest, c *gin.Context) (*dto.ReadingProgressResponse, error)
	GetReadingProgress(ctx context.Context, tenantID, userID, blogPostID uint) (*dto.ReadingProgressResponse, error)
	GetReadingProgressByID(ctx context.Context, tenantID, id uint) (*dto.ReadingProgressResponse, error)

	// Analytics
	GetPostReadingStats(ctx context.Context, tenantID, blogPostID uint) (*dto.PostReadingStatsResponse, error)
	GetUserReadingStats(ctx context.Context, tenantID, userID uint) (*dto.UserReadingStatsResponse, error)
	GetReadingAnalytics(ctx context.Context, tenantID uint, blogPostID *uint, userID *uint, days int) (*dto.ReadingProgressAnalyticsResponse, error)

	// List operations
	GetUserReadingHistory(ctx context.Context, tenantID, userID uint, page, pageSize int) (*dto.ReadingProgressListResponse, error)
	GetPostReaders(ctx context.Context, tenantID, blogPostID uint, page, pageSize int) (*dto.ReadingProgressListResponse, error)
	ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.ReadingProgressResponse, *pagination.CursorResponse, error)
	ListByUserWithCursor(ctx context.Context, tenantID, userID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.ReadingProgressResponse, *pagination.CursorResponse, error)
	ListByPostWithCursor(ctx context.Context, tenantID, blogPostID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.ReadingProgressResponse, *pagination.CursorResponse, error)

	// Cleanup
	CleanupOldProgress(ctx context.Context, tenantID uint, daysToKeep int) error
	DeleteUserProgress(ctx context.Context, tenantID, userID uint) error
	DeletePostProgress(ctx context.Context, tenantID, blogPostID uint) error
}

// blogPostReadingProgressService implements BlogPostReadingProgressService
type blogPostReadingProgressService struct {
	progressRepo repositories.BlogPostReadingProgressRepository
	postRepo     repositories.BlogPostRepository
}

// NewBlogPostReadingProgressService creates a new instance of BlogPostReadingProgressService
func NewBlogPostReadingProgressService(
	progressRepo repositories.BlogPostReadingProgressRepository,
	postRepo repositories.BlogPostRepository,
) BlogPostReadingProgressService {
	return &blogPostReadingProgressService{
		progressRepo: progressRepo,
		postRepo:     postRepo,
	}
}

// UpdateReadingProgress updates or creates reading progress for a blog post
func (s *blogPostReadingProgressService) UpdateReadingProgress(ctx context.Context, tenantID, userID, blogPostID uint, req *dto.UpdateReadingProgressRequest, c *gin.Context) (*dto.ReadingProgressResponse, error) {
	// Verify blog post exists and user has access
	post, err := s.postRepo.GetByID(ctx, tenantID, blogPostID)
	if err != nil {
		return nil, fmt.Errorf("blog post not found: %w", err)
	}

	// Extract device information and IP
	deviceType := s.detectDeviceType(c.GetHeader("User-Agent"))
	ipAddress := s.extractIPAddress(c)
	userAgent := c.GetHeader("User-Agent")

	// Create progress model
	progress := &models.BlogPostReadingProgress{
		TenantID:                  tenantID,
		BlogPostID:                blogPostID,
		UserID:                    userID,
		ReadingProgressPercentage: req.ReadingProgressPercentage,
		TimeSpentSeconds:          req.TimeSpentSeconds,
		LastReadPosition:          req.LastReadPosition,
		ScrollDepthPercentage:     req.ScrollDepthPercentage,
		DeviceType:                deviceType,
		IPAddress:                 ipAddress,
		UserAgent:                 userAgent,
		LastReadAt:                time.Now(),
	}

	// Set completion status if provided
	if req.IsCompleted != nil && *req.IsCompleted {
		progress.IsCompleted = true
		now := time.Now()
		progress.CompletedAt = &now
	}

	// Calculate reading speed if we have enough data
	if post != nil && req.TimeSpentSeconds > 0 {
		readSpeedWPM := s.calculateReadingSpeed(post, req.TimeSpentSeconds, req.ReadingProgressPercentage)
		if readSpeedWPM > 0 {
			progress.ReadSpeedWPM = &readSpeedWPM
		}
	}

	// Save progress
	err = s.progressRepo.CreateOrUpdate(ctx, progress)
	if err != nil {
		return nil, fmt.Errorf("failed to save reading progress: %w", err)
	}

	// Get updated progress to return
	updatedProgress, err := s.progressRepo.GetByUserAndPost(ctx, tenantID, userID, blogPostID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated progress: %w", err)
	}

	return s.convertToProgressResponse(updatedProgress), nil
}

// GetReadingProgress retrieves reading progress for a specific user and post
func (s *blogPostReadingProgressService) GetReadingProgress(ctx context.Context, tenantID, userID, blogPostID uint) (*dto.ReadingProgressResponse, error) {
	progress, err := s.progressRepo.GetByUserAndPost(ctx, tenantID, userID, blogPostID)
	if err != nil {
		return nil, fmt.Errorf("reading progress not found: %w", err)
	}

	return s.convertToProgressResponse(progress), nil
}

// GetReadingProgressByID retrieves reading progress by ID
func (s *blogPostReadingProgressService) GetReadingProgressByID(ctx context.Context, tenantID, id uint) (*dto.ReadingProgressResponse, error) {
	progress, err := s.progressRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, fmt.Errorf("reading progress not found: %w", err)
	}

	return s.convertToProgressResponse(progress), nil
}

// GetPostReadingStats retrieves reading statistics for a blog post
func (s *blogPostReadingProgressService) GetPostReadingStats(ctx context.Context, tenantID, blogPostID uint) (*dto.PostReadingStatsResponse, error) {
	stats, err := s.progressRepo.GetPostReadingStats(ctx, tenantID, blogPostID)
	if err != nil {
		return nil, fmt.Errorf("failed to get post reading stats: %w", err)
	}

	return &dto.PostReadingStatsResponse{
		BlogPostID:          stats.BlogPostID,
		TotalReaders:        stats.TotalReaders,
		CompletedReaders:    stats.CompletedReaders,
		AverageProgress:     stats.AverageProgress,
		AverageTimeSpent:    stats.AverageTimeSpent,
		AverageReadSpeedWPM: stats.AverageReadSpeedWPM,
		AverageScrollDepth:  stats.AverageScrollDepth,
		CompletionRate:      stats.CompletionRate,
		MobileReaders:       stats.MobileReaders,
		DesktopReaders:      stats.DesktopReaders,
		TabletReaders:       stats.TabletReaders,
	}, nil
}

// GetUserReadingStats retrieves reading statistics for a user
func (s *blogPostReadingProgressService) GetUserReadingStats(ctx context.Context, tenantID, userID uint) (*dto.UserReadingStatsResponse, error) {
	stats, err := s.progressRepo.GetUserReadingStats(ctx, tenantID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user reading stats: %w", err)
	}

	return &dto.UserReadingStatsResponse{
		UserID:                 stats.UserID,
		TotalPostsRead:         stats.TotalPostsRead,
		TotalPostsCompleted:    stats.TotalPostsCompleted,
		TotalTimeSpent:         stats.TotalTimeSpent,
		AverageReadingProgress: stats.AverageReadingProgress,
		AverageReadSpeedWPM:    stats.AverageReadSpeedWPM,
		CompletionRate:         stats.CompletionRate,
		PreferredDeviceType:    stats.PreferredDeviceType,
		TotalReadingSessions:   stats.TotalReadingSessions,
	}, nil
}

// GetReadingAnalytics retrieves comprehensive reading analytics
func (s *blogPostReadingProgressService) GetReadingAnalytics(ctx context.Context, tenantID uint, blogPostID *uint, userID *uint, days int) (*dto.ReadingProgressAnalyticsResponse, error) {
	analytics := &dto.ReadingProgressAnalyticsResponse{}

	// Get post stats if blogPostID is provided
	if blogPostID != nil {
		postStats, err := s.GetPostReadingStats(ctx, tenantID, *blogPostID)
		if err == nil {
			analytics.PostStats = postStats
		}
	}

	// Get user stats if userID is provided
	if userID != nil {
		userStats, err := s.GetUserReadingStats(ctx, tenantID, *userID)
		if err == nil {
			analytics.UserStats = userStats
		}
	}

	// Get reading trends
	trends, err := s.progressRepo.GetReadingTrends(ctx, tenantID, blogPostID, days)
	if err == nil {
		analytics.ReadingTrends = make([]dto.ReadingTrendData, len(trends))
		for i, trend := range trends {
			analytics.ReadingTrends[i] = dto.ReadingTrendData{
				Date:             trend.Date,
				TotalReaders:     trend.TotalReaders,
				CompletedReaders: trend.CompletedReaders,
				AverageProgress:  trend.AverageProgress,
				AverageTimeSpent: trend.AverageTimeSpent,
			}
		}
	}

	// Get device distribution
	analytics.DeviceDistribution = s.getDeviceDistribution(ctx, tenantID, blogPostID, days)

	// Get completion rate by time range
	analytics.CompletionRateByTimeRange = s.getCompletionRateByTimeRange(ctx, tenantID, blogPostID, days)

	return analytics, nil
}

// GetUserReadingHistory retrieves reading history for a user
func (s *blogPostReadingProgressService) GetUserReadingHistory(ctx context.Context, tenantID, userID uint, page, pageSize int) (*dto.ReadingProgressListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	progressList, total, err := s.progressRepo.ListByUser(ctx, tenantID, userID, pageSize, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get user reading history: %w", err)
	}

	data := make([]dto.ReadingProgressResponse, len(progressList))
	for i, progress := range progressList {
		data[i] = *s.convertToProgressResponse(&progress)
	}

	return &dto.ReadingProgressListResponse{
		Data:     data,
		Total:    int(total),
		Page:     page,
		PageSize: pageSize,
		HasMore:  int64(page*pageSize) < total,
	}, nil
}

// GetPostReaders retrieves readers for a specific post
func (s *blogPostReadingProgressService) GetPostReaders(ctx context.Context, tenantID, blogPostID uint, page, pageSize int) (*dto.ReadingProgressListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	progressList, total, err := s.progressRepo.ListByPost(ctx, tenantID, blogPostID, pageSize, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get post readers: %w", err)
	}

	data := make([]dto.ReadingProgressResponse, len(progressList))
	for i, progress := range progressList {
		data[i] = *s.convertToProgressResponse(&progress)
	}

	return &dto.ReadingProgressListResponse{
		Data:     data,
		Total:    int(total),
		Page:     page,
		PageSize: pageSize,
		HasMore:  int64(page*pageSize) < total,
	}, nil
}

// CleanupOldProgress removes old reading progress records
func (s *blogPostReadingProgressService) CleanupOldProgress(ctx context.Context, tenantID uint, daysToKeep int) error {
	cutoffDate := time.Now().AddDate(0, 0, -daysToKeep)
	return s.progressRepo.CleanupOldProgress(ctx, tenantID, cutoffDate)
}

// DeleteUserProgress removes all reading progress for a user
func (s *blogPostReadingProgressService) DeleteUserProgress(ctx context.Context, tenantID, userID uint) error {
	return s.progressRepo.DeleteByUser(ctx, tenantID, userID)
}

// DeletePostProgress removes all reading progress for a post
func (s *blogPostReadingProgressService) DeletePostProgress(ctx context.Context, tenantID, blogPostID uint) error {
	return s.progressRepo.DeleteByPost(ctx, tenantID, blogPostID)
}

// ListWithCursor retrieves reading progress with cursor-based pagination
func (s *blogPostReadingProgressService) ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.ReadingProgressResponse, *pagination.CursorResponse, error) {
	progressList, cursorResp, err := s.progressRepo.ListWithCursor(ctx, tenantID, req, filters)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list reading progress with cursor: %w", err)
	}

	data := make([]dto.ReadingProgressResponse, len(progressList))
	for i, progress := range progressList {
		data[i] = *s.convertToProgressResponse(&progress)
	}

	return data, cursorResp, nil
}

// ListByUserWithCursor retrieves reading progress for a specific user with cursor-based pagination
func (s *blogPostReadingProgressService) ListByUserWithCursor(ctx context.Context, tenantID, userID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.ReadingProgressResponse, *pagination.CursorResponse, error) {
	progressList, cursorResp, err := s.progressRepo.ListByUserWithCursor(ctx, tenantID, userID, req, filters)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list user reading progress with cursor: %w", err)
	}

	data := make([]dto.ReadingProgressResponse, len(progressList))
	for i, progress := range progressList {
		data[i] = *s.convertToProgressResponse(&progress)
	}

	return data, cursorResp, nil
}

// ListByPostWithCursor retrieves reading progress for a specific post with cursor-based pagination
func (s *blogPostReadingProgressService) ListByPostWithCursor(ctx context.Context, tenantID, blogPostID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.ReadingProgressResponse, *pagination.CursorResponse, error) {
	progressList, cursorResp, err := s.progressRepo.ListByPostWithCursor(ctx, tenantID, blogPostID, req, filters)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list post reading progress with cursor: %w", err)
	}

	data := make([]dto.ReadingProgressResponse, len(progressList))
	for i, progress := range progressList {
		data[i] = *s.convertToProgressResponse(&progress)
	}

	return data, cursorResp, nil
}

// Helper methods

// convertToProgressResponse converts a model to response DTO
func (s *blogPostReadingProgressService) convertToProgressResponse(progress *models.BlogPostReadingProgress) *dto.ReadingProgressResponse {
	return &dto.ReadingProgressResponse{
		ID:                        progress.ID,
		BlogPostID:                progress.BlogPostID,
		ReadingProgressPercentage: progress.ReadingProgressPercentage,
		TimeSpentSeconds:          progress.TimeSpentSeconds,
		LastReadPosition:          progress.LastReadPosition,
		ScrollDepthPercentage:     progress.ScrollDepthPercentage,
		SessionCount:              progress.SessionCount,
		FirstReadAt:               progress.FirstReadAt,
		LastReadAt:                progress.LastReadAt,
		IsCompleted:               progress.IsCompleted,
		CompletedAt:               progress.CompletedAt,
		ReadSpeedWPM:              progress.ReadSpeedWPM,
		DeviceType:                progress.DeviceType,
		CreatedAt:                 progress.CreatedAt,
		UpdatedAt:                 progress.UpdatedAt,
	}
}

// detectDeviceType detects device type from user agent
func (s *blogPostReadingProgressService) detectDeviceType(userAgent string) models.DeviceType {
	if userAgent == "" {
		return models.DeviceTypeUnknown
	}

	userAgent = strings.ToLower(userAgent)

	// Mobile patterns
	mobilePatterns := []string{
		"mobile", "iphone", "ipod", "android", "blackberry",
		"windows phone", "windows ce", "palm", "symbian",
	}
	for _, pattern := range mobilePatterns {
		if strings.Contains(userAgent, pattern) {
			return models.DeviceTypeMobile
		}
	}

	// Tablet patterns
	tabletPatterns := []string{
		"ipad", "tablet", "kindle", "playbook", "nexus 7", "nexus 10",
	}
	for _, pattern := range tabletPatterns {
		if strings.Contains(userAgent, pattern) {
			return models.DeviceTypeTablet
		}
	}

	// Desktop patterns (default if not mobile or tablet)
	if strings.Contains(userAgent, "mozilla") || strings.Contains(userAgent, "webkit") {
		return models.DeviceTypeDesktop
	}

	return models.DeviceTypeUnknown
}

// extractIPAddress extracts IP address from request
func (s *blogPostReadingProgressService) extractIPAddress(c *gin.Context) string {
	// Check X-Forwarded-For header first
	if xff := c.GetHeader("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if s.isValidIP(ip) {
				return ip
			}
		}
	}

	// Check X-Real-IP header
	if xri := c.GetHeader("X-Real-IP"); xri != "" && s.isValidIP(xri) {
		return xri
	}

	// Fall back to RemoteAddr
	ip, _, err := net.SplitHostPort(c.Request.RemoteAddr)
	if err != nil {
		return c.Request.RemoteAddr
	}

	return ip
}

// isValidIP validates IP address format
func (s *blogPostReadingProgressService) isValidIP(ip string) bool {
	return net.ParseIP(ip) != nil
}

// calculateReadingSpeed calculates reading speed in words per minute
func (s *blogPostReadingProgressService) calculateReadingSpeed(post *models.BlogPost, timeSpentSeconds uint, progressPercentage float64) float64 {
	if post == nil || post.Content == "" || timeSpentSeconds == 0 || progressPercentage == 0 {
		return 0
	}

	// Estimate word count from content
	wordCount := s.estimateWordCount(post.Content)
	if wordCount == 0 {
		return 0
	}

	// Calculate words read based on progress percentage
	wordsRead := float64(wordCount) * (progressPercentage / 100.0)
	if wordsRead == 0 {
		return 0
	}

	// Calculate reading speed (words per minute)
	timeSpentMinutes := float64(timeSpentSeconds) / 60.0
	if timeSpentMinutes == 0 {
		return 0
	}

	readSpeedWPM := wordsRead / timeSpentMinutes

	// Reasonable bounds check (typical reading speed is 200-300 WPM)
	if readSpeedWPM < 50 || readSpeedWPM > 1000 {
		return 0
	}

	return math.Round(readSpeedWPM*100) / 100 // Round to 2 decimal places
}

// estimateWordCount estimates word count from content
func (s *blogPostReadingProgressService) estimateWordCount(content string) int {
	if content == "" {
		return 0
	}

	// Remove HTML tags
	htmlTagPattern := regexp.MustCompile(`<[^>]*>`)
	cleanContent := htmlTagPattern.ReplaceAllString(content, " ")

	// Remove extra whitespace and split by words
	words := regexp.MustCompile(`\S+`).FindAllString(cleanContent, -1)
	return len(words)
}

// getDeviceDistribution gets device distribution for analytics
func (s *blogPostReadingProgressService) getDeviceDistribution(ctx context.Context, tenantID uint, blogPostID *uint, days int) map[string]int {
	// This would typically require a custom query to the repository
	// For now, return a placeholder
	return map[string]int{
		"mobile":  0,
		"desktop": 0,
		"tablet":  0,
		"unknown": 0,
	}
}

// getCompletionRateByTimeRange gets completion rates by time ranges
func (s *blogPostReadingProgressService) getCompletionRateByTimeRange(ctx context.Context, tenantID uint, blogPostID *uint, days int) map[string]float64 {
	// This would typically require a custom query to the repository
	// For now, return a placeholder
	return map[string]float64{
		"last_7_days":  0.0,
		"last_30_days": 0.0,
		"last_90_days": 0.0,
	}
}
