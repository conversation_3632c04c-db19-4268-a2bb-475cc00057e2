package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	notificationModels "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// blogPostService implements BlogPostService interface
type blogPostService struct {
	repo                repositories.BlogPostRepository
	tagRepo             repositories.BlogTagRepository
	authorRepo          repositories.BlogPostAuthorRepository
	notificationService notificationServices.NotificationService
	logger              utils.Logger
}

// NewBlogPostService creates a new blog post service
func NewBlogPostService(
	repo repositories.BlogPostRepository,
	tagRepo repositories.BlogTagRepository,
	authorRepo repositories.BlogPostAuthorRepository,
	notificationService notificationServices.NotificationService,
	logger utils.Logger,
) BlogPostService {
	return &blogPostService{
		repo:                repo,
		tagRepo:             tagRepo,
		authorRepo:          authorRepo,
		notificationService: notificationService,
		logger:              logger,
	}
}

// Create creates a new blog post
func (s *blogPostService) Create(ctx context.Context, req *models.BlogPostCreateRequest) (*models.BlogPostResponse, error) {
	// Check for duplicate slug within the same website
	existing, err := s.repo.GetBySlug(ctx, req.TenantID, req.WebsiteID, req.Slug)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("post with slug '%s' already exists", req.Slug)
	}

	// Create the post model
	post := &models.BlogPost{
		TenantID:      req.TenantID,
		WebsiteID:     req.WebsiteID,
		CategoryID:    req.CategoryID,
		AuthorID:      req.AuthorID,
		Slug:          req.Slug,
		Title:         req.Title,
		Content:       req.Content,
		Excerpt:       req.Excerpt,
		FeaturedImage: req.FeaturedImage,
		Type:          req.Type,
		IsFeatured:    req.IsFeatured,
		AllowComments: req.AllowComments,
		Password:      req.Password,
		ViewCount:     0,
		ScheduledAt:   req.ScheduledAt,
		Status:        models.BlogPostStatus(req.Status),
	}

	// Set default status if not provided
	if post.Status == "" {
		post.Status = "draft"
	}

	// Auto-publish if status is published
	if post.Status == "published" {
		now := time.Now()
		post.PublishedAt = &now
	}

	// Create the post
	err = s.repo.Create(ctx, post)
	if err != nil {
		return nil, fmt.Errorf("failed to create post: %w", err)
	}

	// Handle tags if provided
	if len(req.TagIDs) > 0 {
		err = s.repo.AttachTags(ctx, req.TenantID, post.ID, req.TagIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to attach tags: %w", err)
		}

		// Increment usage count for tags
		for _, tagID := range req.TagIDs {
			s.tagRepo.IncrementUsage(ctx, req.TenantID, tagID)
		}
	}

	// Handle related posts if provided
	if len(req.RelatedPostIDs) > 0 {
		err = s.repo.SyncRelatedPosts(ctx, req.TenantID, post.ID, req.RelatedPostIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to sync related posts: %w", err)
		}
	}

	// Add author to authors table as primary author
	err = s.authorRepo.AddAuthor(ctx, post.ID, post.AuthorID, models.BlogPostAuthorRolePrimary)
	if err != nil {
		s.logger.Error("Failed to add primary author to authors table", "error", err, "post_id", post.ID, "author_id", post.AuthorID)
		// Don't fail the entire operation, just log the error
	} else {
		s.logger.Info("Primary author added to authors table", "post_id", post.ID, "author_id", post.AuthorID)
	}

	// Convert to response
	response := &models.BlogPostResponse{}
	response.FromBlogPost(post)
	return response, nil
}

// GetByID retrieves a blog post by ID
func (s *blogPostService) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(post)
	return response, nil
}

// GetBySlug retrieves a blog post by slug
func (s *blogPostService) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetBySlug(ctx, tenantID, websiteID, slug)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(post)
	return response, nil
}

// Update updates a blog post
func (s *blogPostService) Update(ctx context.Context, tenantID, id uint, req *models.BlogPostUpdateRequest) (*models.BlogPostResponse, error) {
	// Get existing post
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	// Check for duplicate slug if slug is being changed
	if req.Slug != nil && *req.Slug != existing.Slug {
		existingBySlug, err := s.repo.GetBySlug(ctx, tenantID, existing.WebsiteID, *req.Slug)
		if err == nil && existingBySlug != nil && existingBySlug.ID != existing.ID {
			return nil, fmt.Errorf("post with slug '%s' already exists", *req.Slug)
		}
	}

	// Store old status for comparison
	oldStatus := existing.Status

	// Update the post - only update fields that are provided (not nil)
	if req.CategoryID != nil {
		existing.CategoryID = req.CategoryID
	}
	if req.Slug != nil {
		existing.Slug = *req.Slug
	}
	if req.Title != nil {
		existing.Title = *req.Title
	}
	if req.Content != nil {
		existing.Content = *req.Content
	}
	if req.Excerpt != nil {
		existing.Excerpt = *req.Excerpt
	}
	if req.FeaturedImage != nil {
		existing.FeaturedImage = *req.FeaturedImage
	}
	if req.Type != nil {
		existing.Type = *req.Type
	}
	if req.IsFeatured != nil {
		existing.IsFeatured = *req.IsFeatured
	}
	if req.IsSticky != nil {
		existing.IsSticky = *req.IsSticky
	}
	if req.AllowComments != nil {
		existing.AllowComments = *req.AllowComments
	}
	if req.Password != nil {
		existing.Password = *req.Password
	}
	if req.ScheduledAt != nil {
		existing.ScheduledAt = req.ScheduledAt
	}
	if req.Status != nil {
		existing.Status = *req.Status
	}

	// Handle publishing logic
	if req.Status != nil {
		if oldStatus != "published" && *req.Status == "published" {
			// Post is being published for the first time
			now := time.Now()
			existing.PublishedAt = &now
		} else if oldStatus == "published" && *req.Status != "published" {
			// Post is being unpublished
			existing.PublishedAt = nil
		}
	}

	err = s.repo.Update(ctx, tenantID, id, existing)
	if err != nil {
		return nil, fmt.Errorf("failed to update post: %w", err)
	}

	// Handle tags if provided
	if req.TagIDs != nil {
		// Get current tags to update usage counts
		currentTags, err := s.repo.GetPostTags(ctx, tenantID, id)
		if err == nil {
			// Decrement usage count for current tags
			for _, tag := range currentTags {
				s.tagRepo.DecrementUsage(ctx, tenantID, tag.ID)
			}
		}

		// Sync new tags
		err = s.repo.SyncTags(ctx, tenantID, id, *req.TagIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to sync tags: %w", err)
		}

		// Increment usage count for new tags
		for _, tagID := range *req.TagIDs {
			s.tagRepo.IncrementUsage(ctx, tenantID, tagID)
		}
	}

	// Handle related posts if provided
	if req.RelatedPostIDs != nil {
		err = s.repo.SyncRelatedPosts(ctx, tenantID, id, *req.RelatedPostIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to sync related posts: %w", err)
		}
	}

	// Ensure the post has a primary author in the authors table (for legacy posts)
	primaryAuthor, err := s.authorRepo.GetPrimaryAuthor(ctx, existing.ID)
	if err != nil {
		s.logger.Error("Failed to check primary author", "error", err, "post_id", existing.ID)
	} else if primaryAuthor == nil {
		// No primary author exists, add the post's author_id as primary
		err = s.authorRepo.AddAuthor(ctx, existing.ID, existing.AuthorID, models.BlogPostAuthorRolePrimary)
		if err != nil {
			s.logger.Error("Failed to add primary author to authors table", "error", err, "post_id", existing.ID, "author_id", existing.AuthorID)
		} else {
			s.logger.Info("Legacy post synchronized with primary author", "post_id", existing.ID, "author_id", existing.AuthorID)
		}
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(existing)
	return response, nil
}

// Delete soft deletes a blog post
func (s *blogPostService) Delete(ctx context.Context, tenantID, id uint) error {
	// Check if post exists
	_, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	// Get current tags to update usage counts
	currentTags, err := s.repo.GetPostTags(ctx, tenantID, id)
	if err == nil {
		// Decrement usage count for current tags
		for _, tag := range currentTags {
			s.tagRepo.DecrementUsage(ctx, tenantID, tag.ID)
		}
	}

	// Soft delete using repository delete method
	err = s.repo.Delete(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to delete post: %w", err)
	}

	return nil
}

// List retrieves a list of blog posts with filtering
func (s *blogPostService) List(ctx context.Context, filter *models.BlogPostFilter) ([]models.BlogPostResponse, int64, error) {
	posts, total, err := s.repo.List(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, total, nil
}

// ListWithCursor retrieves blog posts using cursor-based pagination
func (s *blogPostService) ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.BlogPostResponse, *pagination.CursorResponse, error) {
	posts, paginationResp, err := s.repo.ListWithCursor(ctx, tenantID, websiteID, req, filters)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list posts with cursor: %w", err)
	}

	// Convert posts to response DTOs
	postResponses := make([]dto.BlogPostResponse, len(posts))
	for i, post := range posts {
		postResponses[i] = s.blogPostToResponseDTO(post)
	}

	return postResponses, paginationResp, nil
}

// GetPublished retrieves published posts
func (s *blogPostService) GetPublished(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]models.BlogPostResponse, int64, error) {
	posts, total, err := s.repo.GetPublished(ctx, tenantID, websiteID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get published posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, total, nil
}

// GetFeatured retrieves featured posts
func (s *blogPostService) GetFeatured(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogPostResponse, error) {
	posts, err := s.repo.GetFeatured(ctx, tenantID, websiteID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get featured posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, nil
}

// GetRelated retrieves related posts
func (s *blogPostService) GetRelated(ctx context.Context, tenantID, postID uint, limit int) ([]models.BlogPostResponse, error) {
	posts, err := s.repo.GetRelated(ctx, tenantID, postID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get related posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, nil
}

// Publish publishes a blog post
func (s *blogPostService) Publish(ctx context.Context, tenantID, postID uint) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	if post.Status == "published" {
		return nil, fmt.Errorf("post is already published")
	}

	err = s.repo.Publish(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to publish post: %w", err)
	}

	// Get updated post
	updatedPost, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated post: %w", err)
	}

	// Ensure the post has a primary author in the authors table (for legacy posts)
	primaryAuthor, err := s.authorRepo.GetPrimaryAuthor(ctx, updatedPost.ID)
	if err != nil {
		s.logger.Error("Failed to check primary author", "error", err, "post_id", updatedPost.ID)
	} else if primaryAuthor == nil {
		// No primary author exists, add the post's author_id as primary
		err = s.authorRepo.AddAuthor(ctx, updatedPost.ID, updatedPost.AuthorID, models.BlogPostAuthorRolePrimary)
		if err != nil {
			s.logger.Error("Failed to add primary author to authors table", "error", err, "post_id", updatedPost.ID, "author_id", updatedPost.AuthorID)
		} else {
			s.logger.Info("Legacy post synchronized with primary author during publish", "post_id", updatedPost.ID, "author_id", updatedPost.AuthorID)
		}
	}

	// Send post published notification
	if s.notificationService != nil {
		err := s.sendPostPublishedNotification(ctx, updatedPost)
		if err != nil {
			// Log error but don't fail publishing - notification is not critical
			s.logger.WithError(err).Error("Failed to send post published notification")
		} else {
			s.logger.WithField("post_id", updatedPost.ID).Info("Post published notification sent successfully")
		}
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(updatedPost)
	return response, nil
}

// Unpublish unpublishes a blog post
func (s *blogPostService) Unpublish(ctx context.Context, tenantID, postID uint) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	if post.Status != "published" {
		return nil, fmt.Errorf("post is not published")
	}

	err = s.repo.Unpublish(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to unpublish post: %w", err)
	}

	// Get updated post
	updatedPost, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated post: %w", err)
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(updatedPost)
	return response, nil
}

// Schedule schedules a blog post for publishing
func (s *blogPostService) Schedule(ctx context.Context, tenantID, postID uint, req *models.BlogPostScheduleCreateRequest) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	if post.Status == "published" {
		return nil, fmt.Errorf("cannot schedule already published post")
	}

	if req.ScheduledAt.Before(time.Now()) {
		return nil, fmt.Errorf("scheduled time must be in the future")
	}

	err = s.repo.Schedule(ctx, tenantID, postID, &req.ScheduledAt)
	if err != nil {
		return nil, fmt.Errorf("failed to schedule post: %w", err)
	}

	// Get updated post
	updatedPost, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated post: %w", err)
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(updatedPost)
	return response, nil
}

// AttachTags attaches tags to a blog post
func (s *blogPostService) AttachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	// Check if post exists
	_, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	err = s.repo.AttachTags(ctx, tenantID, postID, tagIDs)
	if err != nil {
		return fmt.Errorf("failed to attach tags: %w", err)
	}

	// Increment usage count for tags
	for _, tagID := range tagIDs {
		s.tagRepo.IncrementUsage(ctx, tenantID, tagID)
	}

	return nil
}

// DetachTags detaches tags from a blog post
func (s *blogPostService) DetachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	// Check if post exists
	_, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	err = s.repo.DetachTags(ctx, tenantID, postID, tagIDs)
	if err != nil {
		return fmt.Errorf("failed to detach tags: %w", err)
	}

	// Decrement usage count for tags
	for _, tagID := range tagIDs {
		s.tagRepo.DecrementUsage(ctx, tenantID, tagID)
	}

	return nil
}

// SyncTags synchronizes tags for a blog post
func (s *blogPostService) SyncTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	// Check if post exists
	_, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	// Get current tags to update usage counts
	currentTags, err := s.repo.GetPostTags(ctx, tenantID, postID)
	if err == nil {
		// Decrement usage count for current tags
		for _, tag := range currentTags {
			s.tagRepo.DecrementUsage(ctx, tenantID, tag.ID)
		}
	}

	err = s.repo.SyncTags(ctx, tenantID, postID, tagIDs)
	if err != nil {
		return fmt.Errorf("failed to sync tags: %w", err)
	}

	// Increment usage count for new tags
	for _, tagID := range tagIDs {
		s.tagRepo.IncrementUsage(ctx, tenantID, tagID)
	}

	return nil
}

// GetPostTags retrieves tags for a blog post
func (s *blogPostService) GetPostTags(ctx context.Context, tenantID, postID uint) ([]models.BlogTag, error) {
	tags, err := s.repo.GetPostTags(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get post tags: %w", err)
	}
	return tags, nil
}

// IncrementViewCount increments the view count of a blog post
func (s *blogPostService) IncrementViewCount(ctx context.Context, tenantID, postID uint) error {
	err := s.repo.IncrementViewCount(ctx, tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to increment view count: %w", err)
	}
	return nil
}

// GetStats retrieves blog post statistics
func (s *blogPostService) GetStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogPostStats, error) {
	stats, err := s.repo.GetStats(ctx, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog post stats: %w", err)
	}
	return stats, nil
}

// GetPopular retrieves popular posts based on view count
func (s *blogPostService) GetPopular(ctx context.Context, tenantID, websiteID uint, days int, limit int) ([]models.BlogPostResponse, error) {
	posts, err := s.repo.GetPopular(ctx, tenantID, websiteID, days, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, nil
}

// CreateRevision creates a revision for a blog post
func (s *blogPostService) CreateRevision(ctx context.Context, tenantID, postID, userID uint, changesSummary string) (*models.BlogPostRevisionResponse, error) {
	// Check if post exists
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	// This would need a revision repository, but for now we'll return a mock response
	response := &models.BlogPostRevisionResponse{
		ID:             1, // Mock ID
		TenantID:       tenantID,
		PostID:         postID,
		UserID:         userID,
		Title:          post.Title,
		Content:        post.Content,
		Excerpt:        post.Excerpt,
		ChangesSummary: changesSummary,
		CreatedAt:      time.Now(),
	}

	return response, nil
}

// sendPostPublishedNotification sends a notification when a blog post is published
func (s *blogPostService) sendPostPublishedNotification(ctx context.Context, post *models.BlogPost) error {
	// Template data for post published notification
	templateData := map[string]interface{}{
		"post": map[string]interface{}{
			"title":   post.Title,
			"excerpt": post.Excerpt,
			"slug":    post.Slug,
		},
		"author": map[string]interface{}{
			"id": post.AuthorID,
		},
		"website": map[string]interface{}{
			"id": post.WebsiteID,
		},
		"published_at": time.Now().Format("2006-01-02 15:04:05"),
		"current_year": time.Now().Year(),
	}

	// Create notification request
	notificationReq := notificationModels.CreateNotificationRequest{
		Type:         "blog_post_published",
		Channel:      notificationModels.ChannelEmail,
		Subject:      fmt.Sprintf("📝 New Blog Post Published: %s", post.Title),
		TemplateID:   func() *uint { id := uint(3); return &id }(), // Use seeded blog_post_published template ID
		TemplateData: templateData,
		Recipients: []notificationModels.CreateRecipientRequest{
			{
				UserID:           &post.AuthorID,
				RecipientType:    notificationModels.RecipientTypeUser,
				RecipientAddress: "", // Will be resolved from user
			},
		},
		Priority: notificationModels.PriorityNormal,
		Metadata: map[string]interface{}{
			"source":     "blog_post_published",
			"post_id":    post.ID,
			"tenant_id":  post.TenantID,
			"website_id": post.WebsiteID,
			"author_id":  post.AuthorID,
		},
	}

	// Send notification via notification service
	_, err := s.notificationService.CreateNotification(post.TenantID, notificationReq)
	if err != nil {
		return fmt.Errorf("failed to create post published notification: %w", err)
	}

	return nil
}

// blogPostToResponseDTO converts BlogPost model to DTO BlogPostResponse
func (s *blogPostService) blogPostToResponseDTO(post models.BlogPost) dto.BlogPostResponse {
	// Convert category ID (required field)
	categoryID := uint(0)
	if post.CategoryID != nil {
		categoryID = *post.CategoryID
	}

	// Extract tag IDs
	tagIDs := make([]uint, len(post.Tags))
	for i, tag := range post.Tags {
		tagIDs[i] = tag.ID
	}

	// Convert featured image to ID
	var featuredImageID *uint
	// TODO: Map featured image URL to media ID when media service is implemented

	// Get related post IDs from JSON field
	relatedPostIDs := make([]uint, 0)
	if post.RelatedPostIDs != nil {
		// Parse JSON array to []uint
		// This assumes the JSON field contains an array of integers
		// Note: In production, you might want to add proper JSON unmarshaling
	}

	// Convert SEO metadata from JSON to DTO
	var seoMetadata *dto.SEOMetadata
	if post.SEOMetadata != nil {
		// Convert models.SEOMetadata to dto.SEOMetadata
		// This assumes proper JSON unmarshaling from the database
		// Note: In production, you might want to add proper JSON unmarshaling
	}

	// Basic response without related entities
	response := dto.BlogPostResponse{
		ID:              post.ID,
		Title:           post.Title,
		Slug:            post.Slug,
		Content:         post.Content,
		Excerpt:         post.Excerpt,
		Status:          post.Status,
		PublishedAt:     post.PublishedAt,
		CategoryID:      categoryID,
		TagIDs:          tagIDs,
		RelatedPostIDs:  relatedPostIDs, // Use actual related post IDs
		FeaturedImageID: featuredImageID,
		AuthorID:        post.AuthorID,
		Type:            post.Type,
		IsFeatured:      post.IsFeatured,
		AllowComments:   post.AllowComments,
		IsSticky:        post.IsSticky, // Use actual IsSticky from model
		ViewCount:       post.ViewCount,
		CommentCount:    post.CommentCount, // Use actual comment count from model
		WebsiteID:       post.WebsiteID,
		TenantID:        post.TenantID,
		CreatedAt:       post.CreatedAt,
		UpdatedAt:       post.UpdatedAt,
		SEO:             seoMetadata, // Add SEO metadata
	}

	// Add related entities if loaded (for ?include parameter support)
	if post.Category != nil {
		response.Category = &dto.BlogCategoryResponse{
			ID:          post.Category.ID,
			TenantID:    post.Category.TenantID,
			WebsiteID:   post.Category.WebsiteID,
			Name:        post.Category.Name,
			Slug:        post.Category.Slug,
			Description: post.Category.Description,
			CreatedAt:   post.Category.CreatedAt,
			UpdatedAt:   post.Category.UpdatedAt,
		}
	}

	if len(post.Tags) > 0 {
		tags := make([]dto.BlogTagResponse, len(post.Tags))
		for i, tag := range post.Tags {
			tags[i] = dto.BlogTagResponse{
				ID:        tag.ID,
				TenantID:  tag.TenantID,
				WebsiteID: tag.WebsiteID,
				Name:      tag.Name,
				Slug:      tag.Slug,
				CreatedAt: tag.CreatedAt,
				UpdatedAt: tag.UpdatedAt,
			}
		}
		response.Tags = tags
	}

	return response
}

// AssignToReviewer assigns a blog post to a reviewer
func (s *blogPostService) AssignToReviewer(ctx context.Context, tenantID, postID, assignedTo uint, notes string) error {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	// Update workflow assignment
	now := time.Now()
	post.WorkflowAssignedTo = &assignedTo
	post.WorkflowAssignedAt = &now
	post.WorkflowNotes = notes
	post.WorkflowState = models.WorkflowStatePendingReview

	err = s.repo.Update(ctx, tenantID, postID, post)
	if err != nil {
		return fmt.Errorf("failed to assign reviewer: %w", err)
	}

	return nil
}

// SubmitForReview submits a blog post for review
func (s *blogPostService) SubmitForReview(ctx context.Context, tenantID, postID, userID uint) error {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	if post.WorkflowState != models.WorkflowStateCreation {
		return fmt.Errorf("post is not in creation state")
	}

	post.WorkflowState = models.WorkflowStatePendingReview
	err = s.repo.Update(ctx, tenantID, postID, post)
	if err != nil {
		return fmt.Errorf("failed to submit for review: %w", err)
	}

	return nil
}

// ApprovePost approves a blog post
func (s *blogPostService) ApprovePost(ctx context.Context, tenantID, postID, userID uint, notes string) error {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	if post.WorkflowState != models.WorkflowStateInReview && post.WorkflowState != models.WorkflowStatePendingApproval {
		return fmt.Errorf("post is not in reviewable state")
	}

	post.WorkflowState = models.WorkflowStateApproved
	post.WorkflowNotes = notes
	err = s.repo.Update(ctx, tenantID, postID, post)
	if err != nil {
		return fmt.Errorf("failed to approve post: %w", err)
	}

	return nil
}

// RejectPost rejects a blog post
func (s *blogPostService) RejectPost(ctx context.Context, tenantID, postID, userID uint, reason string) error {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	post.WorkflowState = models.WorkflowStateRejected
	post.WorkflowNotes = reason
	err = s.repo.Update(ctx, tenantID, postID, post)
	if err != nil {
		return fmt.Errorf("failed to reject post: %w", err)
	}

	return nil
}

// ReturnToAuthor returns a blog post to the author
func (s *blogPostService) ReturnToAuthor(ctx context.Context, tenantID, postID, userID uint, feedback string) error {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	post.WorkflowState = models.WorkflowStateReturned
	post.WorkflowNotes = feedback
	post.WorkflowAssignedTo = nil // Unassign from current reviewer
	err = s.repo.Update(ctx, tenantID, postID, post)
	if err != nil {
		return fmt.Errorf("failed to return to author: %w", err)
	}

	return nil
}

// GetWorkflowHistory gets workflow history for a blog post
func (s *blogPostService) GetWorkflowHistory(ctx context.Context, tenantID, postID uint) ([]models.BlogPostWorkflowHistory, error) {
	// This would typically be implemented with a separate workflow history repository
	// For now, return empty slice as placeholder
	return []models.BlogPostWorkflowHistory{}, nil
}

// GetPostsByWorkflowState gets posts by workflow state
func (s *blogPostService) GetPostsByWorkflowState(ctx context.Context, tenantID, websiteID uint, state models.WorkflowState, assignedTo *uint) ([]models.BlogPostResponse, error) {
	filter := &models.BlogPostFilter{
		TenantID:  tenantID,
		WebsiteID: websiteID,
	}

	posts, _, err := s.List(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get posts by workflow state: %w", err)
	}

	// Filter by workflow state and assignment (this should be moved to repository level)
	var filteredPosts []models.BlogPostResponse
	for _, post := range posts {
		if post.WorkflowState == state {
			if assignedTo == nil || (post.WorkflowAssignedTo != nil && *post.WorkflowAssignedTo == *assignedTo) {
				filteredPosts = append(filteredPosts, post)
			}
		}
	}

	return filteredPosts, nil
}

// GetRelatedPostIDs gets the manual related post IDs for a specific blog post
func (s *blogPostService) GetRelatedPostIDs(ctx context.Context, tenantID, postID uint) ([]uint, error) {
	return s.repo.GetRelatedPostIDs(ctx, tenantID, postID)
}

// SyncRelatedPosts synchronizes manual related post IDs for a blog post
func (s *blogPostService) SyncRelatedPosts(ctx context.Context, tenantID, postID uint, relatedPostIDs []uint) error {
	return s.repo.SyncRelatedPosts(ctx, tenantID, postID, relatedPostIDs)
}

// GetRelatedPostsByIDs gets blog posts by their IDs (for manual related posts)
func (s *blogPostService) GetRelatedPostsByIDs(ctx context.Context, tenantID uint, postIDs []uint) ([]models.BlogPost, error) {
	return s.repo.GetRelatedPostsByIDs(ctx, tenantID, postIDs)
}
