package services

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// blogPostTemplateService handles business logic for blog post templates
type blogPostTemplateService struct {
	templateRepo repositories.BlogPostTemplateRepository
	postRepo     repositories.BlogPostRepository
	tagRepo      repositories.BlogTagRepository
	logger       utils.Logger
}

// NewBlogPostTemplateService creates a new blog post template service
func NewBlogPostTemplateService(
	templateRepo repositories.BlogPostTemplateRepository,
	postRepo repositories.BlogPostRepository,
	tagRepo repositories.BlogTagRepository,
	logger utils.Logger,
) BlogPostTemplateService {
	return &blogPostTemplateService{
		templateRepo: templateRepo,
		postRepo:     postRepo,
		tagRepo:      tagRepo,
		logger:       logger,
	}
}

// Create creates a new blog post template
func (s *blogPostTemplateService) Create(ctx context.Context, req *models.BlogPostTemplateCreateRequest) (*models.BlogPostTemplateResponse, error) {
	// Validate scope and IDs
	if err := s.validateScopeAndIDs(req.Scope, req.TenantID, req.WebsiteID); err != nil {
		return nil, err
	}

	// Create template model
	template := &models.BlogPostTemplate{
		TenantID:             req.TenantID,
		WebsiteID:            req.WebsiteID,
		Name:                 req.Name,
		Slug:                 req.Slug,
		Description:          req.Description,
		Type:                 req.Type,
		Scope:                req.Scope,
		Icon:                 req.Icon,
		Color:                req.Color,
		IsActive:             true,
		TitleTemplate:        req.TitleTemplate,
		ContentTemplate:      req.ContentTemplate,
		ExcerptTemplate:      req.ExcerptTemplate,
		Structure:            req.Structure,
		DefaultType:          req.DefaultType,
		DefaultStatus:        req.DefaultStatus,
		DefaultCategoryID:    req.DefaultCategoryID,
		DefaultAllowComments: req.DefaultAllowComments,
	}

	// Convert default tags to models.JSON
	if len(req.DefaultTags) > 0 {
		template.DefaultTags = models.JSON{"tags": req.DefaultTags}
	}

	// Get user ID from context
	if userID, ok := ctx.Value("user_id").(uint); ok {
		template.CreatedBy = userID
	}

	// Create template
	if err := s.templateRepo.Create(ctx, template); err != nil {
		s.logger.WithError(err).Error("Failed to create blog post template")
		return nil, err
	}

	// Convert to response
	response := &models.BlogPostTemplateResponse{}
	response.FromBlogPostTemplate(template)

	return response, nil
}

// GetByID retrieves a blog post template by ID
func (s *blogPostTemplateService) GetByID(ctx context.Context, id uint) (*models.BlogPostTemplateResponse, error) {
	template, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &models.BlogPostTemplateResponse{}
	response.FromBlogPostTemplate(template)

	return response, nil
}

// GetBySlug retrieves a blog post template by slug and scope
func (s *blogPostTemplateService) GetBySlug(ctx context.Context, slug string, scope models.BlogPostTemplateScope, scopeID *uint) (*models.BlogPostTemplateResponse, error) {
	template, err := s.templateRepo.GetBySlug(ctx, slug, scope, scopeID)
	if err != nil {
		return nil, err
	}

	response := &models.BlogPostTemplateResponse{}
	response.FromBlogPostTemplate(template)

	return response, nil
}

// Update updates an existing blog post template
func (s *blogPostTemplateService) Update(ctx context.Context, id uint, req *models.BlogPostTemplateUpdateRequest) (*models.BlogPostTemplateResponse, error) {
	// Get existing template
	template, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Name != "" {
		template.Name = req.Name
	}
	if req.Description != "" {
		template.Description = req.Description
	}
	if req.Icon != "" {
		template.Icon = req.Icon
	}
	if req.Color != "" {
		template.Color = req.Color
	}
	if req.IsActive != nil {
		template.IsActive = *req.IsActive
	}
	if req.IsFeatured != nil {
		template.IsFeatured = *req.IsFeatured
	}
	if req.TitleTemplate != "" {
		template.TitleTemplate = req.TitleTemplate
	}
	if req.ContentTemplate != "" {
		template.ContentTemplate = req.ContentTemplate
	}
	if req.ExcerptTemplate != "" {
		template.ExcerptTemplate = req.ExcerptTemplate
	}
	if req.Structure != nil {
		template.Structure = *req.Structure
	}
	if req.DefaultType != "" {
		template.DefaultType = req.DefaultType
	}
	if req.DefaultStatus != "" {
		template.DefaultStatus = req.DefaultStatus
	}
	if req.DefaultCategoryID != nil {
		template.DefaultCategoryID = req.DefaultCategoryID
	}
	if req.DefaultAllowComments != nil {
		template.DefaultAllowComments = *req.DefaultAllowComments
	}
	// Convert default tags to models.JSON
	if len(req.DefaultTags) > 0 {
		template.DefaultTags = models.JSON{"tags": req.DefaultTags}
	}

	// Update template
	if err := s.templateRepo.Update(ctx, id, template); err != nil {
		s.logger.WithError(err).Error("Failed to update blog post template")
		return nil, err
	}

	// Convert to response
	response := &models.BlogPostTemplateResponse{}
	response.FromBlogPostTemplate(template)

	return response, nil
}

// Delete deletes a blog post template
func (s *blogPostTemplateService) Delete(ctx context.Context, id uint) error {
	return s.templateRepo.Delete(ctx, id)
}

// List retrieves blog post templates based on filter
func (s *blogPostTemplateService) List(ctx context.Context, filter *models.BlogPostTemplateFilter) ([]models.BlogPostTemplateResponse, int64, error) {
	templates, total, err := s.templateRepo.List(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	responses := make([]models.BlogPostTemplateResponse, len(templates))
	for i, template := range templates {
		response := &models.BlogPostTemplateResponse{}
		response.FromBlogPostTemplate(&template)
		responses[i] = *response
	}

	return responses, total, nil
}

// ListAccessible retrieves all templates accessible by a user
func (s *blogPostTemplateService) ListAccessible(ctx context.Context, tenantID, websiteID, userID uint) ([]models.BlogPostTemplateResponse, error) {
	templates, err := s.templateRepo.ListAccessible(ctx, tenantID, websiteID, userID)
	if err != nil {
		return nil, err
	}

	responses := make([]models.BlogPostTemplateResponse, len(templates))
	for i, template := range templates {
		response := &models.BlogPostTemplateResponse{}
		response.FromBlogPostTemplate(&template)
		responses[i] = *response
	}

	return responses, nil
}

// GetByType retrieves templates by type
func (s *blogPostTemplateService) GetByType(ctx context.Context, templateType models.BlogPostTemplateType, tenantID *uint) ([]models.BlogPostTemplateResponse, error) {
	templates, err := s.templateRepo.GetByType(ctx, templateType, tenantID)
	if err != nil {
		return nil, err
	}

	responses := make([]models.BlogPostTemplateResponse, len(templates))
	for i, template := range templates {
		response := &models.BlogPostTemplateResponse{}
		response.FromBlogPostTemplate(&template)
		responses[i] = *response
	}

	return responses, nil
}

// GetFeatured retrieves featured templates
func (s *blogPostTemplateService) GetFeatured(ctx context.Context, tenantID *uint, limit int) ([]models.BlogPostTemplateResponse, error) {
	templates, err := s.templateRepo.GetFeatured(ctx, tenantID, limit)
	if err != nil {
		return nil, err
	}

	responses := make([]models.BlogPostTemplateResponse, len(templates))
	for i, template := range templates {
		response := &models.BlogPostTemplateResponse{}
		response.FromBlogPostTemplate(&template)
		responses[i] = *response
	}

	return responses, nil
}

// GetPopular retrieves popular templates
func (s *blogPostTemplateService) GetPopular(ctx context.Context, tenantID *uint, limit int) ([]models.BlogPostTemplateResponse, error) {
	templates, err := s.templateRepo.GetPopularTemplates(ctx, tenantID, limit)
	if err != nil {
		return nil, err
	}

	responses := make([]models.BlogPostTemplateResponse, len(templates))
	for i, template := range templates {
		response := &models.BlogPostTemplateResponse{}
		response.FromBlogPostTemplate(&template)
		responses[i] = *response
	}

	return responses, nil
}

// Duplicate creates a copy of an existing template
func (s *blogPostTemplateService) Duplicate(ctx context.Context, id uint, newName, newSlug string, targetScope models.BlogPostTemplateScope, targetScopeID *uint, userID uint) (*models.BlogPostTemplateResponse, error) {
	// Validate scope and IDs
	if err := s.validateScopeAndIDs(targetScope, targetScopeID, targetScopeID); err != nil {
		return nil, err
	}

	// Set user ID for user scope
	if targetScope == models.BlogPostTemplateScopeUser {
		targetScopeID = &userID
	}

	// Duplicate template
	template, err := s.templateRepo.Duplicate(ctx, id, newName, newSlug, targetScope, targetScopeID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to duplicate blog post template")
		return nil, err
	}

	// Set created by
	template.CreatedBy = userID

	// Convert to response
	response := &models.BlogPostTemplateResponse{}
	response.FromBlogPostTemplate(template)

	return response, nil
}

// CreatePostFromTemplate creates a blog post from a template
func (s *blogPostTemplateService) CreatePostFromTemplate(ctx context.Context, tenantID, websiteID, userID uint, req *models.BlogPostFromTemplateRequest) (*models.BlogPostResponse, error) {
	// Get template
	template, err := s.templateRepo.GetByID(ctx, req.TemplateID)
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}

	// Validate template fields
	if err := s.ValidateTemplateFields(ctx, req.TemplateID, req.FieldValues); err != nil {
		return nil, err
	}

	// Process template content
	title := s.processTemplate(template.TitleTemplate, req.FieldValues)
	content := s.processTemplate(template.ContentTemplate, req.FieldValues)
	excerpt := s.processTemplate(template.ExcerptTemplate, req.FieldValues)

	// Override with provided values
	if req.Title != "" {
		title = req.Title
	}
	if req.Content != "" {
		content = req.Content
	}
	if req.Excerpt != "" {
		excerpt = req.Excerpt
	}

	// Generate slug if not provided - simple implementation
	slug := generateSlugFromTitle(title)

	// Prepare blog post creation request
	postReq := &models.BlogPostCreateRequest{
		TenantID:      tenantID,
		WebsiteID:     websiteID,
		Slug:          slug,
		Title:         title,
		Content:       content,
		Excerpt:       excerpt,
		AuthorID:      userID,
		CategoryID:    req.CategoryID,
		Type:          template.DefaultType,
		IsFeatured:    req.IsFeatured,
		AllowComments: req.AllowComments,
		FeaturedImage: req.FeaturedImage,
		ScheduledAt:   req.ScheduledAt,
		Status:        req.Status,
		TagIDs:        req.TagIDs,
	}

	// Use template defaults if not provided
	if postReq.CategoryID == nil && template.DefaultCategoryID != nil {
		postReq.CategoryID = template.DefaultCategoryID
	}
	if postReq.Status == "" && template.DefaultStatus != "" {
		postReq.Status = template.DefaultStatus
	} else if postReq.Status == "" {
		postReq.Status = models.BlogPostStatusDraft
	}

	// Handle default tags
	if len(postReq.TagIDs) == 0 && template.DefaultTags != nil {
		// Extract tags from models.JSON
		if tagsData, ok := template.DefaultTags["tags"]; ok {
			if tagSlice, ok := tagsData.([]interface{}); ok && len(tagSlice) > 0 {
				var defaultTags []string
				for _, tag := range tagSlice {
					if tagStr, ok := tag.(string); ok {
						defaultTags = append(defaultTags, tagStr)
					}
				}
				if len(defaultTags) > 0 {
					// Create or find tags
					tags, err := s.tagRepo.GetByNames(ctx, tenantID, websiteID, defaultTags)
					if err == nil {
						for _, tag := range tags {
							postReq.TagIDs = append(postReq.TagIDs, tag.ID)
						}
					}
				}
			}
		}
	}

	// Create blog post
	post := &models.BlogPost{
		TenantID:      postReq.TenantID,
		WebsiteID:     postReq.WebsiteID,
		Slug:          postReq.Slug,
		Title:         postReq.Title,
		Content:       postReq.Content,
		Excerpt:       postReq.Excerpt,
		AuthorID:      postReq.AuthorID,
		CategoryID:    postReq.CategoryID,
		Type:          postReq.Type,
		IsFeatured:    postReq.IsFeatured,
		AllowComments: postReq.AllowComments,
		FeaturedImage: postReq.FeaturedImage,
		ScheduledAt:   postReq.ScheduledAt,
		Status:        postReq.Status,
	}

	if err := s.postRepo.Create(ctx, post); err != nil {
		return nil, fmt.Errorf("failed to create post: %w", err)
	}

	// Attach tags if provided
	if len(postReq.TagIDs) > 0 {
		if err := s.postRepo.AttachTags(ctx, tenantID, post.ID, postReq.TagIDs); err != nil {
			s.logger.WithError(err).Warn("Failed to attach tags to post")
		}
	}

	// Increment template usage
	if err := s.templateRepo.IncrementUsage(ctx, req.TemplateID); err != nil {
		s.logger.WithError(err).Warn("Failed to increment template usage")
	}

	// Convert to response
	response := &models.BlogPostResponse{}
	response.FromBlogPost(post)

	return response, nil
}

// ValidateTemplateFields validates field values against template structure
func (s *blogPostTemplateService) ValidateTemplateFields(ctx context.Context, templateID uint, fieldValues map[string]interface{}) error {
	// Get template
	template, err := s.templateRepo.GetByID(ctx, templateID)
	if err != nil {
		return fmt.Errorf("template not found: %w", err)
	}

	// Validate each section and field
	for _, section := range template.Structure.Sections {
		for _, field := range section.Fields {
			value, exists := fieldValues[field.Name]

			// Check required fields
			if field.Required && !exists {
				return fmt.Errorf("required field '%s' is missing", field.Name)
			}

			// Skip validation if field not provided and not required
			if !exists {
				continue
			}

			// Validate field type
			if err := s.validateFieldType(field, value); err != nil {
				return fmt.Errorf("field '%s': %w", field.Name, err)
			}

			// Validate against regex pattern if provided
			if field.Validation != "" {
				if err := s.validateFieldPattern(field, value); err != nil {
					return fmt.Errorf("field '%s': %w", field.Name, err)
				}
			}
		}
	}

	return nil
}

// ListWithCursor retrieves blog post templates with cursor-based pagination
func (s *blogPostTemplateService) ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPostTemplateResponse, *pagination.CursorResponse, error) {
	// Extract websiteID from filters if provided, otherwise use 0
	var websiteID uint
	if wID, ok := filters["website_id"]; ok {
		if id, ok := wID.(uint); ok {
			websiteID = id
		}
	}

	templates, cursorResp, err := s.templateRepo.ListWithCursor(ctx, tenantID, websiteID, req, filters)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list templates with cursor: %w", err)
	}

	responses := make([]models.BlogPostTemplateResponse, len(templates))
	for i, template := range templates {
		response := &models.BlogPostTemplateResponse{}
		response.FromBlogPostTemplate(&template)
		responses[i] = *response
	}

	return responses, cursorResp, nil
}

// ListAccessibleWithCursor retrieves accessible blog post templates with cursor-based pagination
func (s *blogPostTemplateService) ListAccessibleWithCursor(ctx context.Context, tenantID, websiteID, userID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPostTemplateResponse, *pagination.CursorResponse, error) {
	templates, cursorResp, err := s.templateRepo.ListAccessibleWithCursor(ctx, tenantID, websiteID, userID, req, filters)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list accessible templates with cursor: %w", err)
	}

	responses := make([]models.BlogPostTemplateResponse, len(templates))
	for i, template := range templates {
		response := &models.BlogPostTemplateResponse{}
		response.FromBlogPostTemplate(&template)
		responses[i] = *response
	}

	return responses, cursorResp, nil
}

// validateScopeAndIDs validates that the correct IDs are provided for the scope
func (s *blogPostTemplateService) validateScopeAndIDs(scope models.BlogPostTemplateScope, tenantID, websiteID *uint) error {
	switch scope {
	case models.BlogPostTemplateScopeSystem:
		if tenantID != nil || websiteID != nil {
			return errors.New("system templates should not have tenant or website ID")
		}
	case models.BlogPostTemplateScopeTenant:
		if tenantID == nil {
			return errors.New("tenant ID required for tenant scope")
		}
		if websiteID != nil {
			return errors.New("tenant templates should not have website ID")
		}
	case models.BlogPostTemplateScopeWebsite:
		if websiteID == nil {
			return errors.New("website ID required for website scope")
		}
	case models.BlogPostTemplateScopeUser:
		// User scope doesn't require tenant/website ID
	default:
		return fmt.Errorf("invalid scope: %s", scope)
	}
	return nil
}

// processTemplate processes a template string with field values
func (s *blogPostTemplateService) processTemplate(template string, fieldValues map[string]interface{}) string {
	if template == "" {
		return ""
	}

	result := template

	// Replace variables in the format {{fieldName}}
	for key, value := range fieldValues {
		placeholder := fmt.Sprintf("{{%s}}", key)
		valueStr := fmt.Sprintf("%v", value)
		result = strings.ReplaceAll(result, placeholder, valueStr)
	}

	return result
}

// validateFieldType validates that a field value matches its expected type
func (s *blogPostTemplateService) validateFieldType(field models.TemplateField, value interface{}) error {
	switch field.Type {
	case "text", "textarea":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("expected string, got %T", value)
		}
	case "number":
		switch v := value.(type) {
		case int, int32, int64, float32, float64:
			// Valid number types
		case string:
			// Try to parse as number
			var num float64
			if _, err := fmt.Sscanf(v, "%f", &num); err != nil {
				return fmt.Errorf("expected number, got invalid string")
			}
		default:
			return fmt.Errorf("expected number, got %T", value)
		}
	case "date":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("expected date string, got %T", value)
		}
		// TODO: Add date format validation
	case "select":
		valueStr, ok := value.(string)
		if !ok {
			return fmt.Errorf("expected string for select field, got %T", value)
		}
		// Validate against options
		validOption := false
		for _, option := range field.Options {
			if option == valueStr {
				validOption = true
				break
			}
		}
		if !validOption {
			return fmt.Errorf("invalid option '%s', must be one of: %v", valueStr, field.Options)
		}
	case "image":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("expected image URL string, got %T", value)
		}
		// TODO: Add URL validation
	}

	return nil
}

// validateFieldPattern validates a field value against a regex pattern
func (s *blogPostTemplateService) validateFieldPattern(field models.TemplateField, value interface{}) error {
	valueStr, ok := value.(string)
	if !ok {
		return nil // Only validate string values
	}

	regex, err := regexp.Compile(field.Validation)
	if err != nil {
		return fmt.Errorf("invalid validation pattern: %w", err)
	}

	if !regex.MatchString(valueStr) {
		return fmt.Errorf("value does not match required pattern")
	}

	return nil
}

// generateSlugFromTitle creates a URL-friendly slug from a title
func generateSlugFromTitle(title string) string {
	// Convert to lowercase
	slug := strings.ToLower(title)

	// Replace spaces and non-alphanumeric characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading/trailing hyphens
	slug = strings.Trim(slug, "-")

	// Limit length to 100 characters
	if len(slug) > 100 {
		slug = slug[:100]
		// Remove trailing hyphen if created by truncation
		slug = strings.TrimRight(slug, "-")
	}

	return slug
}
