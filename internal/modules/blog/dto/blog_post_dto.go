package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// SEOMetadata represents SEO metadata for blog posts with flat structure
type SEOMetadata struct {
	// Basic Meta Tags
	MetaTitle       string   `json:"meta_title,omitempty" validate:"omitempty,max=60" example:"10 React Best Practices You Should Know"`
	MetaDescription string   `json:"meta_description,omitempty" validate:"omitempty,max=160" example:"Discover the top 10 React development best practices"`
	MetaKeywords    []string `json:"meta_keywords,omitempty" validate:"omitempty,max=10,dive,max=50" example:"[\"react\", \"javascript\", \"best practices\"]"`
	MetaRobots      string   `json:"meta_robots,omitempty" validate:"omitempty,max=50" example:"index,follow"`
	CanonicalURL    string   `json:"canonical_url,omitempty" validate:"omitempty,url" example:"https://blog.example.com/react-best-practices"`

	// Open Graph Meta Tags
	OGTitle       string `json:"og_title,omitempty" validate:"omitempty,max=60" example:"10 React Best Practices"`
	OGDescription string `json:"og_description,omitempty" validate:"omitempty,max=160" example:"Learn React best practices"`
	OGImage       string `json:"og_image,omitempty" validate:"omitempty,url" example:"https://example.com/og-image.jpg"`
	OGType        string `json:"og_type,omitempty" validate:"omitempty,max=50" example:"article"`
	OGURL         string `json:"og_url,omitempty" validate:"omitempty,url" example:"https://blog.example.com/react-best-practices"`
	OGSiteName    string `json:"og_site_name,omitempty" validate:"omitempty,max=100" example:"My Tech Blog"`
	OGLocale      string `json:"og_locale,omitempty" validate:"omitempty,max=10" example:"en_US"`

	// Twitter Card Meta Tags
	TwitterCard        string `json:"twitter_card,omitempty" validate:"omitempty,oneof=summary summary_large_image app player" example:"summary_large_image"`
	TwitterTitle       string `json:"twitter_title,omitempty" validate:"omitempty,max=70" example:"10 React Best Practices"`
	TwitterDescription string `json:"twitter_description,omitempty" validate:"omitempty,max=200" example:"Learn React best practices"`
	TwitterImage       string `json:"twitter_image,omitempty" validate:"omitempty,url" example:"https://example.com/twitter-image.jpg"`
	TwitterCreator     string `json:"twitter_creator,omitempty" validate:"omitempty,max=50" example:"@johndoe"`
	TwitterSite        string `json:"twitter_site,omitempty" validate:"omitempty,max=50" example:"@mytechblog"`

	// Schema.org Structured Data
	SchemaType string                 `json:"schema_type,omitempty" validate:"omitempty,max=50" example:"BlogPosting"`
	SchemaData map[string]interface{} `json:"schema_data,omitempty" example:"{\"@type\":\"BlogPosting\",\"headline\":\"10 React Best Practices\"}"`

	// SEO Settings
	FocusKeyword string `json:"focus_keyword,omitempty" validate:"omitempty,max=100" example:"react best practices"`
}

// BlogPostCreateRequest represents the request payload for creating a blog post
type BlogPostCreateRequest struct {
	Title           string                `json:"title" validate:"required,min=1,max=255" example:"My First Blog Post"`
	Slug            string                `json:"slug,omitempty" validate:"omitempty,min=1,max=255" example:"my-first-blog-post"`
	Content         string                `json:"content" validate:"required" example:"This is the comprehensive content of my first blog post..."`
	Excerpt         string                `json:"excerpt,omitempty" validate:"max=500" example:"A brief summary of my first blog post"`
	Status          models.BlogPostStatus `json:"status" validate:"required,oneof=draft review published scheduled archived rejected" example:"draft"`
	PublishedAt     *time.Time            `json:"published_at,omitempty" example:"2025-01-30T10:00:00Z"`
	CategoryID      uint                  `json:"category_id" validate:"required,min=1" example:"1"`
	TagIDs          []uint                `json:"tag_ids,omitempty" validate:"max=10" example:"[12, 45, 46]"`
	RelatedPostIDs  []uint                `json:"related_post_ids,omitempty" validate:"max=10" example:"[100, 101, 102]"`
	FeaturedImageID *uint                 `json:"featured_image_id,omitempty" validate:"omitempty,min=1" example:"234"`
	AuthorID        *uint                 `json:"author_id,omitempty" validate:"omitempty,min=1" example:"2"`
	Type            models.BlogPostType   `json:"type,omitempty" validate:"omitempty,oneof=post page announcement" example:"post"`
	IsFeatured      bool                  `json:"is_featured,omitempty" example:"false"`
	AllowComments   bool                  `json:"allow_comments,omitempty" example:"true"`
	IsSticky        bool                  `json:"is_sticky,omitempty" example:"false"`
	Password        string                `json:"password,omitempty" validate:"max=255" example:""`
	SEO             *SEOMetadata          `json:"seo,omitempty" validate:"omitempty,dive"`
}

// BlogPostUpdateRequest represents the request payload for updating a blog post
type BlogPostUpdateRequest struct {
	Title           *string                `json:"title,omitempty" validate:"omitempty,min=1,max=255" example:"Updated Blog Post Title"`
	Slug            *string                `json:"slug,omitempty" validate:"omitempty,min=1,max=255" example:"updated-blog-post"`
	Content         *string                `json:"content,omitempty" validate:"omitempty" example:"Updated content of the blog post..."`
	Excerpt         *string                `json:"excerpt,omitempty" validate:"omitempty,max=500" example:"Updated excerpt"`
	Status          *models.BlogPostStatus `json:"status,omitempty" validate:"omitempty,oneof=draft review published scheduled archived rejected" example:"published"`
	PublishedAt     *time.Time             `json:"published_at,omitempty" example:"2025-01-22T15:00:00Z"`
	CategoryID      *uint                  `json:"category_id,omitempty" validate:"omitempty,min=1" example:"2"`
	TagIDs          *[]uint                `json:"tag_ids,omitempty" validate:"omitempty,max=10" example:"[50, 51, 52]"`             // Complete tag list (backend handles sync)
	RelatedPostIDs  *[]uint                `json:"related_post_ids,omitempty" validate:"omitempty,max=10" example:"[100, 101, 102]"` // Manual related posts (backend handles sync)
	FeaturedImageID *uint                  `json:"featured_image_id,omitempty" validate:"omitempty,min=1" example:"235"`
	AuthorID        *uint                  `json:"author_id,omitempty" validate:"omitempty,min=1" example:"3"`
	Type            *models.BlogPostType   `json:"type,omitempty" validate:"omitempty,oneof=post page announcement" example:"post"`
	IsFeatured      *bool                  `json:"is_featured,omitempty" example:"true"`
	AllowComments   *bool                  `json:"allow_comments,omitempty" example:"false"`
	IsSticky        *bool                  `json:"is_sticky,omitempty" example:"true"`
	Password        *string                `json:"password,omitempty" validate:"omitempty,max=255" example:""`
	SEO             *SEOMetadata           `json:"seo,omitempty" validate:"omitempty,dive"`
}

// BlogPostResponse represents the response payload for a blog post
// KEEP_OMITEMPTY: Optional content fields, nullable relationships, and optional nested objects
type BlogPostResponse struct {
	ID              uint                  `json:"id"`
	Title           string                `json:"title"`
	Slug            string                `json:"slug"`
	Content         string                `json:"content,omitempty"` // KEEP_OMITEMPTY: Omit in list views for performance
	Excerpt         string                `json:"excerpt"`           // Always return, empty string if not set
	Status          models.BlogPostStatus `json:"status"`
	PublishedAt     *time.Time            `json:"published_at,omitempty"` // KEEP_OMITEMPTY: Optional publish timestamp
	CategoryID      uint                  `json:"category_id"`
	TagIDs          []uint                `json:"tag_ids"`                     // Always return array, even if empty
	RelatedPostIDs  []uint                `json:"related_post_ids"`            // Manual related posts, always return array
	FeaturedImageID *uint                 `json:"featured_image_id,omitempty"` // KEEP_OMITEMPTY: Optional media
	AuthorID        uint                  `json:"author_id"`
	Type            models.BlogPostType   `json:"type"`
	IsFeatured      bool                  `json:"is_featured"`
	AllowComments   bool                  `json:"allow_comments"`
	IsSticky        bool                  `json:"is_sticky"`
	ViewCount       uint                  `json:"view_count"`
	CommentCount    uint                  `json:"comment_count"`
	WebsiteID       uint                  `json:"website_id"`
	TenantID        uint                  `json:"tenant_id"`
	CreatedAt       time.Time             `json:"created_at"`
	UpdatedAt       time.Time             `json:"updated_at"`
	// Related entities - only populated when requested via ?include parameter
	Category      *BlogCategoryResponse `json:"category,omitempty"`       // KEEP_OMITEMPTY: Optional nested object
	Tags          []BlogTagResponse     `json:"tags,omitempty"`           // KEEP_OMITEMPTY: Optional nested array
	Author        *AuthorResponse       `json:"author,omitempty"`         // KEEP_OMITEMPTY: Optional nested object
	FeaturedImage *MediaResponse        `json:"featured_image,omitempty"` // KEEP_OMITEMPTY: Optional nested object
	// SEO metadata - only populated in detail view
	SEO *SEOMetadata `json:"seo,omitempty"` // KEEP_OMITEMPTY: Optional SEO metadata
}

// AuthorResponse represents author information
type AuthorResponse struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Email     string `json:"email,omitempty"` // KEEP_OMITEMPTY: Privacy concern
	AvatarURL string `json:"avatar_url,omitempty"`
}

// MediaResponse represents media/image information
type MediaResponse struct {
	ID       uint   `json:"id"`
	URL      string `json:"url"`
	Alt      string `json:"alt,omitempty"`
	Width    int    `json:"width,omitempty"`
	Height   int    `json:"height,omitempty"`
	Size     int64  `json:"size,omitempty"`
	MimeType string `json:"mime_type,omitempty"`
}

// BlogPostListResponse represents the response for listing blog posts
type BlogPostListResponse struct {
	Posts      []BlogPostResponse         `json:"posts"`
	Pagination *pagination.CursorResponse `json:"pagination,omitempty"`
}

// BlogPostFilter represents filter parameters for listing blog posts
type BlogPostFilter struct {
	pagination.CursorRequest
	Title      string                 `json:"title,omitempty" form:"title" validate:"omitempty,max=255" example:"My Blog"`
	Status     *models.BlogPostStatus `json:"status,omitempty" form:"status" validate:"omitempty,oneof=draft review published scheduled archived rejected" example:"published"`
	Type       *models.BlogPostType   `json:"type,omitempty" form:"type" validate:"omitempty,oneof=post page announcement" example:"post"`
	CategoryID *uint                  `json:"category_id,omitempty" form:"category_id" validate:"omitempty,min=1" example:"1"`
	AuthorID   *uint                  `json:"author_id,omitempty" form:"author_id" validate:"omitempty,min=1" example:"1"`
	IsFeatured *bool                  `json:"is_featured,omitempty" form:"is_featured" example:"true"`
	TagID      *uint                  `json:"tag_id,omitempty" form:"tag_id" validate:"omitempty,min=1" example:"1"`
	DateFrom   *time.Time             `json:"date_from,omitempty" form:"date_from" example:"2024-01-01T00:00:00Z"`
	DateTo     *time.Time             `json:"date_to,omitempty" form:"date_to" example:"2024-12-31T23:59:59Z"`
	SortBy     string                 `json:"sort_by,omitempty" form:"sort_by" validate:"omitempty,oneof=id title created_at updated_at published_at view_count" example:"created_at"`
	SortOrder  string                 `json:"sort_order,omitempty" form:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// BlogPostRelatedRequest represents request parameters for getting related posts
type BlogPostRelatedRequest struct {
	Limit         int    `json:"limit,omitempty" form:"limit" validate:"omitempty,min=1,max=20" example:"5"`
	Strategy      string `json:"strategy,omitempty" form:"strategy" validate:"omitempty,oneof=tags category both ml" example:"both"`
	SortBy        string `json:"sort_by,omitempty" form:"sort_by" validate:"omitempty,oneof=relevance published_at view_count" example:"relevance"`
	ExcludeIDs    []uint `json:"exclude_ids,omitempty" form:"exclude_ids" validate:"omitempty,max=10" example:"[10,20,30]"`
	IncludeFields string `json:"include_fields,omitempty" form:"include_fields" validate:"omitempty" example:"title,slug,excerpt,featured_image_id"`
}

// BlogPostRelatedResponse represents a related blog post with relevance info
type BlogPostRelatedResponse struct {
	ID              uint       `json:"id"`
	Title           string     `json:"title"`
	Slug            string     `json:"slug"`
	Excerpt         string     `json:"excerpt,omitempty"`
	FeaturedImageID *uint      `json:"featured_image_id,omitempty"`
	PublishedAt     *time.Time `json:"published_at"`
	ViewCount       uint       `json:"view_count"`
	RelevanceScore  float64    `json:"relevance_score,omitempty"` // 0-1 score
	MatchType       string     `json:"match_type,omitempty"`      // "tags", "category", "both"
	SharedTags      []uint     `json:"shared_tags,omitempty"`     // IDs of shared tags
}
