package blog

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	seoServices "github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	tenantRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	tenantServices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterBlogRoutes registers all blog module routes
func RegisterBlogRoutes(r *gin.RouterGroup, blogServices *services.BlogServices, seoMetaService seoServices.SEOMetaService, logger utils.Logger, db *gorm.DB) {
	// Create handlers
	categoryHandler := handlers.NewBlogCategoryHandler(blogServices.CategoryService)
	postHandler := handlers.NewBlogPostHandler(blogServices.PostService)
	tagHandler := handlers.NewBlogTagHandler(blogServices.TagService)
	autosaveHandler := handlers.NewBlogPostAutosaveHandler(blogServices.PostAutosaveService, logger)
	templateHandler := handlers.NewBlogPostTemplateHandler(blogServices.PostTemplateService, logger)
	progressHandler := handlers.NewBlogPostReadingProgressHandler(blogServices.ReadingProgressService)
	newsletterHandler := handlers.NewBlogNewsletterSubscriptionHandler(blogServices.NewsletterSubscriptionService, logger)
	workflowHandler := handlers.NewWorkflowHandler(blogServices.WorkflowService, blogServices.PostService)
	// ratingHandler := handlers.NewBlogPostRatingHandler(blogServices.RatingService, logger) // Temporarily disabled
	authorHandler := handlers.NewBlogPostAuthorHandler(blogServices.AuthorService, logger)
	royaltyHandler := handlers.NewBlogPostRoyaltyHandler(blogServices.RoyaltyService, logger)

	// Create search handler if search service is available
	var searchHandler *handlers.BlogSearchHandler
	if blogServices.SearchService != nil {
		searchHandler = handlers.NewBlogSearchHandler(blogServices.SearchService.GetSearchService(), validator.GetDefaultValidator(), logger)
	}

	// Create SEO handler if SEO service is provided
	var blogPostSEOHandler *handlers.BlogPostSEOHandler
	if seoMetaService != nil {
		blogPostSEOHandler = handlers.NewBlogPostSEOHandler(seoMetaService, logger)
	}

	// Initialize tenant service for middleware
	tenantRepo := tenantRepositories.NewMySQLTenantRepository(db)
	planRepo := tenantRepositories.NewMySQLTenantPlanRepository(db)
	tenantService := tenantServices.NewTenantService(tenantRepo, planRepo)

	// Get JWT service for authentication
	_, jwtService, _, err := auth.GetAuthServiceDependencies(db, logger)
	if err != nil {
		logger.WithError(err).Error("Failed to get auth dependencies for blog routes")
		panic("Failed to initialize JWT service for blog routes")
	}

	// Create main CMS group for all blog routes
	cms := r.Group("/blog")

	// Protected routes group (require authentication and tenant isolation)
	protectedRoutes := cms.Group("")
	protectedRoutes.Use(middleware.JWTAuthMiddleware(*jwtService))
	protectedRoutes.Use(middleware.RequireAuthentication())
	protectedRoutes.Use(middleware.TenantIsolationMiddleware(tenantService))
	{
		// Public category routes
		publicCategories := protectedRoutes.Group("/categories")
		{
			publicCategories.GET("", categoryHandler.ListCategories)
			publicCategories.GET("/:id", categoryHandler.GetCategory)
			publicCategories.GET("/slug/:slug", categoryHandler.GetCategoryBySlug)
			publicCategories.GET("/hierarchy", categoryHandler.GetCategoryHierarchy)
			publicCategories.GET("/select", categoryHandler.ListForSelect)
			publicCategories.GET("/:id/ancestors", categoryHandler.GetAncestors)
			publicCategories.GET("/:id/breadcrumb", categoryHandler.GetBreadcrumb)
		}

		// Public post routes
		publicPosts := protectedRoutes.Group("/posts")
		{
			publicPosts.GET("", postHandler.ListPosts)
			publicPosts.GET("/:id", postHandler.GetPost)
			publicPosts.GET("/slug/:slug", postHandler.GetPostBySlug)
			publicPosts.GET("/published", postHandler.GetPublishedPosts)
			publicPosts.GET("/featured", postHandler.GetFeaturedPosts)
			publicPosts.GET("/:id/related", postHandler.GetRelatedPosts)
			publicPosts.GET("/stats", postHandler.GetPostStats)
			publicPosts.GET("/popular", postHandler.GetPopularPosts)
			// publicPosts.GET("/:id/rating-stats", ratingHandler.GetRatingStats) // Temporarily disabled
			// publicPosts.GET("/:id/ratings", ratingHandler.ListRatingsByPost) // Temporarily disabled
		}

		// Public tag routes
		publicTags := protectedRoutes.Group("/tags")
		{
			publicTags.GET("", tagHandler.ListTags)
			publicTags.GET("/:id", tagHandler.GetTag)
			publicTags.GET("/slug/:slug", tagHandler.GetTagBySlug)
			publicTags.GET("/most-used", tagHandler.GetMostUsedTags)
			publicTags.GET("/suggestions", tagHandler.GetTagSuggestions)
			publicTags.GET("/stats", tagHandler.GetTagStats)
			publicTags.GET("/select", tagHandler.ListForSelect)
		}

		// Public author routes
		publicAuthors := protectedRoutes.Group("/authors")
		{
			publicAuthors.GET("/select", middleware.RequirePermission("blog.posts.read"), authorHandler.ListForSelect) // List authors for select components
		}

		// Public template routes
		publicTemplates := protectedRoutes.Group("/templates")
		{
			publicTemplates.GET("", templateHandler.ListTemplates)
			publicTemplates.GET("/:id", templateHandler.GetTemplate)
			publicTemplates.GET("/slug/:slug", templateHandler.GetTemplateBySlug)
			publicTemplates.GET("/featured", templateHandler.GetFeaturedTemplates)
			publicTemplates.GET("/popular", templateHandler.GetPopularTemplates)
		}

		// Public newsletter routes
		newsletter := protectedRoutes.Group("/newsletter")
		{
			newsletter.POST("/subscribe", newsletterHandler.Subscribe)
			newsletter.POST("/confirm", newsletterHandler.Confirm)
			newsletter.POST("/unsubscribe", newsletterHandler.Unsubscribe)
		}

		// Protected category routes
		categories := protectedRoutes.Group("/categories")
		{
			categories.POST("", middleware.RequirePermission("blog.categories.create"), categoryHandler.CreateCategory)
			categories.PUT("/:id", middleware.RequirePermission("blog.categories.update"), categoryHandler.UpdateCategory)
			categories.DELETE("/:id", middleware.RequirePermission("blog.categories.delete"), categoryHandler.DeleteCategory)
			categories.POST("/:id/move", middleware.RequirePermission("blog.categories.update"), categoryHandler.MoveCategory)
			categories.POST("/positions", middleware.RequirePermission("blog.categories.update"), categoryHandler.UpdateCategoryPositions)
		}

		// Auto-save management routes (global)
		autosave := protectedRoutes.Group("/autosave")
		{
			autosave.POST("/resolve-conflict", middleware.RequirePermission("blog.posts.update"), autosaveHandler.ResolveConflict) // Resolve auto-save conflict
			autosave.GET("/conflicted", middleware.RequirePermission("blog.posts.read"), autosaveHandler.GetConflictedAutosaves)   // Get all conflicted auto-saves
		}

		// Protected post routes
		posts := protectedRoutes.Group("/posts")
		{
			posts.POST("", middleware.RequirePermission("blog.posts.create"), postHandler.CreatePost)
			posts.PUT("/:id", middleware.RequirePermission("blog.posts.update"), postHandler.UpdatePost)
			posts.DELETE("/:id", middleware.RequirePermission("blog.posts.delete"), postHandler.DeletePost)
			posts.POST("/:id/publish", middleware.RequirePermission("blog.posts.publish"), postHandler.PublishPost)
			posts.POST("/:id/unpublish", middleware.RequirePermission("blog.posts.publish"), postHandler.UnpublishPost)
			posts.POST("/:id/tags/attach", middleware.RequirePermission("blog.posts.update"), postHandler.AttachTags)
			posts.POST("/:id/tags/detach", middleware.RequirePermission("blog.posts.update"), postHandler.DetachTags)
			posts.POST("/:id/tags/sync", middleware.RequirePermission("blog.posts.update"), postHandler.SyncTags)

			// Auto-save endpoints
			posts.POST("/:id/autosave", middleware.RequirePermission("blog.posts.update"), autosaveHandler.AutoSave)                    // Auto-save post
			posts.GET("/:id/autosave", middleware.RequirePermission("blog.posts.read"), autosaveHandler.GetAutosave)                    // Get auto-save
			posts.DELETE("/:id/autosave", middleware.RequirePermission("blog.posts.update"), autosaveHandler.DeleteAutosave)            // Delete auto-save
			posts.GET("/:id/autosave/status", middleware.RequirePermission("blog.posts.read"), autosaveHandler.GetAutosaveStatus)       // Get auto-save status
			posts.POST("/:id/autosave/restore", middleware.RequirePermission("blog.posts.update"), autosaveHandler.RestoreFromAutosave) // Restore from auto-save

			// Reading progress endpoints
			posts.POST("/:id/progress", middleware.RequirePermission("blog.posts.read"), progressHandler.UpdateReadingProgress)   // Update reading progress
			posts.GET("/:id/progress", middleware.RequirePermission("blog.posts.read"), progressHandler.GetReadingProgress)       // Get reading progress
			posts.GET("/:id/reading-stats", middleware.RequirePermission("blog.posts.read"), progressHandler.GetPostReadingStats) // Get post reading stats
			posts.GET("/:id/readers", middleware.RequirePermission("blog.posts.read"), progressHandler.GetPostReaders)            // Get post readers

			// Rating endpoints
			// posts.GET("/:id/my-rating", middleware.RequirePermission("blog.posts.read"), ratingHandler.GetUserRatingForPost) // Temporarily disabled
			// posts.GET("/:id/can-rate", middleware.RequirePermission("blog.posts.read"), ratingHandler.CanUserRate) // Temporarily disabled

			// Author management endpoints
			posts.POST("/:id/authors", middleware.RequirePermission("blog.posts.update"), authorHandler.AddAuthor)                                  // Add author to post
			posts.GET("/:id/authors", middleware.RequirePermission("blog.posts.read"), authorHandler.ListAuthorsByPost)                             // List authors for post
			posts.GET("/:id/authors/primary", middleware.RequirePermission("blog.posts.read"), authorHandler.GetPrimaryAuthor)                      // Get primary author
			posts.GET("/:id/authors/:user_id", middleware.RequirePermission("blog.posts.read"), authorHandler.GetAuthor)                            // Get specific author
			posts.PUT("/:id/authors/:user_id", middleware.RequirePermission("blog.posts.update"), authorHandler.UpdateAuthorRole)                   // Update author role
			posts.DELETE("/:id/authors/:user_id", middleware.RequirePermission("blog.posts.update"), authorHandler.RemoveAuthor)                    // Remove author
			posts.POST("/:id/authors/bulk", middleware.RequirePermission("blog.posts.update"), authorHandler.BulkAddAuthors)                        // Bulk add authors
			posts.PUT("/:id/authors/replace", middleware.RequirePermission("blog.posts.update"), authorHandler.ReplaceAuthors)                      // Replace all authors
			posts.GET("/:id/authors/stats", middleware.RequirePermission("blog.posts.read"), authorHandler.GetAuthorStats)                          // Get author statistics
			posts.POST("/:id/authors/transfer-primary", middleware.RequirePermission("blog.posts.update"), authorHandler.TransferPrimaryAuthorship) // Transfer primary authorship

			// Royalty management endpoints
			posts.GET("/:id/royalty", middleware.RequirePermission("blog.posts.read"), royaltyHandler.GetRoyaltyByPost)             // Get royalty for post
			posts.POST("/:id/royalty/calculate", middleware.RequirePermission("blog.posts.read"), royaltyHandler.CalculateRoyalty)  // Calculate royalty
			posts.PUT("/:id/royalty/cms", middleware.RequirePermission("blog.posts.update"), royaltyHandler.UpdateCMSRoyalty)       // Update CMS royalty
			posts.PUT("/:id/royalty/editor", middleware.RequirePermission("blog.posts.update"), royaltyHandler.UpdateEditorRoyalty) // Update editor royalty
			posts.PUT("/:id/royalty/final", middleware.RequirePermission("blog.posts.update"), royaltyHandler.UpdateFinalRoyalty)   // Update final royalty

			// SEO convenience endpoints for blog posts
			if blogPostSEOHandler != nil {
				posts.POST("/:id/seo", middleware.RequirePermission("blog.posts.update"), blogPostSEOHandler.CreatePostSEO)          // Create SEO for post
				posts.GET("/:id/seo", middleware.RequirePermission("blog.posts.read"), blogPostSEOHandler.GetPostSEO)                // Get SEO for post
				posts.PUT("/:id/seo", middleware.RequirePermission("blog.posts.update"), blogPostSEOHandler.UpdatePostSEO)           // Update SEO for post
				posts.DELETE("/:id/seo", middleware.RequirePermission("blog.posts.update"), blogPostSEOHandler.DeletePostSEO)        // Delete SEO for post
				posts.POST("/:id/seo/analyze", middleware.RequirePermission("blog.posts.read"), blogPostSEOHandler.AnalyzePostSEO)   // Analyze post SEO
				posts.POST("/:id/seo/validate", middleware.RequirePermission("blog.posts.read"), blogPostSEOHandler.ValidatePostSEO) // Validate post SEO
				posts.GET("/:id/seo/tags", middleware.RequirePermission("blog.posts.read"), blogPostSEOHandler.GeneratePostMetaTags) // Generate meta tags
			}

			// Workflow endpoints
			posts.POST("/:id/workflow/transition", workflowHandler.TransitionWorkflow) // Transition workflow state
			posts.POST("/:id/workflow/assign", workflowHandler.AssignPost)             // Assign post to user
			posts.PUT("/:id/workflow/notes", workflowHandler.UpdateWorkflowNotes)      // Update workflow notes
		}

		// Workflow management routes
		workflow := protectedRoutes.Group("/workflow")
		{
			workflow.GET("/my-tasks", workflowHandler.GetMyTasks)             // Get my assigned tasks
			workflow.GET("/queue/:state", workflowHandler.GetWorkflowQueue)   // Get workflow queue by state
			workflow.GET("/:id/history", workflowHandler.GetWorkflowHistory)  // Get workflow history
			workflow.GET("/:id/actions", workflowHandler.GetAvailableActions) // Get available actions
		}

		// Global reading progress endpoints
		reading := protectedRoutes.Group("/reading")
		{
			reading.GET("/stats", middleware.RequirePermission("blog.posts.read"), progressHandler.GetUserReadingStats)     // Get user reading stats
			reading.GET("/analytics", middleware.RequirePermission("blog.posts.read"), progressHandler.GetReadingAnalytics) // Get reading analytics
			reading.GET("/history", middleware.RequirePermission("blog.posts.read"), progressHandler.GetUserReadingHistory) // Get user reading history
		}

		// Protected tag routes
		tags := protectedRoutes.Group("/tags")
		{
			tags.POST("", middleware.RequirePermission("blog.tags.create"), tagHandler.CreateTag)
			tags.PUT("/:id", middleware.RequirePermission("blog.tags.update"), tagHandler.UpdateTag)
			tags.DELETE("/:id", middleware.RequirePermission("blog.tags.delete"), tagHandler.DeleteTag)
		}

		// Protected template routes
		templates := protectedRoutes.Group("/templates")
		{
			templates.POST("", middleware.RequirePermission("blog.posts.create"), templateHandler.CreateTemplate)
			templates.PUT("/:id", middleware.RequirePermission("blog.posts.update"), templateHandler.UpdateTemplate)
			templates.DELETE("/:id", middleware.RequirePermission("blog.posts.delete"), templateHandler.DeleteTemplate)
			templates.POST("/:id/duplicate", middleware.RequirePermission("blog.posts.create"), templateHandler.DuplicateTemplate)
			templates.GET("/accessible", middleware.RequirePermission("blog.posts.read"), templateHandler.ListAccessibleTemplates)
			templates.POST("/create-post", middleware.RequirePermission("blog.posts.create"), templateHandler.CreatePostFromTemplate)
		}

		// Protected newsletter routes
		protectedNewsletter := protectedRoutes.Group("/newsletter")
		{
			protectedNewsletter.GET("/subscriptions", middleware.RequirePermission("notification.templates.read"), newsletterHandler.List)
			protectedNewsletter.GET("/subscriptions/:id", middleware.RequirePermission("notification.templates.read"), newsletterHandler.GetByID)
			protectedNewsletter.PUT("/subscriptions/:id", middleware.RequirePermission("notification.templates.update"), newsletterHandler.Update)
			protectedNewsletter.DELETE("/subscriptions/:id", middleware.RequirePermission("notification.templates.delete"), newsletterHandler.Delete)
			protectedNewsletter.GET("/stats", middleware.RequirePermission("notification.templates.read"), newsletterHandler.GetStats)
			protectedNewsletter.GET("/growth", middleware.RequirePermission("notification.templates.read"), newsletterHandler.GetGrowthData)
			protectedNewsletter.GET("/export", middleware.RequirePermission("notification.templates.read"), newsletterHandler.ExportSubscribers)
		}

		// Royalty management routes
		royalties := protectedRoutes.Group("/royalties")
		{
			royalties.POST("", middleware.RequirePermission("blog.posts.create"), royaltyHandler.CreateRoyalty)               // Create royalty
			royalties.GET("", middleware.RequirePermission("blog.posts.read"), royaltyHandler.ListRoyalties)                  // List royalties
			royalties.GET("/cursor", middleware.RequirePermission("blog.posts.read"), royaltyHandler.ListRoyaltiesWithCursor) // List with cursor
			royalties.GET("/stats", middleware.RequirePermission("blog.posts.read"), royaltyHandler.GetRoyaltyStats)          // Get royalty stats
			royalties.GET("/period", middleware.RequirePermission("blog.posts.read"), royaltyHandler.GetRoyaltyByPeriod)      // Get royalty by period
			royalties.GET("/:id", middleware.RequirePermission("blog.posts.read"), royaltyHandler.GetRoyalty)                 // Get royalty by ID
			royalties.PUT("/:id", middleware.RequirePermission("blog.posts.update"), royaltyHandler.UpdateRoyalty)            // Update royalty
			royalties.DELETE("/:id", middleware.RequirePermission("blog.posts.delete"), royaltyHandler.DeleteRoyalty)         // Delete royalty
		}

		// Rating management routes
		// ratings := protectedRoutes.Group("/ratings") // Temporarily disabled
		// {
		//	ratings.POST("", middleware.RequirePermission("blog.posts.read"), ratingHandler.CreateRating)    // Create rating
		//	ratings.GET("", middleware.RequirePermission("blog.posts.read"), ratingHandler.ListRatings)      // List ratings
		//	ratings.GET("/:id", middleware.RequirePermission("blog.posts.read"), ratingHandler.GetRating)    // Get rating by ID
		//	ratings.PUT("/:id", middleware.RequirePermission("blog.posts.read"), ratingHandler.UpdateRating) // Update rating
		//	ratings.DELETE("/:id", middleware.RequirePermission("blog.posts.read"), ratingHandler.DeleteRating) // Delete rating
		// }
	}

	// Public search routes (no authentication required for basic search)
	if searchHandler != nil {
		publicSearch := cms.Group("/search")
		{
			publicSearch.GET("", searchHandler.SearchPosts)               // Search posts
			publicSearch.GET("/autocomplete", searchHandler.Autocomplete) // Autocomplete suggestions
			publicSearch.GET("/health", searchHandler.HealthCheck)        // Health check
		}

		// Protected search routes (require authentication)
		protectedSearch := protectedRoutes.Group("/search")
		{
			protectedSearch.GET("/stats", middleware.RequirePermission("blog.posts.read"), searchHandler.GetSearchStats) // Search statistics
		}
	}
}

// RegisterBlogExportRoutes registers blog export/import routes
func RegisterBlogExportRoutes(r *gin.RouterGroup, exportHandler interface{}) {
	// This function would be called separately to register export routes
	// Implementation would depend on the specific export handler interface
	// For now, this is a placeholder to show where export routes would be registered
}
