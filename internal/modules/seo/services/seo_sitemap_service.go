package services

import (
	"context"
	"encoding/xml"
	"fmt"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SitemapEntry represents a single sitemap entry
type SitemapEntry struct {
	URL        string     `xml:"loc"`
	LastMod    *time.Time `xml:"lastmod,omitempty"`
	ChangeFreq *string    `xml:"changefreq,omitempty"`
	Priority   *float64   `xml:"priority,omitempty"`
}

// SitemapXML represents the XML sitemap structure
type SitemapXML struct {
	XMLName xml.Name       `xml:"urlset"`
	Xmlns   string         `xml:"xmlns,attr"`
	URLs    []SitemapEntry `xml:"url"`
}

// SitemapIndexEntry represents a sitemap index entry
type SitemapIndexEntry struct {
	URL     string     `xml:"loc"`
	LastMod *time.Time `xml:"lastmod,omitempty"`
}

// SitemapIndexXML represents the XML sitemap index structure
type SitemapIndexXML struct {
	XMLName  xml.Name            `xml:"sitemapindex"`
	Xmlns    string              `xml:"xmlns,attr"`
	Sitemaps []SitemapIndexEntry `xml:"sitemap"`
}

// SEOSitemapService defines the interface for SEO sitemap operations
type SEOSitemapService interface {
	// Basic CRUD operations
	Create(ctx context.Context, req *models.SEOSitemapCreateRequest) (*models.SEOSitemapResponse, error)
	GetByID(ctx context.Context, id uint) (*models.SEOSitemapResponse, error)
	Update(ctx context.Context, id uint, req *models.SEOSitemapUpdateRequest) (*models.SEOSitemapResponse, error)
	Delete(ctx context.Context, id uint) error

	// List and query operations
	List(ctx context.Context, req *models.SEOSitemapListRequest) ([]*models.SEOSitemapResponse, int64, error)
	GetByWebsite(ctx context.Context, websiteID, tenantID uint) ([]*models.SEOSitemapResponse, error)
	GetByType(ctx context.Context, websiteID, tenantID uint, sitemapType models.SitemapType) ([]*models.SEOSitemapResponse, error)

	// Sitemap generation operations
	GenerateSitemap(ctx context.Context, websiteID, tenantID uint, sitemapType models.SitemapType) (*models.SEOSitemapResponse, error)
	GenerateAllSitemaps(ctx context.Context, websiteID, tenantID uint) ([]*models.SEOSitemapResponse, error)
	RefreshSitemap(ctx context.Context, id uint) (*models.SEOSitemapResponse, error)

	// XML output operations
	GetSitemapXML(ctx context.Context, id uint) (string, error)
	GetSitemapIndexXML(ctx context.Context, websiteID, tenantID uint) (string, error)
	ServeSitemap(ctx context.Context, websiteID, tenantID uint, filename string) (string, error)

	// Content discovery operations
	DiscoverPages(ctx context.Context, websiteID, tenantID uint) ([]SitemapEntry, error)
	DiscoverBlogPosts(ctx context.Context, websiteID, tenantID uint) ([]SitemapEntry, error)
	DiscoverStaticPages(ctx context.Context, websiteID, tenantID uint) ([]SitemapEntry, error)

	// Validation operations
	ValidateSitemap(ctx context.Context, id uint) error
	ValidateXML(ctx context.Context, xmlContent string) error

	// Statistics operations
	GetStats(ctx context.Context, websiteID, tenantID uint) (*models.SEOSitemapStatsResponse, error)

	// Maintenance operations
	CleanupOldSitemaps(ctx context.Context, days int) (int, error)
	CompressSitemaps(ctx context.Context, websiteID, tenantID uint) error
}

// seoSitemapService implements the SEOSitemapService interface
type seoSitemapService struct {
	repo   repositories.SEOSitemapRepository
	logger utils.Logger
}

// NewSEOSitemapService creates a new SEO sitemap service
func NewSEOSitemapService(
	repo repositories.SEOSitemapRepository,
	logger utils.Logger,
) SEOSitemapService {
	return &seoSitemapService{
		repo:   repo,
		logger: logger,
	}
}

// Create creates a new SEO sitemap
func (s *seoSitemapService) Create(ctx context.Context, req *models.SEOSitemapCreateRequest) (*models.SEOSitemapResponse, error) {
	// Validate the request
	if err := s.validateSitemapRequest(req); err != nil {
		return nil, err
	}

	// Create the sitemap model
	sitemap := &models.SEOSitemap{
		WebsiteID: req.WebsiteID,
		TenantID:  req.TenantID,

		Type:        req.Type,
		Name:        req.Name,
		Description: req.Description,
		Filename:    req.Filename,

		URLCount:   req.URLCount,
		FileSize:   req.FileSize,
		Compressed: req.Compressed,

		GenerationSettings: req.GenerationSettings,

		Status: req.Status,
	}

	// Set defaults
	if sitemap.Status == "" {
		sitemap.Status = models.SitemapStatusActive
	}

	// Create the sitemap
	if err := s.repo.Create(ctx, sitemap); err != nil {
		s.logger.Error("Failed to create SEO sitemap", "error", err)
		return nil, fmt.Errorf("failed to create sitemap: %w", err)
	}

	s.logger.Info("Created SEO sitemap", "id", sitemap.ID, "type", sitemap.Type)
	return sitemap.ToResponse(), nil
}

// GetByID retrieves an SEO sitemap by ID
func (s *seoSitemapService) GetByID(ctx context.Context, id uint) (*models.SEOSitemapResponse, error) {
	sitemap, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get sitemap: %w", err)
	}

	return sitemap.ToResponse(), nil
}

// Update updates an existing SEO sitemap
func (s *seoSitemapService) Update(ctx context.Context, id uint, req *models.SEOSitemapUpdateRequest) (*models.SEOSitemapResponse, error) {
	// Get existing sitemap
	sitemap, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get sitemap: %w", err)
	}

	// Update fields
	if req.Name != nil {
		sitemap.Name = *req.Name
	}
	if req.Description != nil {
		sitemap.Description = req.Description
	}
	if req.Filename != nil {
		sitemap.Filename = *req.Filename
	}
	if req.URLCount != nil {
		sitemap.URLCount = *req.URLCount
	}
	if req.FileSize != nil {
		sitemap.FileSize = *req.FileSize
	}
	if req.Compressed != nil {
		sitemap.Compressed = *req.Compressed
	}
	if req.GenerationSettings != nil {
		sitemap.GenerationSettings = req.GenerationSettings
	}
	if req.Status != nil {
		sitemap.Status = *req.Status
	}

	// Update the sitemap
	if err := s.repo.Update(ctx, sitemap); err != nil {
		s.logger.Error("Failed to update SEO sitemap", "error", err, "id", id)
		return nil, fmt.Errorf("failed to update sitemap: %w", err)
	}

	s.logger.Info("Updated SEO sitemap", "id", id)
	return sitemap.ToResponse(), nil
}

// Delete permanently deletes an SEO sitemap
func (s *seoSitemapService) Delete(ctx context.Context, id uint) error {
	if err := s.repo.Delete(ctx, id); err != nil {
		s.logger.Error("Failed to delete SEO sitemap", "error", err, "id", id)
		return fmt.Errorf("failed to delete sitemap: %w", err)
	}

	s.logger.Info("Deleted SEO sitemap", "id", id)
	return nil
}

// List retrieves SEO sitemaps with pagination and filters
func (s *seoSitemapService) List(ctx context.Context, req *models.SEOSitemapListRequest) ([]*models.SEOSitemapResponse, int64, error) {
	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// Build filters
	filters := make(map[string]interface{})
	if req.Type != nil {
		filters["type"] = *req.Type
	}
	if req.Status != nil {
		filters["status"] = *req.Status
	}
	if req.Name != nil {
		filters["name"] = *req.Name
	}
	if req.SortBy != nil {
		filters["sort_by"] = *req.SortBy
	}
	if req.SortOrder != nil {
		filters["sort_order"] = *req.SortOrder
	}

	// Get sitemaps
	sitemaps, total, err := s.repo.List(ctx, req.WebsiteID, req.TenantID, req.Page, req.PageSize, filters)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list sitemaps: %w", err)
	}

	// Convert to response format
	responses := make([]*models.SEOSitemapResponse, len(sitemaps))
	for i, sitemap := range sitemaps {
		responses[i] = sitemap.ToResponse()
	}

	return responses, total, nil
}

// GetByWebsite retrieves all sitemaps for a website
func (s *seoSitemapService) GetByWebsite(ctx context.Context, websiteID, tenantID uint) ([]*models.SEOSitemapResponse, error) {
	sitemaps, err := s.repo.GetByWebsite(ctx, websiteID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get sitemaps by website: %w", err)
	}

	responses := make([]*models.SEOSitemapResponse, len(sitemaps))
	for i, sitemap := range sitemaps {
		responses[i] = sitemap.ToResponse()
	}

	return responses, nil
}

// GetByType retrieves sitemaps by type
func (s *seoSitemapService) GetByType(ctx context.Context, websiteID, tenantID uint, sitemapType models.SitemapType) ([]*models.SEOSitemapResponse, error) {
	sitemaps, err := s.repo.GetByType(ctx, websiteID, tenantID, sitemapType)
	if err != nil {
		return nil, fmt.Errorf("failed to get sitemaps by type: %w", err)
	}

	responses := make([]*models.SEOSitemapResponse, len(sitemaps))
	for i, sitemap := range sitemaps {
		responses[i] = sitemap.ToResponse()
	}

	return responses, nil
}

// GenerateSitemap generates a sitemap of specific type
func (s *seoSitemapService) GenerateSitemap(ctx context.Context, websiteID, tenantID uint, sitemapType models.SitemapType) (*models.SEOSitemapResponse, error) {
	// Discover content based on type
	var entries []SitemapEntry
	var err error

	switch sitemapType {
	case models.SitemapTypePages:
		entries, err = s.DiscoverPages(ctx, websiteID, tenantID)
	case models.SitemapTypeBlog:
		entries, err = s.DiscoverBlogPosts(ctx, websiteID, tenantID)
	case models.SitemapTypeStatic:
		entries, err = s.DiscoverStaticPages(ctx, websiteID, tenantID)
	default:
		return nil, fmt.Errorf("unsupported sitemap type: %s", sitemapType)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to discover content: %w", err)
	}

	// Generate XML
	sitemapXML := &SitemapXML{
		Xmlns: "http://www.sitemaps.org/schemas/sitemap/0.9",
		URLs:  entries,
	}

	xmlContent, err := xml.MarshalIndent(sitemapXML, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal sitemap XML: %w", err)
	}

	// Create or update sitemap
	filename := fmt.Sprintf("sitemap_%s.xml", strings.ToLower(string(sitemapType)))
	sitemap := &models.SEOSitemap{
		WebsiteID: websiteID,
		TenantID:  tenantID,

		Type:        sitemapType,
		Name:        fmt.Sprintf("%s Sitemap", sitemapType),
		Description: utils.StringPtr(fmt.Sprintf("Generated sitemap for %s", sitemapType)),
		Filename:    filename,

		URLCount:   len(entries),
		FileSize:   len(xmlContent),
		Compressed: false,

		Content: string(xmlContent),

		Status: models.SitemapStatusActive,
	}

	// Check if sitemap already exists
	existing, err := s.repo.GetByTypeAndWebsite(ctx, websiteID, tenantID, sitemapType)
	if err == nil && existing != nil {
		// Update existing sitemap
		existing.URLCount = sitemap.URLCount
		existing.FileSize = sitemap.FileSize
		existing.Content = sitemap.Content
		existing.GeneratedAt = time.Now()

		if err := s.repo.Update(ctx, existing); err != nil {
			return nil, fmt.Errorf("failed to update existing sitemap: %w", err)
		}

		s.logger.Info("Updated existing sitemap", "id", existing.ID, "type", sitemapType)
		return existing.ToResponse(), nil
	}

	// Create new sitemap
	if err := s.repo.Create(ctx, sitemap); err != nil {
		return nil, fmt.Errorf("failed to create sitemap: %w", err)
	}

	s.logger.Info("Generated new sitemap", "id", sitemap.ID, "type", sitemapType, "urls", len(entries))
	return sitemap.ToResponse(), nil
}

// GenerateAllSitemaps generates all types of sitemaps
func (s *seoSitemapService) GenerateAllSitemaps(ctx context.Context, websiteID, tenantID uint) ([]*models.SEOSitemapResponse, error) {
	var responses []*models.SEOSitemapResponse

	// Generate each type of sitemap
	types := []models.SitemapType{
		models.SitemapTypePages,
		models.SitemapTypeBlog,
		models.SitemapTypeStatic,
	}

	for _, sitemapType := range types {
		result, err := s.GenerateSitemap(ctx, websiteID, tenantID, sitemapType)
		if err != nil {
			s.logger.Error("Failed to generate sitemap", "type", sitemapType, "error", err)
			continue
		}
		responses = append(responses, result)
	}

	s.logger.Info("Generated all sitemaps", "website_id", websiteID, "count", len(responses))
	return responses, nil
}

// RefreshSitemap refreshes an existing sitemap
func (s *seoSitemapService) RefreshSitemap(ctx context.Context, id uint) (*models.SEOSitemapResponse, error) {
	sitemap, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get sitemap: %w", err)
	}

	// Regenerate the sitemap
	return s.GenerateSitemap(ctx, sitemap.WebsiteID, sitemap.TenantID, sitemap.Type)
}

// GetSitemapXML returns the XML content of a sitemap
func (s *seoSitemapService) GetSitemapXML(ctx context.Context, id uint) (string, error) {
	sitemap, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return "", fmt.Errorf("failed to get sitemap: %w", err)
	}

	if sitemap.Content == "" {
		return "", fmt.Errorf("sitemap has no content")
	}

	return sitemap.Content, nil
}

// GetSitemapIndexXML returns the sitemap index XML
func (s *seoSitemapService) GetSitemapIndexXML(ctx context.Context, websiteID, tenantID uint) (string, error) {
	sitemaps, err := s.repo.GetByWebsite(ctx, websiteID, tenantID)
	if err != nil {
		return "", fmt.Errorf("failed to get sitemaps: %w", err)
	}

	var entries []SitemapIndexEntry
	for _, sitemap := range sitemaps {
		if sitemap.Status == models.SitemapStatusActive {
			entry := SitemapIndexEntry{
				URL:     fmt.Sprintf("/sitemap/%s", sitemap.Filename),
				LastMod: &sitemap.GeneratedAt,
			}
			entries = append(entries, entry)
		}
	}

	indexXML := &SitemapIndexXML{
		Xmlns:    "http://www.sitemaps.org/schemas/sitemap/0.9",
		Sitemaps: entries,
	}

	xmlContent, err := xml.MarshalIndent(indexXML, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal sitemap index XML: %w", err)
	}

	return string(xmlContent), nil
}

// ServeSitemap serves a sitemap by filename
func (s *seoSitemapService) ServeSitemap(ctx context.Context, websiteID, tenantID uint, filename string) (string, error) {
	sitemap, err := s.repo.GetByFilename(ctx, websiteID, tenantID, filename)
	if err != nil {
		return "", fmt.Errorf("sitemap not found: %w", err)
	}

	if sitemap.Status != models.SitemapStatusActive {
		return "", fmt.Errorf("sitemap is not active")
	}

	return sitemap.Content, nil
}

// DiscoverPages discovers all pages for sitemap generation
func (s *seoSitemapService) DiscoverPages(ctx context.Context, websiteID, tenantID uint) ([]SitemapEntry, error) {
	// This would integrate with the website/page management system
	// For now, return empty list
	return []SitemapEntry{}, nil
}

// DiscoverBlogPosts discovers blog posts for sitemap generation
func (s *seoSitemapService) DiscoverBlogPosts(ctx context.Context, websiteID, tenantID uint) ([]SitemapEntry, error) {
	// This would integrate with the blog module
	// For now, return empty list
	return []SitemapEntry{}, nil
}

// DiscoverStaticPages discovers static pages for sitemap generation
func (s *seoSitemapService) DiscoverStaticPages(ctx context.Context, websiteID, tenantID uint) ([]SitemapEntry, error) {
	// This would discover static pages
	// For now, return empty list
	return []SitemapEntry{}, nil
}

// ValidateSitemap validates a sitemap
func (s *seoSitemapService) ValidateSitemap(ctx context.Context, id uint) error {
	sitemap, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get sitemap: %w", err)
	}

	return s.ValidateXML(ctx, sitemap.Content)
}

// ValidateXML validates sitemap XML content
func (s *seoSitemapService) ValidateXML(ctx context.Context, xmlContent string) error {
	if xmlContent == "" {
		return fmt.Errorf("XML content is empty")
	}

	// Try to parse the XML
	var sitemapXML SitemapXML
	if err := xml.Unmarshal([]byte(xmlContent), &sitemapXML); err != nil {
		return fmt.Errorf("invalid XML format: %w", err)
	}

	// Basic validation
	if len(sitemapXML.URLs) == 0 {
		return fmt.Errorf("sitemap contains no URLs")
	}

	// Validate each URL
	for i, url := range sitemapXML.URLs {
		if url.URL == "" {
			return fmt.Errorf("URL %d is empty", i)
		}

		if !strings.HasPrefix(url.URL, "http://") && !strings.HasPrefix(url.URL, "https://") {
			return fmt.Errorf("URL %d is not a valid URL: %s", i, url.URL)
		}
	}

	return nil
}

// GetStats retrieves sitemap statistics
func (s *seoSitemapService) GetStats(ctx context.Context, websiteID, tenantID uint) (*models.SEOSitemapStatsResponse, error) {
	stats, err := s.repo.GetStats(ctx, websiteID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get sitemap stats: %w", err)
	}

	return stats, nil
}

// CleanupOldSitemaps removes old sitemaps
func (s *seoSitemapService) CleanupOldSitemaps(ctx context.Context, days int) (int, error) {
	if days <= 0 {
		days = 30 // Default to 30 days
	}

	before := time.Now().AddDate(0, 0, -days)
	count, err := s.repo.CleanupOldSitemaps(ctx, before)
	if err != nil {
		s.logger.Error("Failed to cleanup old sitemaps", "error", err)
		return 0, fmt.Errorf("failed to cleanup old sitemaps: %w", err)
	}

	s.logger.Info("Cleaned up old sitemaps", "count", count, "days", days)
	return count, nil
}

// CompressSitemaps compresses large sitemaps
func (s *seoSitemapService) CompressSitemaps(ctx context.Context, websiteID, tenantID uint) error {
	sitemaps, err := s.repo.GetByWebsite(ctx, websiteID, tenantID)
	if err != nil {
		return fmt.Errorf("failed to get sitemaps: %w", err)
	}

	var compressed int
	for _, sitemap := range sitemaps {
		// Compress if sitemap is large and not already compressed
		if sitemap.FileSize > 10*1024*1024 && !sitemap.Compressed { // 10MB threshold
			// TODO: Implement compression logic
			sitemap.Compressed = true
			if err := s.repo.Update(ctx, sitemap); err != nil {
				s.logger.Error("Failed to update sitemap compression", "id", sitemap.ID, "error", err)
				continue
			}
			compressed++
		}
	}

	s.logger.Info("Compressed sitemaps", "count", compressed)
	return nil
}

// validateSitemapRequest validates a sitemap creation request
func (s *seoSitemapService) validateSitemapRequest(req *models.SEOSitemapCreateRequest) error {
	if req.WebsiteID == 0 {
		return fmt.Errorf("website_id is required")
	}
	if req.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}
	if req.Type == "" {
		return fmt.Errorf("type is required")
	}
	if req.Name == "" {
		return fmt.Errorf("name is required")
	}
	if req.Filename == "" {
		return fmt.Errorf("filename is required")
	}

	// Validate sitemap type
	validTypes := []models.SitemapType{
		models.SitemapTypePages,
		models.SitemapTypeBlog,
		models.SitemapTypeStatic,
		models.SitemapTypeIndex,
	}

	valid := false
	for _, validType := range validTypes {
		if req.Type == validType {
			valid = true
			break
		}
	}

	if !valid {
		return fmt.Errorf("invalid sitemap type: %s", req.Type)
	}

	return nil
}
