package media

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/handlers"
	tenantRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	tenantServices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

func RegisterRoutes(
	router *gin.RouterGroup,
	db *gorm.DB,
	fileHandler *handlers.MediaFileHandler,
	folderHandler *handlers.MediaFolderHandler,
	logger utils.Logger,
) {
	// Initialize tenant service for middleware
	tenantRepo := tenantRepositories.NewMySQLTenantRepository(db)
	planRepo := tenantRepositories.NewMySQLTenantPlanRepository(db)
	tenantService := tenantServices.NewTenantService(tenantRepo, planRepo)

	// Get JWT service for authentication
	_, jwtService, _, err := auth.GetAuthServiceDependencies(db, logger)
	if err != nil {
		logger.WithError(err).Error("Failed to get auth dependencies for media routes")
		panic("Failed to initialize JWT service for media routes")
	}

	// Create main media group
	media := router.Group("/media")

	// Protected routes group (require authentication and tenant isolation)
	protectedRoutes := media.Group("")
	protectedRoutes.Use(middleware.JWTAuthMiddleware(*jwtService))
	protectedRoutes.Use(middleware.RequireAuthentication())
	protectedRoutes.Use(middleware.TenantIsolationMiddleware(tenantService))
	protectedRoutes.Use(middleware.GinWebsiteMiddleware())
	{
		// File routes
		files := protectedRoutes.Group("/files")
		{
			// Upload endpoints with rate limiting
			upload := files.Group("")
			upload.Use(middleware.GinRateLimitMiddleware())
			{
				upload.POST("/upload", fileHandler.UploadFile)
				upload.POST("/upload/init", fileHandler.InitChunkedUpload)
				upload.POST("/upload/chunk", fileHandler.UploadChunk)
				upload.POST("/upload/complete", fileHandler.CompleteChunkedUpload)
			}

			// CRUD operations
			files.GET("", fileHandler.ListFiles)
			files.GET("/search", fileHandler.SearchFiles)
			files.GET("/:id", fileHandler.GetFile)
			files.PUT("/:id", fileHandler.UpdateFile)
			files.DELETE("/:id", fileHandler.DeleteFile)

			// File operations
			files.POST("/move", fileHandler.MoveFiles)
			files.POST("/:id/copy", fileHandler.CopyFile)
			files.POST("/:id/rename", fileHandler.RenameFile)

			// Tag operations
			files.POST("/:id/tags", fileHandler.AddTags)
			files.DELETE("/:id/tags", fileHandler.RemoveTags)
			files.PUT("/:id/tags", fileHandler.SetTags)

			// Bulk operations
			files.POST("/bulk/update", fileHandler.BulkUpdateMetadata)
			files.POST("/bulk/visibility", fileHandler.BulkChangeVisibility)
			files.DELETE("/bulk", fileHandler.DeleteFiles)

			// Statistics
			files.GET("/stats/storage", fileHandler.GetStorageStats)
			files.GET("/stats/types", fileHandler.GetFileTypeStats)

			// Download/Stream (public access possible)
			files.GET("/:id/download", fileHandler.DownloadFile)
			files.GET("/:id/stream", fileHandler.StreamFile)
		}

		// Folder routes
		folders := protectedRoutes.Group("/folders")
		{
			folders.GET("", folderHandler.ListFolders)
			folders.POST("", folderHandler.CreateFolder)
			folders.GET("/tree", folderHandler.GetFolderTree)
			folders.GET("/:id", folderHandler.GetFolder)
			folders.PUT("/:id", folderHandler.UpdateFolder)
			folders.DELETE("/:id", folderHandler.DeleteFolder)
			folders.POST("/:id/move", folderHandler.MoveFolder)
			folders.GET("/:id/stats", folderHandler.GetFolderStats)

			// System folders
			folders.POST("/system/init", folderHandler.CreateSystemFolders)
			folders.GET("/system", folderHandler.GetSystemFolders)
		}

		// Tag routes
		tags := protectedRoutes.Group("/tags")
		{
			tags.GET("", fileHandler.ListTags)
			tags.POST("", fileHandler.CreateTag)
			tags.GET("/popular", fileHandler.GetPopularTags)
			tags.GET("/search", fileHandler.SearchTags)
			tags.GET("/:id", fileHandler.GetTag)
			tags.PUT("/:id", fileHandler.UpdateTag)
			tags.DELETE("/:id", fileHandler.DeleteTag)
			tags.GET("/:id/files", fileHandler.GetFilesByTag)
			tags.POST("/cleanup", fileHandler.CleanupUnusedTags)
		}
	}

	// Public routes (for CDN/direct access)
	public := router.Group("/media/public")
	{
		public.GET("/files/:slug", fileHandler.GetFileBySlug)
		public.GET("/download/:slug", fileHandler.DownloadFileBySlug)
	}
}