package media

import (
	"os"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/gorm"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

type Module struct {
	// Repositories
	fileRepo      repositories.MediaFileRepository
	folderRepo    repositories.MediaFolderRepository
	tagRepo       repositories.MediaTagRepository
	thumbnailRepo repositories.MediaThumbnailRepository
	sessionRepo   repositories.UploadSessionRepository

	// Services
	fileService      services.MediaFileService
	folderService    services.MediaFolderService
	tagService       services.MediaTagService
	thumbnailService services.MediaThumbnailService
	storageService   services.StorageService

	// Handlers
	fileHandler   *handlers.MediaFileHandler
	folderHandler *handlers.MediaFolderHandler

	// Dependencies
	db        *gorm.DB
	logger    utils.Logger
	validator validator.Validator
}

func NewModule(
	db *gorm.DB,
	logger utils.Logger,
	validator validator.Validator,
) *Module {
	// Initialize repositories
	fileRepo := mysql.NewMediaFileRepository(db)
	folderRepo := mysql.NewMediaFolderRepository(db)
	tagRepo := mysql.NewMediaTagRepository(db)
	thumbnailRepo := mysql.NewMediaThumbnailRepository(db)
	sessionRepo := mysql.NewUploadSessionRepository(db)

	// Load storage configuration
	storageConfig := loadStorageConfig()

	// Initialize storage service
	storageService, err := services.NewStorageService(storageConfig)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize storage service")
	}

	// Initialize services (using stub implementations for now)
	fileService := services.NewMediaFileService(
		fileRepo,
		folderRepo,
		tagRepo,
		thumbnailRepo,
		sessionRepo,
		storageService,
		logger,
		storageConfig,
	)

	folderService := services.NewMediaFolderService(
		folderRepo,
		fileRepo,
		logger,
	)

	tagService := services.NewMediaTagService(
		tagRepo,
		fileRepo,
		logger,
	)

	thumbnailService := services.NewMediaThumbnailService(
		thumbnailRepo,
		fileRepo,
		storageService,
		logger,
		loadThumbnailConfig(),
	)

	// Initialize handlers
	fileHandler := handlers.NewMediaFileHandler(
		fileService,
		validator,
		logger,
	)

	folderHandler := handlers.NewMediaFolderHandler(
		folderService,
		validator,
		logger,
	)

	return &Module{
		// Repositories
		fileRepo:      fileRepo,
		folderRepo:    folderRepo,
		tagRepo:       tagRepo,
		thumbnailRepo: thumbnailRepo,
		sessionRepo:   sessionRepo,

		// Services
		fileService:      fileService,
		folderService:    folderService,
		tagService:       tagService,
		thumbnailService: thumbnailService,
		storageService:   storageService,

		// Handlers
		fileHandler:   fileHandler,
		folderHandler: folderHandler,

		// Dependencies
		db:        db,
		logger:    logger,
		validator: validator,
	}
}

func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	RegisterRoutes(
		router,
		m.db,
		m.fileHandler,
		m.folderHandler,
		m.logger,
	)
}

func loadStorageConfig() *services.StorageConfig {
	// Initialize viper to read environment variables
	viper.AutomaticEnv()
	viper.SetEnvPrefix("")
	
	// First try environment variables directly
	storageType := os.Getenv("MEDIA_STORAGE_TYPE")
	if storageType == "" {
		storageType = viper.GetString("media.storage_type")
	}
	if storageType == "" {
		storageType = "local"
	}

	// Load max file size
	maxFileSize := int64(0)
	if envSize := os.Getenv("MEDIA_MAX_FILE_SIZE"); envSize != "" {
		if size, err := strconv.ParseInt(envSize, 10, 64); err == nil {
			maxFileSize = size
		}
	}
	if maxFileSize == 0 {
		maxFileSize = viper.GetInt64("media.max_file_size")
	}

	// Load CDN base URL
	cdnBaseURL := os.Getenv("MEDIA_CDN_BASE_URL")
	if cdnBaseURL == "" {
		cdnBaseURL = viper.GetString("media.cdn_base_url")
	}

	config := &services.StorageConfig{
		Type:             storageType,
		MaxFileSize:      maxFileSize,
		AllowedMimeTypes: viper.GetStringSlice("media.allowed_mime_types"),
		CDNBaseURL:       cdnBaseURL,
	}

	// Set default max file size if not configured
	if config.MaxFileSize == 0 {
		config.MaxFileSize = 100 * 1024 * 1024 // 100MB default
	}

	// Set default CDN base URL if not configured
	if config.CDNBaseURL == "" {
		config.CDNBaseURL = viper.GetString("app.base_url") + "/media/public"
	}

	// Load storage-specific configuration
	switch storageType {
	case "local":
		config.LocalPath = viper.GetString("media.local.path")
		if config.LocalPath == "" {
			config.LocalPath = "./uploads"
		}
		// Ensure local storage directory exists
		if err := os.MkdirAll(config.LocalPath, 0755); err != nil {
			panic("Failed to create local storage directory: " + err.Error())
		}

	case "minio":
		// Load MinIO configuration from environment variables
		config.MinioEndpoint = os.Getenv("MEDIA_MINIO_ENDPOINT")
		if config.MinioEndpoint == "" {
			config.MinioEndpoint = viper.GetString("media.minio.endpoint")
		}
		
		config.MinioBucket = os.Getenv("MEDIA_MINIO_BUCKET")
		if config.MinioBucket == "" {
			config.MinioBucket = viper.GetString("media.minio.bucket")
		}
		
		config.MinioAccessKey = os.Getenv("MEDIA_MINIO_ACCESS_KEY")
		if config.MinioAccessKey == "" {
			config.MinioAccessKey = viper.GetString("media.minio.access_key")
		}
		
		config.MinioSecretKey = os.Getenv("MEDIA_MINIO_SECRET_KEY")
		if config.MinioSecretKey == "" {
			config.MinioSecretKey = viper.GetString("media.minio.secret_key")
		}
		
		if sslStr := os.Getenv("MEDIA_MINIO_USE_SSL"); sslStr != "" {
			config.MinioUseSSL = sslStr == "true"
		} else {
			config.MinioUseSSL = viper.GetBool("media.minio.use_ssl")
		}

		if config.MinioEndpoint == "" || config.MinioBucket == "" {
			panic("MinIO configuration is incomplete")
		}
	}

	return config
}

func loadThumbnailConfig() *services.ThumbnailConfig {
	return &services.ThumbnailConfig{
		Sizes: map[models.ThumbnailSize]services.ThumbnailSizeConfig{
			models.ThumbnailSizeSmall: {
				Width:   150,
				Height:  150,
				Quality: 80,
				Format:  "jpeg",
			},
			models.ThumbnailSizeMedium: {
				Width:   300,
				Height:  300,
				Quality: 85,
				Format:  "jpeg",
			},
			models.ThumbnailSizeLarge: {
				Width:   600,
				Height:  600,
				Quality: 90,
				Format:  "jpeg",
			},
		},
	}
}

