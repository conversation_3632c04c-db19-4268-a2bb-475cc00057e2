package services

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/dto"
)

type MediaTagService interface {
	// CRUD operations
	CreateTag(ctx context.Context, tenantID uint, req *dto.MediaTagCreateRequest) (*dto.MediaTagResponse, error)
	GetTagByID(ctx context.Context, id uint) (*dto.MediaTagResponse, error)
	GetTagByName(ctx context.Context, tenantID uint, name string) (*dto.MediaTagResponse, error)
	UpdateTag(ctx context.Context, id uint, req *dto.MediaTagUpdateRequest) (*dto.MediaTagResponse, error)
	DeleteTag(ctx context.Context, id uint) error
	
	// Batch operations
	CreateTags(ctx context.Context, tenantID uint, req *dto.MediaTagBulkRequest) ([]*dto.MediaTagResponse, error)
	GetOrCreateTags(ctx context.Context, tenantID uint, names []string) ([]*dto.MediaTagResponse, error)
	DeleteUnusedTags(ctx context.Context, tenantID uint) (int64, error)
	
	// List and search
	ListTags(ctx context.Context, filter *dto.MediaTagFilter) ([]*dto.MediaTagResponse, error)
	SearchTags(ctx context.Context, tenantID uint, query string) ([]*dto.MediaTagResponse, error)
	GetPopularTags(ctx context.Context, tenantID uint, limit int) ([]*dto.MediaTagResponse, error)
	
	// File associations
	GetTagsByFileID(ctx context.Context, fileID uint) ([]*dto.MediaTagResponse, error)
	GetFilesByTagID(ctx context.Context, tagID uint) ([]*dto.MediaFileResponse, error)
	
	// Usage management
	RecalculateUsageCounts(ctx context.Context, tenantID uint) error
}

// Service errors
var (
	ErrTagNotFound = NewServiceError("TAG_NOT_FOUND", "Tag not found")
	ErrTagExists   = NewServiceError("TAG_EXISTS", "Tag already exists")
	ErrTagInUse    = NewServiceError("TAG_IN_USE", "Tag is in use")
)