package services

import (
	"context"
	"io"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/dto"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type MediaFileService interface {
	// Upload operations
	UploadFile(ctx context.Context, tenantID, websiteID, userID uint, req *dto.MediaFileUploadRequest) (*dto.MediaFileResponse, error)
	InitChunkedUpload(ctx context.Context, tenantID, userID uint, req *dto.ChunkedUploadInitRequest) (*dto.UploadSessionResponse, error)
	UploadChunk(ctx context.Context, req *dto.ChunkedUploadRequest) (*dto.UploadSessionResponse, error)
	CompleteChunkedUpload(ctx context.Context, sessionID string) (*dto.MediaFileResponse, error)

	// CRUD operations
	CreateFile(ctx context.Context, userID uint, req *dto.MediaFileCreateRequest) (*dto.MediaFileResponse, error)
	GetFileByID(ctx context.Context, id uint) (*dto.MediaFileResponse, error)
	GetFileBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*dto.MediaFileResponse, error)
	UpdateFile(ctx context.Context, id uint, req *dto.MediaFileUpdateRequest) (*dto.MediaFileResponse, error)
	DeleteFile(ctx context.Context, id uint) error
	DeleteFiles(ctx context.Context, ids []uint) error

	// List and search
	ListFiles(ctx context.Context, filter *dto.MediaFileFilter, cursor *pagination.CursorRequest) ([]*dto.MediaFileResponse, *pagination.CursorResponse, error)
	SearchFiles(ctx context.Context, tenantID, websiteID uint, query string, cursor *pagination.CursorRequest) ([]*dto.MediaFileResponse, *pagination.CursorResponse, error)

	// File operations
	MoveFiles(ctx context.Context, fileIDs []uint, folderID *uint) error
	CopyFile(ctx context.Context, id uint, folderID *uint) (*dto.MediaFileResponse, error)
	RenameFile(ctx context.Context, id uint, newName string) (*dto.MediaFileResponse, error)
	
	// Tag operations
	AddTags(ctx context.Context, fileID uint, tagNames []string) error
	RemoveTags(ctx context.Context, fileID uint, tagNames []string) error
	SetTags(ctx context.Context, fileID uint, tagNames []string) error

	// Access operations
	GetFileStream(ctx context.Context, id uint) (io.ReadCloser, *FileMetadata, error)
	GetFileURL(ctx context.Context, id uint, expiry int) (string, error)
	TrackFileView(ctx context.Context, id uint) error
	TrackFileDownload(ctx context.Context, id uint) error

	// Bulk operations
	BulkUpdateMetadata(ctx context.Context, updates map[uint]*dto.MediaFileUpdateRequest) error
	BulkChangeVisibility(ctx context.Context, fileIDs []uint, visibility string) error

	// Statistics
	GetStorageStats(ctx context.Context, tenantID, websiteID uint) (*dto.StorageStatsResponse, error)
	GetFileTypeStats(ctx context.Context, tenantID, websiteID uint) (*dto.FileTypeStatsResponse, error)
}

type FileMetadata struct {
	ID          uint
	Filename    string
	MimeType    string
	Size        int64
	ContentType string
}

type StorageConfig struct {
	Type            string
	LocalPath       string
	MinioEndpoint   string
	MinioBucket     string
	MinioAccessKey  string
	MinioSecretKey  string
	MinioUseSSL     bool
	CDNBaseURL      string
	MaxFileSize     int64
	AllowedMimeTypes []string
}

// Service errors
var (
	ErrFileNotFound       = NewServiceError("FILE_NOT_FOUND", "File not found")
	ErrFileExists         = NewServiceError("FILE_EXISTS", "File already exists")
	ErrInvalidFileType    = NewServiceError("INVALID_FILE_TYPE", "Invalid file type")
	ErrFileSizeExceeded   = NewServiceError("FILE_SIZE_EXCEEDED", "File size exceeds limit")
	ErrUploadFailed       = NewServiceError("UPLOAD_FAILED", "File upload failed")
	ErrStorageError       = NewServiceError("STORAGE_ERROR", "Storage operation failed")
	ErrInvalidSession     = NewServiceError("INVALID_SESSION", "Invalid upload session")
	ErrSessionExpired     = NewServiceError("SESSION_EXPIRED", "Upload session expired")
	ErrChunkMismatch      = NewServiceError("CHUNK_MISMATCH", "Chunk data mismatch")
)