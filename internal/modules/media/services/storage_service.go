package services

import (
	"context"
	"io"
	"mime/multipart"
)

type StorageService interface {
	// Storage operations
	Store(ctx context.Context, file multipart.File, path string, contentType string) (string, error)
	StoreFromReader(ctx context.Context, reader io.Reader, path string, contentType string, size int64) (string, error)
	StoreFromPath(ctx context.Context, sourcePath, destPath string, contentType string) (string, error)
	
	// Retrieval operations
	Get(ctx context.Context, path string) (io.ReadCloser, error)
	GetWithMetadata(ctx context.Context, path string) (io.ReadCloser, *ObjectMetadata, error)
	Exists(ctx context.Context, path string) (bool, error)
	
	// URL operations
	GetPublicURL(path string) string
	GetSignedURL(ctx context.Context, path string, expiry int) (string, error)
	
	// Management operations
	Delete(ctx context.Context, path string) error
	DeleteBatch(ctx context.Context, paths []string) error
	Move(ctx context.Context, sourcePath, destPath string) error
	Copy(ctx context.Context, sourcePath, destPath string) error
	
	// Directory operations
	List(ctx context.Context, prefix string) ([]string, error)
	ListWithMetadata(ctx context.Context, prefix string) ([]*ObjectMetadata, error)
	
	// Metadata operations
	GetMetadata(ctx context.Context, path string) (*ObjectMetadata, error)
	SetMetadata(ctx context.Context, path string, metadata map[string]string) error
}

type ObjectMetadata struct {
	Path         string
	Size         int64
	ContentType  string
	ETag         string
	LastModified int64
	Metadata     map[string]string
}

// Factory function
func NewStorageService(config *StorageConfig) (StorageService, error) {
	switch config.Type {
	case "local":
		return NewLocalStorageService(config.LocalPath, config.CDNBaseURL), nil
	case "minio":
		return NewMinioStorageService(
			config.MinioEndpoint,
			config.MinioBucket,
			config.MinioAccessKey,
			config.MinioSecretKey,
			config.MinioUseSSL,
			config.CDNBaseURL,
		)
	default:
		return nil, NewServiceError("INVALID_STORAGE_TYPE", "Invalid storage type")
	}
}