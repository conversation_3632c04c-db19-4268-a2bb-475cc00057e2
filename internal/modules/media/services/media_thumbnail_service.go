package services

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

type MediaThumbnailService interface {
	// Thumbnail generation
	GenerateThumbnails(ctx context.Context, fileID uint) error
	GenerateThumbnail(ctx context.Context, fileID uint, size models.ThumbnailSize) error
	RegenerateThumbnails(ctx context.Context, fileID uint) error
	
	// Retrieval
	GetThumbnail(ctx context.Context, fileID uint, size models.ThumbnailSize) (*ThumbnailInfo, error)
	GetThumbnails(ctx context.Context, fileID uint) ([]*ThumbnailInfo, error)
	
	// Batch operations
	GenerateBatchThumbnails(ctx context.Context, fileIDs []uint) error
	ProcessPendingThumbnails(ctx context.Context, limit int) error
	RetryFailedThumbnails(ctx context.Context, limit int) error
	
	// Cleanup
	DeleteThumbnails(ctx context.Context, fileID uint) error
	DeleteOrphanedThumbnails(ctx context.Context) (int64, error)
	DeleteOldThumbnails(ctx context.Context, days int) (int64, error)
}

type ThumbnailInfo struct {
	ID        uint
	Size      string
	Width     int
	Height    int
	Format    string
	PublicURL string
	FileSize  int64
	Status    string
}

type ThumbnailConfig struct {
	Sizes map[models.ThumbnailSize]ThumbnailSizeConfig
}

type ThumbnailSizeConfig struct {
	Width   int
	Height  int
	Quality int
	Format  string
}

// Service errors
var (
	ErrThumbnailNotFound     = NewServiceError("THUMBNAIL_NOT_FOUND", "Thumbnail not found")
	ErrThumbnailGeneration   = NewServiceError("THUMBNAIL_GENERATION_FAILED", "Thumbnail generation failed")
	ErrUnsupportedFileType   = NewServiceError("UNSUPPORTED_FILE_TYPE", "File type not supported for thumbnails")
)