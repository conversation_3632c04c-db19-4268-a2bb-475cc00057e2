package services

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/dto"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type MediaFolderService interface {
	// CRUD operations
	CreateFolder(ctx context.Context, req *dto.MediaFolderCreateRequest) (*dto.MediaFolderResponse, error)
	GetFolderByID(ctx context.Context, id uint) (*dto.MediaFolderResponse, error)
	GetFolderByPath(ctx context.Context, tenantID, websiteID uint, path string) (*dto.MediaFolderResponse, error)
	UpdateFolder(ctx context.Context, id uint, req *dto.MediaFolderUpdateRequest) (*dto.MediaFolderResponse, error)
	DeleteFolder(ctx context.Context, id uint) error
	
	// Tree operations
	GetFolderTree(ctx context.Context, tenantID, websiteID uint, maxDepth int) ([]*dto.MediaFolderTreeResponse, error)
	GetFolderChildren(ctx context.Context, folderID uint) ([]*dto.MediaFolderResponse, error)
	GetFolderAncestors(ctx context.Context, folderID uint) ([]*dto.MediaFolderResponse, error)
	MoveFolder(ctx context.Context, folderID uint, req *dto.MediaFolderMoveRequest) (*dto.MediaFolderResponse, error)
	
	// List and search
	ListFolders(ctx context.Context, filter *dto.MediaFolderFilter, cursor *pagination.CursorRequest) ([]*dto.MediaFolderResponse, *pagination.CursorResponse, error)
	SearchFolders(ctx context.Context, tenantID, websiteID uint, query string) ([]*dto.MediaFolderResponse, error)
	
	// System folders
	CreateSystemFolders(ctx context.Context, tenantID, websiteID uint) error
	GetSystemFolders(ctx context.Context, tenantID, websiteID uint) ([]*dto.MediaFolderResponse, error)
	
	// Statistics
	GetFolderStats(ctx context.Context, folderID uint) (*dto.FolderStatsResponse, error)
}

// Service errors
var (
	ErrFolderNotFound     = NewServiceError("FOLDER_NOT_FOUND", "Folder not found")
	ErrFolderExists       = NewServiceError("FOLDER_EXISTS", "Folder already exists")
	ErrFolderNotEmpty     = NewServiceError("FOLDER_NOT_EMPTY", "Folder is not empty")
	ErrCircularReference  = NewServiceError("CIRCULAR_REFERENCE", "Circular reference detected")
	ErrSystemFolder       = NewServiceError("SYSTEM_FOLDER", "Cannot modify system folder")
	ErrInvalidPath        = NewServiceError("INVALID_PATH", "Invalid folder path")
)