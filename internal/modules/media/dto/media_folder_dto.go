package dto

import (
	"encoding/json"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

// Create/Update DTOs
type MediaFolderCreateRequest struct {
	TenantID    uint                   `json:"tenant_id" validate:"required"`
	WebsiteID   uint                   `json:"website_id" validate:"required"`
	ParentID    *uint                  `json:"parent_id"`
	Name        string                 `json:"name" validate:"required,max=255"`
	Description string                 `json:"description"`
	Color       string                 `json:"color" validate:"omitempty,hexcolor"`
	Icon        string                 `json:"icon"`
	SortOrder   int                    `json:"sort_order"`
	Settings    map[string]interface{} `json:"settings"`
}

type MediaFolderUpdateRequest struct {
	Name        string                 `json:"name" validate:"omitempty,max=255"`
	Description string                 `json:"description"`
	Color       string                 `json:"color" validate:"omitempty,hexcolor"`
	Icon        string                 `json:"icon"`
	SortOrder   *int                   `json:"sort_order"`
	Settings    map[string]interface{} `json:"settings"`
}

type MediaFolderMoveRequest struct {
	ParentID *uint `json:"parent_id"`
}

// Response DTOs
type MediaFolderResponse struct {
	ID          uint                   `json:"id"`
	TenantID    uint                   `json:"tenant_id"`
	WebsiteID   uint                   `json:"website_id"`
	ParentID    *uint                  `json:"parent_id"`
	Name        string                 `json:"name"`
	Slug        string                 `json:"slug"`
	Path        string                 `json:"path"`
	FullPath    string                 `json:"full_path"`
	Description string                 `json:"description"`
	Color       string                 `json:"color"`
	Icon        string                 `json:"icon"`
	SortOrder   int                    `json:"sort_order"`
	IsSystem    bool                   `json:"is_system"`
	Settings    map[string]interface{} `json:"settings"`
	Status      string                 `json:"status"`
	FileCount   int                    `json:"file_count,omitempty"`
	TotalSize   int64                  `json:"total_size,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Parent      *MediaFolderResponse   `json:"parent,omitempty"`
	Children    []MediaFolderResponse  `json:"children,omitempty"`
}

type MediaFolderTreeResponse struct {
	ID          uint                      `json:"id"`
	Name        string                    `json:"name"`
	Slug        string                    `json:"slug"`
	Path        string                    `json:"path"`
	Icon        string                    `json:"icon"`
	Color       string                    `json:"color"`
	IsSystem    bool                      `json:"is_system"`
	FileCount   int                       `json:"file_count"`
	Children    []MediaFolderTreeResponse `json:"children,omitempty"`
}

// Filter DTOs
type MediaFolderFilter struct {
	TenantID     uint   `json:"tenant_id"`
	WebsiteID    uint   `json:"website_id"`
	ParentID     *uint  `json:"parent_id"`
	SearchQuery  string `json:"search_query"`
	IncludeFiles bool   `json:"include_files"`
	IncludeTree  bool   `json:"include_tree"`
	MaxDepth     int    `json:"max_depth"`
}

// Statistics Response DTOs
type FolderStatsResponse struct {
	FileCount      int64            `json:"file_count"`
	SubfolderCount int64            `json:"subfolder_count"`
	TotalSize      int64            `json:"total_size"`
	FileTypes      map[string]int64 `json:"file_types"`
	FormattedSize  string           `json:"formatted_size"`
}

// Helper functions
func MediaFolderToResponse(folder *models.MediaFolder) *MediaFolderResponse {
	resp := &MediaFolderResponse{
		ID:          folder.ID,
		TenantID:    folder.TenantID,
		WebsiteID:   folder.WebsiteID,
		ParentID:    folder.ParentID,
		Name:        folder.Name,
		Slug:        folder.Slug,
		Path:        folder.Path,
		FullPath:    folder.GetFullPath(),
		Description: folder.Description,
		Color:       folder.Color,
		Icon:        folder.Icon,
		SortOrder:   folder.SortOrder,
		IsSystem:    folder.IsSystem,
		Status:      string(folder.Status),
		CreatedAt:   folder.CreatedAt,
		UpdatedAt:   folder.UpdatedAt,
	}

	// Parse settings
	if folder.Settings != nil {
		var settings map[string]interface{}
		if err := json.Unmarshal(folder.Settings, &settings); err == nil {
			resp.Settings = settings
		}
	}

	// Add parent
	if folder.Parent != nil {
		resp.Parent = MediaFolderToResponse(folder.Parent)
	}

	// Add children
	if len(folder.Children) > 0 {
		resp.Children = make([]MediaFolderResponse, len(folder.Children))
		for i, child := range folder.Children {
			resp.Children[i] = *MediaFolderToResponse(&child)
		}
	}

	// Add file count if files are loaded
	if folder.Files != nil {
		resp.FileCount = len(folder.Files)
		var totalSize int64
		for _, file := range folder.Files {
			totalSize += file.FileSize
		}
		resp.TotalSize = totalSize
	}

	return resp
}

func MediaFolderToTreeResponse(folder *models.MediaFolder) *MediaFolderTreeResponse {
	resp := &MediaFolderTreeResponse{
		ID:       folder.ID,
		Name:     folder.Name,
		Slug:     folder.Slug,
		Path:     folder.Path,
		Icon:     folder.Icon,
		Color:    folder.Color,
		IsSystem: folder.IsSystem,
	}

	// Add file count if files are loaded
	if folder.Files != nil {
		resp.FileCount = len(folder.Files)
	}

	// Add children
	if len(folder.Children) > 0 {
		resp.Children = make([]MediaFolderTreeResponse, len(folder.Children))
		for i, child := range folder.Children {
			resp.Children[i] = *MediaFolderToTreeResponse(&child)
		}
	}

	return resp
}