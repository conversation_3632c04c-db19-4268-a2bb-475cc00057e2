package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

// Create/Update DTOs
type MediaTagCreateRequest struct {
	Name        string `json:"name" validate:"required,max=100"`
	Description string `json:"description"`
	Color       string `json:"color" validate:"omitempty,hexcolor"`
}

type MediaTagUpdateRequest struct {
	Name        string `json:"name" validate:"omitempty,max=100"`
	Description string `json:"description"`
	Color       string `json:"color" validate:"omitempty,hexcolor"`
}

type MediaTagBulkRequest struct {
	Names []string `json:"names" validate:"required,min=1,dive,required,max=100"`
}

// Response DTOs
type MediaTagResponse struct {
	ID          uint      `json:"id"`
	TenantID    uint      `json:"tenant_id"`
	Name        string    `json:"name"`
	Slug        string    `json:"slug"`
	Description string    `json:"description"`
	Color       string    `json:"color"`
	UsageCount  int       `json:"usage_count"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Filter DTOs
type MediaTagFilter struct {
	TenantID    uint   `json:"tenant_id"`
	SearchQuery string `json:"search_query"`
	MinUsage    *int   `json:"min_usage"`
	MaxUsage    *int   `json:"max_usage"`
	SortBy      string `json:"sort_by" validate:"omitempty,oneof=name usage_count created_at"`
	SortOrder   string `json:"sort_order" validate:"omitempty,oneof=asc desc"`
}

// Helper functions
func MediaTagToResponse(tag *models.MediaTag) *MediaTagResponse {
	return &MediaTagResponse{
		ID:          tag.ID,
		TenantID:    tag.TenantID,
		Name:        tag.Name,
		Slug:        tag.Slug,
		Description: tag.Description,
		Color:       tag.Color,
		UsageCount:  tag.UsageCount,
		CreatedAt:   tag.CreatedAt,
		UpdatedAt:   tag.UpdatedAt,
	}
}