package dto

import (
	"encoding/json"
	"mime/multipart"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

// Upload DTOs
type MediaFileUploadRequest struct {
	File         *multipart.FileHeader `form:"file" validate:"required"`
	FolderID     *uint                 `form:"folder_id"`
	Title        string                `form:"title"`
	AltText      string                `form:"alt_text"`
	Description  string                `form:"description"`
	Category     string                `form:"category"`
	Visibility   string                `form:"visibility" validate:"omitempty,oneof=public private shared"`
	StorageType  string                `form:"storage_type" validate:"omitempty,oneof=local minio s3 gcs"`
	Tags         []string              `form:"tags"`
}

type ChunkedUploadInitRequest struct {
	Filename    string                `json:"filename" validate:"required"`
	MimeType    string                `json:"mime_type" validate:"required"`
	TotalSize   int64                 `json:"total_size" validate:"required,min=1"`
	ChunkSize   int                   `json:"chunk_size" validate:"required,min=1024"`
	TotalChunks int                   `json:"total_chunks" validate:"required,min=1"`
	FolderID    *uint                 `json:"folder_id"`
	Metadata    map[string]interface{} `json:"metadata"`
}

type ChunkedUploadRequest struct {
	SessionID   string                `form:"session_id" validate:"required"`
	ChunkNumber int                   `form:"chunk_number" validate:"required,min=0"`
	Chunk       *multipart.FileHeader `form:"chunk" validate:"required"`
}

// Create/Update DTOs
type MediaFileCreateRequest struct {
	TenantID         uint                   `json:"tenant_id" validate:"required"`
	WebsiteID        uint                   `json:"website_id" validate:"required"`
	FolderID         *uint                  `json:"folder_id"`
	Filename         string                 `json:"filename" validate:"required"`
	OriginalFilename string                 `json:"original_filename" validate:"required"`
	MimeType         string                 `json:"mime_type" validate:"required"`
	FileSize         int64                  `json:"file_size" validate:"required,min=1"`
	FileHash         string                 `json:"file_hash" validate:"required"`
	StorageType      string                 `json:"storage_type" validate:"required,oneof=local minio s3 gcs"`
	StoragePath      string                 `json:"storage_path" validate:"required"`
	PublicURL        string                 `json:"public_url" validate:"required"`
	Width            *int                   `json:"width"`
	Height           *int                   `json:"height"`
	Duration         *int                   `json:"duration"`
	FileType         string                 `json:"file_type" validate:"required,oneof=image video audio document archive other"`
	Category         string                 `json:"category"`
	AltText          string                 `json:"alt_text"`
	Title            string                 `json:"title"`
	Description      string                 `json:"description"`
	Visibility       string                 `json:"visibility" validate:"omitempty,oneof=public private shared"`
	Metadata         map[string]interface{} `json:"metadata"`
	Tags             []string               `json:"tags"`
}

type MediaFileUpdateRequest struct {
	FolderID     *uint   `json:"folder_id"`
	Title        string  `json:"title"`
	AltText      string  `json:"alt_text"`
	Description  string  `json:"description"`
	Category     string  `json:"category"`
	Visibility   string  `json:"visibility" validate:"omitempty,oneof=public private shared"`
	Tags         []string `json:"tags"`
}

// Response DTOs
type MediaFileResponse struct {
	ID               uint                   `json:"id"`
	TenantID         uint                   `json:"tenant_id"`
	WebsiteID        uint                   `json:"website_id"`
	FolderID         *uint                  `json:"folder_id"`
	UserID           uint                   `json:"user_id"`
	Filename         string                 `json:"filename"`
	OriginalFilename string                 `json:"original_filename"`
	Slug             string                 `json:"slug"`
	MimeType         string                 `json:"mime_type"`
	FileSize         int64                  `json:"file_size"`
	FileHash         string                 `json:"file_hash"`
	StorageType      string                 `json:"storage_type"`
	PublicURL        string                 `json:"public_url"`
	Width            *int                   `json:"width"`
	Height           *int                   `json:"height"`
	Duration         *int                   `json:"duration"`
	Metadata         map[string]interface{} `json:"metadata"`
	FileType         string                 `json:"file_type"`
	Category         string                 `json:"category"`
	AltText          string                 `json:"alt_text"`
	Title            string                 `json:"title"`
	Description      string                 `json:"description"`
	Visibility       string                 `json:"visibility"`
	ViewCount        int                    `json:"view_count"`
	DownloadCount    int                    `json:"download_count"`
	LastAccessedAt   *time.Time             `json:"last_accessed_at"`
	Status           string                 `json:"status"`
	ProcessingStatus map[string]interface{} `json:"processing_status"`
	ErrorMessage     string                 `json:"error_message,omitempty"`
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
	Folder           *MediaFolderResponse   `json:"folder,omitempty"`
	Tags             []MediaTagResponse     `json:"tags,omitempty"`
	Thumbnails       []ThumbnailResponse    `json:"thumbnails,omitempty"`
}

type UploadSessionResponse struct {
	SessionID      string    `json:"session_id"`
	UploadType     string    `json:"upload_type"`
	Status         string    `json:"status"`
	Progress       float64   `json:"progress"`
	UploadedBytes  int64     `json:"uploaded_bytes"`
	TotalSize      int64     `json:"total_size"`
	UploadedChunks int       `json:"uploaded_chunks"`
	TotalChunks    *int      `json:"total_chunks"`
	ExpiresAt      time.Time `json:"expires_at"`
}

type ThumbnailResponse struct {
	ID        uint   `json:"id"`
	Size      string `json:"size"`
	Width     int    `json:"width"`
	Height    int    `json:"height"`
	Format    string `json:"format"`
	PublicURL string `json:"public_url"`
	FileSize  int64  `json:"file_size"`
	Status    string `json:"status"`
}

// Filter DTOs
type MediaFileFilter struct {
	TenantID      uint     `json:"tenant_id"`
	WebsiteID     uint     `json:"website_id"`
	FolderID      *uint    `json:"folder_id"`
	UserID        *uint    `json:"user_id"`
	FileTypes     []string `json:"file_types"`
	Categories    []string `json:"categories"`
	MimeTypes     []string `json:"mime_types"`
	Visibility    []string `json:"visibility"`
	Status        []string `json:"status"`
	Tags          []string `json:"tags"`
	SearchQuery   string   `json:"search_query"`
	MinSize       *int64   `json:"min_size"`
	MaxSize       *int64   `json:"max_size"`
	CreatedAfter  *time.Time `json:"created_after"`
	CreatedBefore *time.Time `json:"created_before"`
	SortBy        string   `json:"sort_by" validate:"omitempty,oneof=name size created_at updated_at"`
	SortOrder     string   `json:"sort_order" validate:"omitempty,oneof=asc desc"`
}

// Statistics Response DTOs
type StorageStatsResponse struct {
	TotalFiles       int64            `json:"total_files"`
	TotalSize        int64            `json:"total_size"`
	AverageFileSize  int64            `json:"average_file_size"`
	StorageTypes     map[string]int64 `json:"storage_types"`
	FormattedSize    string           `json:"formatted_size"`
	FormattedAverage string           `json:"formatted_average"`
}

type FileTypeStatsResponse struct {
	Images    int64            `json:"images"`
	Videos    int64            `json:"videos"`
	Audio     int64            `json:"audio"`
	Documents int64            `json:"documents"`
	Archives  int64            `json:"archives"`
	Others    int64            `json:"others"`
	Total     int64            `json:"total"`
	ByType    map[string]int64 `json:"by_type"`
}

// Helper functions
func MediaFileToResponse(file *models.MediaFile) *MediaFileResponse {
	resp := &MediaFileResponse{
		ID:               file.ID,
		TenantID:         file.TenantID,
		WebsiteID:        file.WebsiteID,
		FolderID:         file.FolderID,
		UserID:           file.UserID,
		Filename:         file.Filename,
		OriginalFilename: file.OriginalFilename,
		Slug:             file.Slug,
		MimeType:         file.MimeType,
		FileSize:         file.FileSize,
		FileHash:         file.FileHash,
		StorageType:      string(file.StorageType),
		PublicURL:        file.PublicURL,
		Width:            file.Width,
		Height:           file.Height,
		Duration:         file.Duration,
		FileType:         string(file.FileType),
		Category:         file.Category,
		AltText:          file.AltText,
		Title:            file.Title,
		Description:      file.Description,
		Visibility:       string(file.Visibility),
		ViewCount:        file.ViewCount,
		DownloadCount:    file.DownloadCount,
		LastAccessedAt:   file.LastAccessedAt,
		Status:           string(file.Status),
		ErrorMessage:     file.ErrorMessage,
		CreatedAt:        file.CreatedAt,
		UpdatedAt:        file.UpdatedAt,
	}

	// Parse metadata
	if file.Metadata != nil {
		var metadata map[string]interface{}
		if err := json.Unmarshal(file.Metadata, &metadata); err == nil {
			resp.Metadata = metadata
		}
	}

	// Parse processing status
	if file.ProcessingStatus != nil {
		var processingStatus map[string]interface{}
		if err := json.Unmarshal(file.ProcessingStatus, &processingStatus); err == nil {
			resp.ProcessingStatus = processingStatus
		}
	}

	// Add relationships
	if file.Folder != nil {
		resp.Folder = MediaFolderToResponse(file.Folder)
	}

	if len(file.Tags) > 0 {
		resp.Tags = make([]MediaTagResponse, len(file.Tags))
		for i, tag := range file.Tags {
			resp.Tags[i] = *MediaTagToResponse(&tag)
		}
	}

	if len(file.Thumbnails) > 0 {
		resp.Thumbnails = make([]ThumbnailResponse, len(file.Thumbnails))
		for i, thumb := range file.Thumbnails {
			resp.Thumbnails[i] = ThumbnailResponse{
				ID:        thumb.ID,
				Size:      string(thumb.Size),
				Width:     thumb.Width,
				Height:    thumb.Height,
				Format:    thumb.Format,
				PublicURL: thumb.PublicURL,
				FileSize:  thumb.FileSize,
				Status:    string(thumb.Status),
			}
		}
	}

	return resp
}