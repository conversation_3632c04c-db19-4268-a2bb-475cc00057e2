package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

type UploadSessionRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, session *models.UploadSession) error
	GetByID(ctx context.Context, id uint) (*models.UploadSession, error)
	GetBySessionID(ctx context.Context, sessionID string) (*models.UploadSession, error)
	Update(ctx context.Context, session *models.UploadSession) error
	Delete(ctx context.Context, id uint) error

	// Session operations
	UpdateProgress(ctx context.Context, sessionID string, uploadedBytes int64, uploadedChunks int) error
	UpdateStatus(ctx context.Context, sessionID string, status models.UploadStatus) error
	UpdateStatusWithError(ctx context.Context, sessionID string, status models.UploadStatus, errorMessage string) error
	MarkAsCompleted(ctx context.Context, sessionID string, finalPath string) error

	// List operations
	GetByUserID(ctx context.Context, userID uint) ([]*models.UploadSession, error)
	GetByStatus(ctx context.Context, status models.UploadStatus) ([]*models.UploadSession, error)
	GetActive(ctx context.Context, tenantID uint) ([]*models.UploadSession, error)
	GetExpired(ctx context.Context) ([]*models.UploadSession, error)

	// Cleanup operations
	CleanupExpired(ctx context.Context) (int64, error)
	CleanupOlderThan(ctx context.Context, duration time.Duration) (int64, error)
	CleanupByStatus(ctx context.Context, status models.UploadStatus, olderThan time.Duration) (int64, error)

	// Statistics
	GetUserUploadStats(ctx context.Context, userID uint) (*UserUploadStats, error)
	GetTenantUploadStats(ctx context.Context, tenantID uint) (*TenantUploadStats, error)
}

type UserUploadStats struct {
	ActiveSessions    int64
	CompletedSessions int64
	TotalUploadedSize int64
	AverageUploadSize int64
}

type TenantUploadStats struct {
	TotalUsers        int64
	ActiveSessions    int64
	CompletedToday    int64
	TotalUploadedSize int64
	StorageTypeUsage  map[string]int64
}