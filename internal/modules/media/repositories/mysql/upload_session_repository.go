package mysql

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"gorm.io/gorm"
)

type uploadSessionRepository struct {
	db *gorm.DB
}

func NewUploadSessionRepository(db *gorm.DB) repositories.UploadSessionRepository {
	return &uploadSessionRepository{db: db}
}

func (r *uploadSessionRepository) Create(ctx context.Context, session *models.UploadSession) error {
	return r.db.WithContext(ctx).Create(session).Error
}

func (r *uploadSessionRepository) GetByID(ctx context.Context, id uint) (*models.UploadSession, error) {
	var session models.UploadSession
	err := r.db.WithContext(ctx).First(&session, id).Error
	if err != nil {
		return nil, err
	}
	return &session, nil
}

func (r *uploadSessionRepository) GetBySessionID(ctx context.Context, sessionID string) (*models.UploadSession, error) {
	var session models.UploadSession
	err := r.db.WithContext(ctx).
		Where("session_id = ?", sessionID).
		First(&session).Error
	if err != nil {
		return nil, err
	}
	return &session, nil
}

func (r *uploadSessionRepository) Update(ctx context.Context, session *models.UploadSession) error {
	return r.db.WithContext(ctx).Save(session).Error
}

func (r *uploadSessionRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.UploadSession{}, id).Error
}

func (r *uploadSessionRepository) UpdateProgress(ctx context.Context, sessionID string, uploadedBytes int64, uploadedChunks int) error {
	return r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("session_id = ?", sessionID).
		Updates(map[string]interface{}{
			"uploaded_bytes":  uploadedBytes,
			"uploaded_chunks": uploadedChunks,
			"status":         models.UploadStatusUploading,
		}).Error
}

func (r *uploadSessionRepository) UpdateStatus(ctx context.Context, sessionID string, status models.UploadStatus) error {
	return r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("session_id = ?", sessionID).
		Update("status", status).Error
}

func (r *uploadSessionRepository) UpdateStatusWithError(ctx context.Context, sessionID string, status models.UploadStatus, errorMessage string) error {
	return r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("session_id = ?", sessionID).
		Updates(map[string]interface{}{
			"status":        status,
			"error_message": errorMessage,
		}).Error
}

func (r *uploadSessionRepository) MarkAsCompleted(ctx context.Context, sessionID string, finalPath string) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("session_id = ?", sessionID).
		Updates(map[string]interface{}{
			"status":       models.UploadStatusCompleted,
			"final_path":   finalPath,
			"completed_at": &now,
		}).Error
}

func (r *uploadSessionRepository) GetByUserID(ctx context.Context, userID uint) ([]*models.UploadSession, error) {
	var sessions []*models.UploadSession
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&sessions).Error
	return sessions, err
}

func (r *uploadSessionRepository) GetByStatus(ctx context.Context, status models.UploadStatus) ([]*models.UploadSession, error) {
	var sessions []*models.UploadSession
	err := r.db.WithContext(ctx).
		Where("status = ?", status).
		Find(&sessions).Error
	return sessions, err
}

func (r *uploadSessionRepository) GetActive(ctx context.Context, tenantID uint) ([]*models.UploadSession, error) {
	var sessions []*models.UploadSession
	err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Where("status IN ?", []models.UploadStatus{
			models.UploadStatusPending,
			models.UploadStatusUploading,
			models.UploadStatusProcessing,
		}).
		Where("expires_at > ?", time.Now()).
		Find(&sessions).Error
	return sessions, err
}

func (r *uploadSessionRepository) GetExpired(ctx context.Context) ([]*models.UploadSession, error) {
	var sessions []*models.UploadSession
	err := r.db.WithContext(ctx).
		Where("expires_at <= ?", time.Now()).
		Where("status NOT IN ?", []models.UploadStatus{
			models.UploadStatusCompleted,
			models.UploadStatusExpired,
		}).
		Find(&sessions).Error
	return sessions, err
}

func (r *uploadSessionRepository) CleanupExpired(ctx context.Context) (int64, error) {
	// First, update status to expired
	if err := r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("expires_at <= ?", time.Now()).
		Where("status NOT IN ?", []models.UploadStatus{
			models.UploadStatusCompleted,
			models.UploadStatusExpired,
		}).
		Update("status", models.UploadStatusExpired).Error; err != nil {
		return 0, err
	}

	// Then delete expired sessions older than 7 days
	cutoffDate := time.Now().AddDate(0, 0, -7)
	result := r.db.WithContext(ctx).
		Where("status = ? AND expires_at < ?", models.UploadStatusExpired, cutoffDate).
		Delete(&models.UploadSession{})

	return result.RowsAffected, result.Error
}

func (r *uploadSessionRepository) CleanupOlderThan(ctx context.Context, duration time.Duration) (int64, error) {
	cutoffTime := time.Now().Add(-duration)
	result := r.db.WithContext(ctx).
		Where("created_at < ?", cutoffTime).
		Delete(&models.UploadSession{})

	return result.RowsAffected, result.Error
}

func (r *uploadSessionRepository) CleanupByStatus(ctx context.Context, status models.UploadStatus, olderThan time.Duration) (int64, error) {
	cutoffTime := time.Now().Add(-olderThan)
	result := r.db.WithContext(ctx).
		Where("status = ? AND created_at < ?", status, cutoffTime).
		Delete(&models.UploadSession{})

	return result.RowsAffected, result.Error
}

func (r *uploadSessionRepository) GetUserUploadStats(ctx context.Context, userID uint) (*repositories.UserUploadStats, error) {
	stats := &repositories.UserUploadStats{}

	// Get active sessions count
	r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("user_id = ?", userID).
		Where("status IN ?", []models.UploadStatus{
			models.UploadStatusPending,
			models.UploadStatusUploading,
			models.UploadStatusProcessing,
		}).
		Count(&stats.ActiveSessions)

	// Get completed sessions count
	r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("user_id = ?", userID).
		Where("status = ?", models.UploadStatusCompleted).
		Count(&stats.CompletedSessions)

	// Get total uploaded size
	r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("user_id = ?", userID).
		Where("status = ?", models.UploadStatusCompleted).
		Select("COALESCE(SUM(total_size), 0)").
		Scan(&stats.TotalUploadedSize)

	// Calculate average upload size
	if stats.CompletedSessions > 0 {
		stats.AverageUploadSize = stats.TotalUploadedSize / stats.CompletedSessions
	}

	return stats, nil
}

func (r *uploadSessionRepository) GetTenantUploadStats(ctx context.Context, tenantID uint) (*repositories.TenantUploadStats, error) {
	stats := &repositories.TenantUploadStats{
		StorageTypeUsage: make(map[string]int64),
	}

	// Get total users
	r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("tenant_id = ?", tenantID).
		Distinct("user_id").
		Count(&stats.TotalUsers)

	// Get active sessions count
	r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("tenant_id = ?", tenantID).
		Where("status IN ?", []models.UploadStatus{
			models.UploadStatusPending,
			models.UploadStatusUploading,
			models.UploadStatusProcessing,
		}).
		Count(&stats.ActiveSessions)

	// Get completed today
	startOfDay := time.Now().Truncate(24 * time.Hour)
	r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("tenant_id = ?", tenantID).
		Where("status = ?", models.UploadStatusCompleted).
		Where("completed_at >= ?", startOfDay).
		Count(&stats.CompletedToday)

	// Get total uploaded size
	r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("tenant_id = ?", tenantID).
		Where("status = ?", models.UploadStatusCompleted).
		Select("COALESCE(SUM(total_size), 0)").
		Scan(&stats.TotalUploadedSize)

	// Get storage type usage
	var storageTypes []struct {
		StorageType string
		Count       int64
	}
	r.db.WithContext(ctx).
		Model(&models.UploadSession{}).
		Where("tenant_id = ?", tenantID).
		Where("status = ?", models.UploadStatusCompleted).
		Select("storage_type, COUNT(*) as count").
		Group("storage_type").
		Scan(&storageTypes)

	for _, st := range storageTypes {
		stats.StorageTypeUsage[st.StorageType] = st.Count
	}

	return stats, nil
}