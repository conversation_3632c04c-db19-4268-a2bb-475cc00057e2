package mysql

import (
	"context"
	"fmt"
	"strings"

	"github.com/gosimple/slug"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

type mediaFolderRepository struct {
	db *gorm.DB
}

func NewMediaFolderRepository(db *gorm.DB) repositories.MediaFolderRepository {
	return &mediaFolderRepository{db: db}
}

func (r *mediaFolderRepository) Create(ctx context.Context, folder *models.MediaFolder) error {
	// Generate slug if not provided
	if folder.Slug == "" {
		folder.Slug = slug.Make(folder.Name)
	}

	// Generate unique slug
	uniqueSlug, err := r.GenerateUniquePath(ctx, folder.TenantID, folder.WebsiteID, folder.ParentID, folder.Name)
	if err != nil {
		return err
	}
	folder.Slug = uniqueSlug

	// Set path based on parent
	if folder.ParentID != nil {
		parent, err := r.GetByID(ctx, *folder.ParentID)
		if err != nil {
			return err
		}
		folder.Path = parent.GetFullPath()
	} else {
		folder.Path = ""
	}

	return r.db.WithContext(ctx).Create(folder).Error
}

func (r *mediaFolderRepository) GetByID(ctx context.Context, id uint) (*models.MediaFolder, error) {
	var folder models.MediaFolder
	err := r.db.WithContext(ctx).
		Preload("Parent").
		Preload("Children", "status = ?", models.FolderStatusActive).
		First(&folder, id).Error
	if err != nil {
		return nil, err
	}
	return &folder, nil
}

func (r *mediaFolderRepository) GetBySlug(ctx context.Context, tenantID, websiteID uint, parentID *uint, slug string) (*models.MediaFolder, error) {
	var folder models.MediaFolder
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND slug = ?", tenantID, websiteID, slug)

	if parentID != nil {
		query = query.Where("parent_id = ?", *parentID)
	} else {
		query = query.Where("parent_id IS NULL")
	}

	err := query.First(&folder).Error
	if err != nil {
		return nil, err
	}
	return &folder, nil
}

func (r *mediaFolderRepository) GetByPath(ctx context.Context, tenantID, websiteID uint, path string) (*models.MediaFolder, error) {
	// Split path into segments
	segments := strings.Split(strings.Trim(path, "/"), "/")
	if len(segments) == 0 || (len(segments) == 1 && segments[0] == "") {
		return nil, gorm.ErrRecordNotFound
	}

	var currentFolder *models.MediaFolder
	var parentID *uint

	// Traverse the path
	for _, segment := range segments {
		folder, err := r.GetBySlug(ctx, tenantID, websiteID, parentID, segment)
		if err != nil {
			return nil, err
		}
		currentFolder = folder
		parentID = &folder.ID
	}

	return currentFolder, nil
}

func (r *mediaFolderRepository) Update(ctx context.Context, folder *models.MediaFolder) error {
	// If parent changed, update path
	if folder.ParentID != nil {
		parent, err := r.GetByID(ctx, *folder.ParentID)
		if err != nil {
			return err
		}
		folder.Path = parent.GetFullPath()
	} else {
		folder.Path = ""
	}

	return r.db.WithContext(ctx).Save(folder).Error
}

func (r *mediaFolderRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.MediaFolder{}, id).Error
}

func (r *mediaFolderRepository) SoftDelete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.MediaFolder{}).
		Where("id = ?", id).
		Update("status", models.FolderStatusDeleted).Error
}

func (r *mediaFolderRepository) GetTree(ctx context.Context, tenantID, websiteID uint, maxDepth int) ([]*models.MediaFolder, error) {
	var rootFolders []*models.MediaFolder
	
	// Get root folders
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND parent_id IS NULL", tenantID, websiteID).
		Where("status = ?", models.FolderStatusActive).
		Order("sort_order ASC, name ASC").
		Find(&rootFolders).Error
	if err != nil {
		return nil, err
	}

	// Load children recursively
	for _, folder := range rootFolders {
		if err := r.loadChildren(ctx, folder, 1, maxDepth); err != nil {
			return nil, err
		}
	}

	return rootFolders, nil
}

func (r *mediaFolderRepository) GetChildren(ctx context.Context, parentID uint) ([]*models.MediaFolder, error) {
	var folders []*models.MediaFolder
	err := r.db.WithContext(ctx).
		Where("parent_id = ?", parentID).
		Where("status = ?", models.FolderStatusActive).
		Order("sort_order ASC, name ASC").
		Find(&folders).Error
	return folders, err
}

func (r *mediaFolderRepository) GetAncestors(ctx context.Context, folderID uint) ([]*models.MediaFolder, error) {
	var ancestors []*models.MediaFolder
	currentID := folderID

	for {
		folder, err := r.GetByID(ctx, currentID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				break
			}
			return nil, err
		}

		if folder.ParentID == nil {
			break
		}

		parent, err := r.GetByID(ctx, *folder.ParentID)
		if err != nil {
			return nil, err
		}

		ancestors = append([]*models.MediaFolder{parent}, ancestors...)
		currentID = parent.ID
	}

	return ancestors, nil
}

func (r *mediaFolderRepository) GetDescendants(ctx context.Context, folderID uint) ([]*models.MediaFolder, error) {
	var descendants []*models.MediaFolder
	
	// Recursive CTE to get all descendants
	err := r.db.WithContext(ctx).
		Raw(`
			WITH RECURSIVE folder_tree AS (
				SELECT * FROM media_folders WHERE parent_id = ? AND status = ?
				UNION ALL
				SELECT f.* FROM media_folders f
				JOIN folder_tree ft ON f.parent_id = ft.id
				WHERE f.status = ?
			)
			SELECT * FROM folder_tree ORDER BY path, name
		`, folderID, models.FolderStatusActive, models.FolderStatusActive).
		Scan(&descendants).Error

	return descendants, err
}

func (r *mediaFolderRepository) MoveFolder(ctx context.Context, folderID uint, newParentID *uint) error {
	// Get the folder
	folder, err := r.GetByID(ctx, folderID)
	if err != nil {
		return err
	}

	// Check for circular reference
	if newParentID != nil {
		if err := r.checkCircularReference(ctx, folderID, *newParentID); err != nil {
			return err
		}
	}

	// Update parent and path
	folder.ParentID = newParentID
	if err := r.Update(ctx, folder); err != nil {
		return err
	}

	// Update paths for all descendants
	return r.UpdatePaths(ctx, folderID)
}

func (r *mediaFolderRepository) List(ctx context.Context, filter *dto.MediaFolderFilter, cursor *pagination.CursorRequest) ([]*models.MediaFolder, *pagination.CursorResponse, error) {
	query := r.db.WithContext(ctx)

	if filter.TenantID > 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID > 0 {
		query = query.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.ParentID != nil {
		query = query.Where("parent_id = ?", *filter.ParentID)
	} else if !filter.IncludeTree {
		query = query.Where("parent_id IS NULL")
	}
	if filter.SearchQuery != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", 
			"%"+filter.SearchQuery+"%", "%"+filter.SearchQuery+"%")
	}

	query = query.Where("status = ?", models.FolderStatusActive)

	// Apply cursor pagination
	if cursor != nil && cursor.Cursor != "" {
		decodedCursor, err := pagination.DecodeCursor(cursor.Cursor)
		if err == nil {
			query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))",
				decodedCursor.Time, decodedCursor.Time, decodedCursor.ID)
		}
	}

	// Apply sorting
	query = query.Order("created_at DESC, id DESC")

	// Apply limit
	limit := 20
	if cursor != nil && cursor.Limit > 0 {
		limit = cursor.Limit
	}
	query = query.Limit(limit + 1)

	var folders []*models.MediaFolder
	err := query.Find(&folders).Error
	if err != nil {
		return nil, nil, err
	}

	// Check if there are more results
	hasMore := false
	if len(folders) > limit {
		hasMore = true
		folders = folders[:limit]
	}

	// Generate next cursor
	var nextCursor string
	if hasMore && len(folders) > 0 {
		lastFolder := folders[len(folders)-1]
		nextCursor, _ = pagination.EncodeCursor(lastFolder.ID, lastFolder.CreatedAt)
	}

	// Create cursor response
	cursorResp := &pagination.CursorResponse{
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}

	// Load children if tree is requested
	if filter.IncludeTree {
		for _, folder := range folders {
			if err := r.loadChildren(ctx, folder, 1, filter.MaxDepth); err != nil {
				return nil, nil, err
			}
		}
	}

	// Load files if requested
	if filter.IncludeFiles {
		for _, folder := range folders {
			if err := r.loadFiles(ctx, folder); err != nil {
				return nil, nil, err
			}
		}
	}

	return folders, cursorResp, nil
}

func (r *mediaFolderRepository) Search(ctx context.Context, tenantID, websiteID uint, query string) ([]*models.MediaFolder, error) {
	var folders []*models.MediaFolder
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID).
		Where("status = ?", models.FolderStatusActive).
		Where("name LIKE ? OR description LIKE ?", "%"+query+"%", "%"+query+"%").
		Order("name ASC").
		Find(&folders).Error
	return folders, err
}

func (r *mediaFolderRepository) UpdatePaths(ctx context.Context, folderID uint) error {
	_, err := r.GetByID(ctx, folderID)
	if err != nil {
		return err
	}

	// Get all descendants
	descendants, err := r.GetDescendants(ctx, folderID)
	if err != nil {
		return err
	}

	// Update each descendant's path
	for _, desc := range descendants {
		parent, err := r.GetByID(ctx, *desc.ParentID)
		if err != nil {
			return err
		}
		desc.Path = parent.GetFullPath()
		if err := r.db.WithContext(ctx).Save(desc).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *mediaFolderRepository) GenerateUniquePath(ctx context.Context, tenantID, websiteID uint, parentID *uint, name string) (string, error) {
	baseSlug := slug.Make(name)
	uniqueSlug := baseSlug
	counter := 1

	for {
		exists, err := r.slugExists(ctx, tenantID, websiteID, parentID, uniqueSlug)
		if err != nil {
			return "", err
		}
		if !exists {
			break
		}
		uniqueSlug = fmt.Sprintf("%s-%d", baseSlug, counter)
		counter++
	}

	return uniqueSlug, nil
}

func (r *mediaFolderRepository) CountByParentID(ctx context.Context, parentID *uint) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.MediaFolder{})
	
	if parentID != nil {
		query = query.Where("parent_id = ?", *parentID)
	} else {
		query = query.Where("parent_id IS NULL")
	}
	
	err := query.Where("status = ?", models.FolderStatusActive).Count(&count).Error
	return count, err
}

func (r *mediaFolderRepository) GetFolderStats(ctx context.Context, folderID uint) (*repositories.FolderStats, error) {
	stats := &repositories.FolderStats{
		FileTypes: make(map[string]int64),
	}

	// Get file count and total size
	r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("folder_id = ?", folderID).
		Where("status != ?", models.FileStatusDeleted).
		Select("COUNT(*) as file_count, COALESCE(SUM(file_size), 0) as total_size").
		Scan(&stats)

	// Get subfolder count
	r.db.WithContext(ctx).
		Model(&models.MediaFolder{}).
		Where("parent_id = ?", folderID).
		Where("status = ?", models.FolderStatusActive).
		Count(&stats.SubfolderCount)

	// Get file type distribution
	var fileTypes []struct {
		FileType string
		Count    int64
	}
	r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("folder_id = ?", folderID).
		Where("status != ?", models.FileStatusDeleted).
		Select("file_type, COUNT(*) as count").
		Group("file_type").
		Scan(&fileTypes)

	for _, ft := range fileTypes {
		stats.FileTypes[ft.FileType] = ft.Count
	}

	return stats, nil
}

func (r *mediaFolderRepository) GetSystemFolders(ctx context.Context, tenantID, websiteID uint) ([]*models.MediaFolder, error) {
	var folders []*models.MediaFolder
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND is_system = ?", tenantID, websiteID, true).
		Where("status = ?", models.FolderStatusActive).
		Find(&folders).Error
	return folders, err
}

func (r *mediaFolderRepository) CreateSystemFolders(ctx context.Context, tenantID, websiteID uint) error {
	systemFolders := []models.MediaFolder{
		{
			TenantID:    tenantID,
			WebsiteID:   websiteID,
			Name:        "Images",
			Slug:        "images",
			Path:        "",
			Description: "System folder for images",
			Icon:        "image",
			IsSystem:    true,
			SortOrder:   1,
			Status:      models.FolderStatusActive,
		},
		{
			TenantID:    tenantID,
			WebsiteID:   websiteID,
			Name:        "Documents",
			Slug:        "documents",
			Path:        "",
			Description: "System folder for documents",
			Icon:        "document",
			IsSystem:    true,
			SortOrder:   2,
			Status:      models.FolderStatusActive,
		},
		{
			TenantID:    tenantID,
			WebsiteID:   websiteID,
			Name:        "Videos",
			Slug:        "videos",
			Path:        "",
			Description: "System folder for videos",
			Icon:        "video",
			IsSystem:    true,
			SortOrder:   3,
			Status:      models.FolderStatusActive,
		},
	}

	return r.db.WithContext(ctx).CreateInBatches(systemFolders, 10).Error
}

// Helper methods
func (r *mediaFolderRepository) loadChildren(ctx context.Context, folder *models.MediaFolder, currentDepth, maxDepth int) error {
	if maxDepth > 0 && currentDepth > maxDepth {
		return nil
	}

	children, err := r.GetChildren(ctx, folder.ID)
	if err != nil {
		return err
	}

	folder.Children = make([]models.MediaFolder, len(children))
	for i, child := range children {
		folder.Children[i] = *child
		if err := r.loadChildren(ctx, &folder.Children[i], currentDepth+1, maxDepth); err != nil {
			return err
		}
	}

	return nil
}

func (r *mediaFolderRepository) loadFiles(ctx context.Context, folder *models.MediaFolder) error {
	var files []models.MediaFile
	err := r.db.WithContext(ctx).
		Where("folder_id = ?", folder.ID).
		Where("status != ?", models.FileStatusDeleted).
		Find(&files).Error
	if err != nil {
		return err
	}
	folder.Files = files
	return nil
}

func (r *mediaFolderRepository) slugExists(ctx context.Context, tenantID, websiteID uint, parentID *uint, slug string) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.MediaFolder{}).
		Where("tenant_id = ? AND website_id = ? AND slug = ?", tenantID, websiteID, slug)

	if parentID != nil {
		query = query.Where("parent_id = ?", *parentID)
	} else {
		query = query.Where("parent_id IS NULL")
	}

	err := query.Count(&count).Error
	return count > 0, err
}

func (r *mediaFolderRepository) checkCircularReference(ctx context.Context, folderID, newParentID uint) error {
	// Check if newParentID is a descendant of folderID
	descendants, err := r.GetDescendants(ctx, folderID)
	if err != nil {
		return err
	}

	for _, desc := range descendants {
		if desc.ID == newParentID {
			return fmt.Errorf("circular reference detected: cannot move folder to its own descendant")
		}
	}

	return nil
}