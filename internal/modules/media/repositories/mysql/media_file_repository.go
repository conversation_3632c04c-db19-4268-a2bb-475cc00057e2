package mysql

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

type mediaFileRepository struct {
	db *gorm.DB
}

func NewMediaFileRepository(db *gorm.DB) repositories.MediaFileRepository {
	return &mediaFileRepository{db: db}
}

func (r *mediaFileRepository) Create(ctx context.Context, file *models.MediaFile) error {
	return r.db.WithContext(ctx).Create(file).Error
}

func (r *mediaFileRepository) GetByID(ctx context.Context, id uint) (*models.MediaFile, error) {
	var file models.MediaFile
	err := r.db.WithContext(ctx).
		Preload("Folder").
		Preload("Tags").
		Preload("Thumbnails").
		First(&file, id).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

func (r *mediaFileRepository) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.MediaFile, error) {
	var file models.MediaFile
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND slug = ?", tenantID, websiteID, slug).
		Preload("Folder").
		Preload("Tags").
		Preload("Thumbnails").
		First(&file).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

func (r *mediaFileRepository) GetByHash(ctx context.Context, tenantID uint, hash string) (*models.MediaFile, error) {
	var file models.MediaFile
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND file_hash = ?", tenantID, hash).
		First(&file).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

func (r *mediaFileRepository) Update(ctx context.Context, file *models.MediaFile) error {
	return r.db.WithContext(ctx).Save(file).Error
}

func (r *mediaFileRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.MediaFile{}, id).Error
}

func (r *mediaFileRepository) SoftDelete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", id).
		Update("status", models.FileStatusDeleted).Error
}

func (r *mediaFileRepository) CreateBatch(ctx context.Context, files []*models.MediaFile) error {
	return r.db.WithContext(ctx).CreateInBatches(files, 100).Error
}

func (r *mediaFileRepository) UpdateBatch(ctx context.Context, ids []uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id IN ?", ids).
		Updates(updates).Error
}

func (r *mediaFileRepository) DeleteBatch(ctx context.Context, ids []uint) error {
	return r.db.WithContext(ctx).Delete(&models.MediaFile{}, ids).Error
}

func (r *mediaFileRepository) SoftDeleteBatch(ctx context.Context, ids []uint) error {
	return r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id IN ?", ids).
		Update("status", models.FileStatusDeleted).Error
}

func (r *mediaFileRepository) List(ctx context.Context, filter *dto.MediaFileFilter, cursor *pagination.CursorRequest) ([]*models.MediaFile, *pagination.CursorResponse, error) {
	query := r.buildFilterQuery(r.db.WithContext(ctx), filter)

	// Apply cursor pagination
	if cursor != nil && cursor.Cursor != "" {
		decodedCursor, err := pagination.DecodeCursor(cursor.Cursor)
		if err == nil {
			query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))",
				decodedCursor.Time, decodedCursor.Time, decodedCursor.ID)
		}
	}

	// Apply sorting
	sortBy := "created_at"
	sortOrder := "DESC"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}
	query = query.Order(fmt.Sprintf("%s %s, id DESC", sortBy, sortOrder))

	// Apply limit
	limit := 20
	if cursor != nil && cursor.Limit > 0 {
		limit = cursor.Limit
	}
	query = query.Limit(limit + 1)

	// Execute query
	var files []*models.MediaFile
	if err := query.
		Preload("Folder").
		//Preload("Tags").
		//Preload("Thumbnails").
		Find(&files).Error; err != nil {
		return nil, nil, err
	}

	// Check if there are more results
	hasMore := false
	if len(files) > limit {
		hasMore = true
		files = files[:limit]
	}

	// Generate next cursor
	var nextCursor string
	if hasMore && len(files) > 0 {
		lastFile := files[len(files)-1]
		nextCursor, _ = pagination.EncodeCursor(lastFile.ID, lastFile.CreatedAt)
	}

	cursorResp := pagination.NewCursorResponse(hasMore, nextCursor, len(files))
	return files, cursorResp, nil
}

func (r *mediaFileRepository) Search(ctx context.Context, tenantID, websiteID uint, query string, cursor *pagination.CursorRequest) ([]*models.MediaFile, *pagination.CursorResponse, error) {
	dbQuery := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID).
		Where("status != ?", models.FileStatusDeleted)

	if query != "" {
		dbQuery = dbQuery.Where(
			"MATCH(filename, original_filename, alt_text, title, description) AGAINST(? IN NATURAL LANGUAGE MODE)",
			query,
		)
	}

	// Apply cursor and pagination logic similar to List method
	return r.paginateQuery(dbQuery, cursor)
}

func (r *mediaFileRepository) CountByFilter(ctx context.Context, filter *dto.MediaFileFilter) (int64, error) {
	var count int64
	query := r.buildFilterQuery(r.db.WithContext(ctx), filter)
	err := query.Model(&models.MediaFile{}).Count(&count).Error
	return count, err
}

func (r *mediaFileRepository) GetByFolderID(ctx context.Context, tenantID, websiteID, folderID uint) ([]*models.MediaFile, error) {
	var files []*models.MediaFile
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND folder_id = ?", tenantID, websiteID, folderID).
		Where("status != ?", models.FileStatusDeleted).
		Find(&files).Error
	return files, err
}

func (r *mediaFileRepository) MoveToFolder(ctx context.Context, fileIDs []uint, folderID *uint) error {
	return r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id IN ?", fileIDs).
		Update("folder_id", folderID).Error
}

func (r *mediaFileRepository) CountByFolderID(ctx context.Context, tenantID, websiteID, folderID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("tenant_id = ? AND website_id = ? AND folder_id = ?", tenantID, websiteID, folderID).
		Where("status != ?", models.FileStatusDeleted).
		Count(&count).Error
	return count, err
}

func (r *mediaFileRepository) GetFolderSize(ctx context.Context, tenantID, websiteID, folderID uint) (int64, error) {
	var totalSize int64
	err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("tenant_id = ? AND website_id = ? AND folder_id = ?", tenantID, websiteID, folderID).
		Where("status != ?", models.FileStatusDeleted).
		Select("COALESCE(SUM(file_size), 0)").
		Scan(&totalSize).Error
	return totalSize, err
}

func (r *mediaFileRepository) AddTags(ctx context.Context, fileID uint, tagIDs []uint) error {
	var file models.MediaFile
	if err := r.db.WithContext(ctx).First(&file, fileID).Error; err != nil {
		return err
	}

	var tags []models.MediaTag
	if err := r.db.WithContext(ctx).Find(&tags, tagIDs).Error; err != nil {
		return err
	}

	return r.db.WithContext(ctx).Model(&file).Association("Tags").Append(&tags)
}

func (r *mediaFileRepository) RemoveTags(ctx context.Context, fileID uint, tagIDs []uint) error {
	var file models.MediaFile
	if err := r.db.WithContext(ctx).First(&file, fileID).Error; err != nil {
		return err
	}

	var tags []models.MediaTag
	if err := r.db.WithContext(ctx).Find(&tags, tagIDs).Error; err != nil {
		return err
	}

	return r.db.WithContext(ctx).Model(&file).Association("Tags").Delete(&tags)
}

func (r *mediaFileRepository) SetTags(ctx context.Context, fileID uint, tagIDs []uint) error {
	var file models.MediaFile
	if err := r.db.WithContext(ctx).First(&file, fileID).Error; err != nil {
		return err
	}

	var tags []models.MediaTag
	if len(tagIDs) > 0 {
		if err := r.db.WithContext(ctx).Find(&tags, tagIDs).Error; err != nil {
			return err
		}
	}

	return r.db.WithContext(ctx).Model(&file).Association("Tags").Replace(&tags)
}

func (r *mediaFileRepository) GetByTagID(ctx context.Context, tenantID, tagID uint) ([]*models.MediaFile, error) {
	var files []*models.MediaFile
	err := r.db.WithContext(ctx).
		Joins("JOIN media_file_tags ON media_file_tags.file_id = media_files.id").
		Where("media_files.tenant_id = ? AND media_file_tags.tag_id = ?", tenantID, tagID).
		Where("media_files.status != ?", models.FileStatusDeleted).
		Find(&files).Error
	return files, err
}

func (r *mediaFileRepository) UpdateStatus(ctx context.Context, id uint, status models.FileStatus) error {
	return r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", id).
		Update("status", status).Error
}

func (r *mediaFileRepository) UpdateProcessingStatus(ctx context.Context, id uint, processingStatus map[string]interface{}) error {
	statusJSON, err := json.Marshal(processingStatus)
	if err != nil {
		return err
	}

	return r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", id).
		Update("processing_status", statusJSON).Error
}

func (r *mediaFileRepository) GetByStatus(ctx context.Context, tenantID uint, status models.FileStatus) ([]*models.MediaFile, error) {
	var files []*models.MediaFile
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Find(&files).Error
	return files, err
}

func (r *mediaFileRepository) IncrementViewCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", id).
		UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).
		UpdateColumn("last_accessed_at", time.Now()).Error
}

func (r *mediaFileRepository) IncrementDownloadCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", id).
		UpdateColumn("download_count", gorm.Expr("download_count + ?", 1)).
		UpdateColumn("last_accessed_at", time.Now()).Error
}

func (r *mediaFileRepository) UpdateLastAccessedAt(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", id).
		Update("last_accessed_at", time.Now()).Error
}

func (r *mediaFileRepository) GetByStorageType(ctx context.Context, tenantID uint, storageType models.StorageType) ([]*models.MediaFile, error) {
	var files []*models.MediaFile
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND storage_type = ?", tenantID, storageType).
		Where("status != ?", models.FileStatusDeleted).
		Find(&files).Error
	return files, err
}

func (r *mediaFileRepository) GetExpiredFiles(ctx context.Context, expiryTime time.Time) ([]*models.MediaFile, error) {
	var files []*models.MediaFile
	err := r.db.WithContext(ctx).
		Where("created_at < ? AND status = ?", expiryTime, models.FileStatusUploading).
		Find(&files).Error
	return files, err
}

func (r *mediaFileRepository) GetOrphanedFiles(ctx context.Context, tenantID uint) ([]*models.MediaFile, error) {
	var files []*models.MediaFile
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND folder_id IS NOT NULL", tenantID).
		Where("status != ?", models.FileStatusDeleted).
		Where("folder_id NOT IN (SELECT id FROM media_folders WHERE tenant_id = ?)", tenantID).
		Find(&files).Error
	return files, err
}

func (r *mediaFileRepository) GetStorageStats(ctx context.Context, tenantID, websiteID uint) (*repositories.StorageStats, error) {
	var stats repositories.StorageStats

	// Get total files and size
	r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID).
		Where("status != ?", models.FileStatusDeleted).
		Select("COUNT(*) as total_files, COALESCE(SUM(file_size), 0) as total_size").
		Scan(&stats)

	// Calculate average file size
	if stats.TotalFiles > 0 {
		stats.AverageFileSize = stats.TotalSize / stats.TotalFiles
	}

	// Get storage type distribution
	var storageTypes []struct {
		StorageType string
		Count       int64
	}
	r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID).
		Where("status != ?", models.FileStatusDeleted).
		Select("storage_type, COUNT(*) as count").
		Group("storage_type").
		Scan(&storageTypes)

	stats.StorageTypes = make(map[string]int64)
	for _, st := range storageTypes {
		stats.StorageTypes[st.StorageType] = st.Count
	}

	return &stats, nil
}

func (r *mediaFileRepository) GetFileTypeStats(ctx context.Context, tenantID, websiteID uint) (*repositories.FileTypeStats, error) {
	var stats repositories.FileTypeStats

	r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID).
		Where("status != ?", models.FileStatusDeleted).
		Select(
			"COUNT(CASE WHEN file_type = '"+string(models.FileTypeImage)+"' THEN 1 END) as images",
			"COUNT(CASE WHEN file_type = '"+string(models.FileTypeVideo)+"' THEN 1 END) as videos",
			"COUNT(CASE WHEN file_type = '"+string(models.FileTypeAudio)+"' THEN 1 END) as audio",
			"COUNT(CASE WHEN file_type = '"+string(models.FileTypeDocument)+"' THEN 1 END) as documents",
			"COUNT(CASE WHEN file_type = '"+string(models.FileTypeArchive)+"' THEN 1 END) as archives",
			"COUNT(CASE WHEN file_type = '"+string(models.FileTypeOther)+"' THEN 1 END) as others",
		).
		Scan(&stats)

	return &stats, nil
}

// Helper methods
func (r *mediaFileRepository) buildFilterQuery(query *gorm.DB, filter *dto.MediaFileFilter) *gorm.DB {
	if filter.TenantID > 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID > 0 {
		query = query.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.FolderID != nil {
		query = query.Where("folder_id = ?", *filter.FolderID)
	}
	if filter.UserID != nil {
		query = query.Where("user_id = ?", *filter.UserID)
	}
	if len(filter.FileTypes) > 0 {
		query = query.Where("file_type IN ?", filter.FileTypes)
	}
	if len(filter.Categories) > 0 {
		query = query.Where("category IN ?", filter.Categories)
	}
	if len(filter.MimeTypes) > 0 {
		query = query.Where("mime_type IN ?", filter.MimeTypes)
	}
	if len(filter.Visibility) > 0 {
		query = query.Where("visibility IN ?", filter.Visibility)
	}
	if len(filter.Status) > 0 {
		query = query.Where("status IN ?", filter.Status)
	} else {
		query = query.Where("status != ?", models.FileStatusDeleted)
	}
	if filter.MinSize != nil {
		query = query.Where("file_size >= ?", *filter.MinSize)
	}
	if filter.MaxSize != nil {
		query = query.Where("file_size <= ?", *filter.MaxSize)
	}
	if filter.CreatedAfter != nil {
		query = query.Where("created_at >= ?", *filter.CreatedAfter)
	}
	if filter.CreatedBefore != nil {
		query = query.Where("created_at <= ?", *filter.CreatedBefore)
	}
	if len(filter.Tags) > 0 {
		query = query.Joins("JOIN media_file_tags ON media_file_tags.file_id = media_files.id").
			Joins("JOIN media_tags ON media_tags.id = media_file_tags.tag_id").
			Where("media_tags.name IN ?", filter.Tags).
			Group("media_files.id")
	}

	return query
}

func (r *mediaFileRepository) paginateQuery(query *gorm.DB, cursor *pagination.CursorRequest) ([]*models.MediaFile, *pagination.CursorResponse, error) {
	// Apply cursor pagination
	if cursor != nil && cursor.Cursor != "" {
		decodedCursor, err := pagination.DecodeCursor(cursor.Cursor)
		if err == nil {
			query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))",
				decodedCursor.Time, decodedCursor.Time, decodedCursor.ID)
		}
	}

	// Apply sorting
	query = query.Order("created_at DESC, id DESC")

	// Apply limit
	limit := 20
	if cursor != nil && cursor.Limit > 0 {
		limit = cursor.Limit
	}
	query = query.Limit(limit + 1)

	// Execute query
	var files []*models.MediaFile
	if err := query.
		Preload("Folder").
		Preload("Tags").
		Preload("Thumbnails").
		Find(&files).Error; err != nil {
		return nil, nil, err
	}

	// Check if there are more results
	hasMore := false
	if len(files) > limit {
		hasMore = true
		files = files[:limit]
	}

	// Generate next cursor
	var nextCursor string
	if hasMore && len(files) > 0 {
		lastFile := files[len(files)-1]
		nextCursor, _ = pagination.EncodeCursor(lastFile.ID, lastFile.CreatedAt)
	}

	cursorResp := pagination.NewCursorResponse(hasMore, nextCursor, len(files))
	return files, cursorResp, nil
}
