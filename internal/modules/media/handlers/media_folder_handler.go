package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

type MediaFolderHandler struct {
	folderService services.MediaFolderService
	validator     validator.Validator
	logger        utils.Logger
}

func NewMediaFolderHandler(
	folderService services.MediaFolderService,
	validator validator.Validator,
	logger utils.Logger,
) *MediaFolderHandler {
	return &MediaFolderHandler{
		folderService: folderService,
		validator:     validator,
		logger:        logger,
	}
}

// CreateFolder godoc
// @Summary      Create media folder
// @Description  Creates a new media folder
// @Tags         media
// @Accept       json
// @Produce      json
// @Param        body body dto.MediaFolderCreateRequest true "Folder data"
// @Success      201 {object} response.Response{data=dto.MediaFolderResponse}
// @Failure      400 {object} response.Response
// @Failure      409 {object} response.Response
// @Router       /api/cms/v1/media/folders [post]
func (h *MediaFolderHandler) CreateFolder(c *gin.Context) {
	var req dto.MediaFolderCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	// Set tenant and website from context
	req.TenantID = c.GetUint("tenant_id")
	req.WebsiteID = c.GetUint("website_id")

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	result, err := h.folderService.CreateFolder(c.Request.Context(), &req)
	if err != nil {
		if services.IsServiceError(err, "FOLDER_EXISTS") {
			response.Conflict(c.Writer, err.Error())
			return
		}
		
		h.logger.WithError(err).Error("Failed to create folder")
		response.InternalServerErrorWithContext(c, "Failed to create folder")
		return
	}

	response.CreatedWithContext(c, result)
}

// GetFolder godoc
// @Summary      Get media folder by ID
// @Description  Retrieves a media folder by its ID
// @Tags         media
// @Produce      json
// @Param        id path int true "Folder ID"
// @Success      200 {object} response.Response{data=dto.MediaFolderResponse}
// @Failure      404 {object} response.Response
// @Router       /api/cms/v1/media/folders/{id} [get]
func (h *MediaFolderHandler) GetFolder(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid folder ID")
		return
	}

	result, err := h.folderService.GetFolderByID(c.Request.Context(), uint(id))
	if err != nil {
		if services.IsServiceError(err, "FOLDER_NOT_FOUND") {
			response.NotFound(c.Writer, "Folder not found")
			return
		}
		
		h.logger.WithError(err).Error("Failed to get folder")
		response.InternalServerErrorWithContext(c, "Failed to get folder")
		return
	}

	response.SuccessWithContext(c, result)
}

// UpdateFolder godoc
// @Summary      Update media folder
// @Description  Updates a media folder
// @Tags         media
// @Accept       json
// @Produce      json
// @Param        id path int true "Folder ID"
// @Param        body body dto.MediaFolderUpdateRequest true "Update data"
// @Success      200 {object} response.Response{data=dto.MediaFolderResponse}
// @Failure      400 {object} response.Response
// @Failure      404 {object} response.Response
// @Router       /api/cms/v1/media/folders/{id} [put]
func (h *MediaFolderHandler) UpdateFolder(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid folder ID")
		return
	}

	var req dto.MediaFolderUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	result, err := h.folderService.UpdateFolder(c.Request.Context(), uint(id), &req)
	if err != nil {
		if services.IsServiceError(err, "FOLDER_NOT_FOUND") {
			response.NotFound(c.Writer, "Folder not found")
			return
		}
		if services.IsServiceError(err, "SYSTEM_FOLDER") {
			response.Forbidden(c.Writer, "Cannot modify system folder")
			return
		}
		
		h.logger.WithError(err).Error("Failed to update folder")
		response.InternalServerErrorWithContext(c, "Failed to update folder")
		return
	}

	response.SuccessWithContext(c, result)
}

// DeleteFolder godoc
// @Summary      Delete media folder
// @Description  Deletes a media folder
// @Tags         media
// @Produce      json
// @Param        id path int true "Folder ID"
// @Success      204 {object} response.Response
// @Failure      404 {object} response.Response
// @Failure      409 {object} response.Response
// @Router       /api/cms/v1/media/folders/{id} [delete]
func (h *MediaFolderHandler) DeleteFolder(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid folder ID")
		return
	}

	err = h.folderService.DeleteFolder(c.Request.Context(), uint(id))
	if err != nil {
		if services.IsServiceError(err, "FOLDER_NOT_FOUND") {
			response.NotFound(c.Writer, "Folder not found")
			return
		}
		if services.IsServiceError(err, "FOLDER_NOT_EMPTY") {
			response.Conflict(c.Writer, "Folder is not empty")
			return
		}
		if services.IsServiceError(err, "SYSTEM_FOLDER") {
			response.Forbidden(c.Writer, "Cannot delete system folder")
			return
		}
		
		h.logger.WithError(err).Error("Failed to delete folder")
		response.InternalServerErrorWithContext(c, "Failed to delete folder")
		return
	}

	response.SuccessWithContext(c, nil)
}

// GetFolderTree godoc
// @Summary      Get folder tree
// @Description  Gets the folder tree structure
// @Tags         media
// @Produce      json
// @Param        max_depth query int false "Maximum tree depth"
// @Success      200 {object} response.Response{data=[]dto.MediaFolderTreeResponse}
// @Router       /api/cms/v1/media/folders/tree [get]
func (h *MediaFolderHandler) GetFolderTree(c *gin.Context) {
	// Get tenant and website from context
	tenantID := c.GetUint("tenant_id")
	websiteID := c.GetUint("website_id")

	// Parse max depth
	maxDepth := 0
	if depthStr := c.Query("max_depth"); depthStr != "" {
		if depth, err := strconv.Atoi(depthStr); err == nil && depth > 0 {
			maxDepth = depth
		}
	}

	tree, err := h.folderService.GetFolderTree(c.Request.Context(), tenantID, websiteID, maxDepth)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get folder tree")
		response.InternalServerErrorWithContext(c, "Failed to get folder tree")
		return
	}

	response.SuccessWithContext(c, tree)
}

// MoveFolder godoc
// @Summary      Move media folder
// @Description  Moves a folder to a new parent
// @Tags         media
// @Accept       json
// @Produce      json
// @Param        id path int true "Folder ID"
// @Param        body body dto.MediaFolderMoveRequest true "Move data"
// @Success      200 {object} response.Response{data=dto.MediaFolderResponse}
// @Failure      400 {object} response.Response
// @Failure      404 {object} response.Response
// @Router       /api/cms/v1/media/folders/{id}/move [post]
func (h *MediaFolderHandler) MoveFolder(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid folder ID")
		return
	}

	var req dto.MediaFolderMoveRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	result, err := h.folderService.MoveFolder(c.Request.Context(), uint(id), &req)
	if err != nil {
		if services.IsServiceError(err, "FOLDER_NOT_FOUND") {
			response.NotFound(c.Writer, "Folder not found")
			return
		}
		if services.IsServiceError(err, "CIRCULAR_REFERENCE") {
			response.BadRequestWithContext(c, "Circular reference detected")
			return
		}
		if services.IsServiceError(err, "SYSTEM_FOLDER") {
			response.Forbidden(c.Writer, "Cannot move system folder")
			return
		}
		
		h.logger.WithError(err).Error("Failed to move folder")
		response.InternalServerErrorWithContext(c, "Failed to move folder")
		return
	}

	response.SuccessWithContext(c, result)
}

// ListFolders godoc
// @Summary      List media folders
// @Description  Lists media folders with filtering and pagination
// @Tags         media
// @Produce      json
// @Param        parent_id query int false "Filter by parent ID"
// @Param        search query string false "Search query"
// @Param        include_files query bool false "Include file information"
// @Param        cursor query string false "Pagination cursor"
// @Param        limit query int false "Items per page"
// @Success      200 {object} response.Response{data=[]dto.MediaFolderResponse,meta=pagination.CursorResponse}
// @Router       /api/cms/v1/media/folders [get]
func (h *MediaFolderHandler) ListFolders(c *gin.Context) {
	// Get tenant and website from context
	tenantID := c.GetUint("tenant_id")
	websiteID := c.GetUint("website_id")

	// Build filter
	filter := &dto.MediaFolderFilter{
		TenantID:     tenantID,
		WebsiteID:    websiteID,
		SearchQuery:  c.Query("search"),
		IncludeFiles: c.Query("include_files") == "true",
		IncludeTree:  c.Query("include_tree") == "true",
	}

	// Parse parent ID
	if parentIDStr := c.Query("parent_id"); parentIDStr != "" {
		parentID, err := strconv.ParseUint(parentIDStr, 10, 32)
		if err == nil {
			parentIDUint := uint(parentID)
			filter.ParentID = &parentIDUint
		}
	}

	// Parse pagination
	cursorReq := pagination.NewCursorRequest(
		c.Query("cursor"),
		pagination.ParseLimitFromString(c.Query("limit")),
	)

	folders, cursorResp, err := h.folderService.ListFolders(c.Request.Context(), filter, cursorReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list folders")
		response.InternalServerErrorWithContext(c, "Failed to list folders")
		return
	}

	response.CursorPaginatedWithContext(c, folders, *cursorResp)
}

// GetFolderStats godoc
// @Summary      Get folder statistics
// @Description  Gets statistics for a folder
// @Tags         media
// @Produce      json
// @Param        id path int true "Folder ID"
// @Success      200 {object} response.Response{data=dto.FolderStatsResponse}
// @Failure      404 {object} response.Response
// @Router       /api/cms/v1/media/folders/{id}/stats [get]
func (h *MediaFolderHandler) GetFolderStats(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid folder ID")
		return
	}

	stats, err := h.folderService.GetFolderStats(c.Request.Context(), uint(id))
	if err != nil {
		if services.IsServiceError(err, "FOLDER_NOT_FOUND") {
			response.NotFound(c.Writer, "Folder not found")
			return
		}
		
		h.logger.WithError(err).Error("Failed to get folder stats")
		response.InternalServerErrorWithContext(c, "Failed to get folder stats")
		return
	}

	response.SuccessWithContext(c, stats)
}

// CreateSystemFolders - Stub implementation
func (h *MediaFolderHandler) CreateSystemFolders(c *gin.Context) {
	_ = response.NewResponse(c.Writer).Error(http.StatusNotImplemented, "Create system folders not implemented")
}

// GetSystemFolders - Stub implementation
func (h *MediaFolderHandler) GetSystemFolders(c *gin.Context) {
	_ = response.NewResponse(c.Writer).Error(http.StatusNotImplemented, "Get system folders not implemented")
}