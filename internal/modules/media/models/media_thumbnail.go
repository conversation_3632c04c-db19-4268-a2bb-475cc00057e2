package models

import (
	"time"
)

type ThumbnailSize string
type ThumbnailStatus string

const (
	ThumbnailSizeSmall  ThumbnailSize = "small"
	ThumbnailSizeMedium ThumbnailSize = "medium"
	ThumbnailSizeLarge  ThumbnailSize = "large"
	ThumbnailSizeCustom ThumbnailSize = "custom"

	ThumbnailStatusPending    ThumbnailStatus = "pending"
	ThumbnailStatusProcessing ThumbnailStatus = "processing"
	ThumbnailStatusReady      ThumbnailStatus = "ready"
	ThumbnailStatusFailed     ThumbnailStatus = "failed"
)

type MediaThumbnail struct {
	ID           uint            `gorm:"primaryKey" json:"id"`
	FileID       uint            `gorm:"not null;index" json:"file_id"`
	Size         ThumbnailSize   `gorm:"not null" json:"size"`
	Width        int             `gorm:"not null" json:"width"`
	Height       int             `gorm:"not null" json:"height"`
	Format       string          `gorm:"not null" json:"format"`
	Quality      int             `gorm:"default:80" json:"quality"`
	StoragePath  string          `gorm:"not null" json:"storage_path"`
	PublicURL    string          `gorm:"not null" json:"public_url"`
	FileSize     int64           `gorm:"not null" json:"file_size"`
	Status       ThumbnailStatus `gorm:"default:'pending'" json:"status"`
	ErrorMessage string          `gorm:"type:text" json:"error_message,omitempty"`
	GeneratedAt  *time.Time      `json:"generated_at"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`

	// Relationships
	File *MediaFile `gorm:"foreignKey:FileID" json:"file,omitempty"`
}

func (MediaThumbnail) TableName() string {
	return "media_thumbnails"
}

func (t *MediaThumbnail) IsReady() bool {
	return t.Status == ThumbnailStatusReady
}