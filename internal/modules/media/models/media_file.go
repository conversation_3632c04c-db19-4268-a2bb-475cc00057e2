package models

import (
	"encoding/json"
	"time"
)

type FileType string
type StorageType string
type VisibilityType string
type FileStatus string

const (
	FileTypeImage    FileType = "image"
	FileTypeVideo    FileType = "video"
	FileTypeAudio    FileType = "audio"
	FileTypeDocument FileType = "document"
	FileTypeArchive  FileType = "archive"
	FileTypeOther    FileType = "other"

	StorageTypeLocal StorageType = "local"
	StorageTypeMinio StorageType = "minio"
	StorageTypeS3    StorageType = "s3"
	StorageTypeGCS   StorageType = "gcs"

	VisibilityPublic  VisibilityType = "public"
	VisibilityPrivate VisibilityType = "private"
	VisibilityShared  VisibilityType = "shared"

	FileStatusUploading  FileStatus = "uploading"
	FileStatusProcessing FileStatus = "processing"
	FileStatusReady      FileStatus = "ready"
	FileStatusError      FileStatus = "error"
	FileStatusDeleted    FileStatus = "deleted"
)

type MediaFile struct {
	ID               uint            `gorm:"primaryKey" json:"id"`
	TenantID         uint            `gorm:"not null;index" json:"tenant_id"`
	WebsiteID        uint            `gorm:"not null;index" json:"website_id"`
	FolderID         *uint           `gorm:"index" json:"folder_id"`
	UserID           uint            `gorm:"not null;index" json:"user_id"`
	Filename         string          `gorm:"not null" json:"filename"`
	OriginalFilename string          `gorm:"not null" json:"original_filename"`
	Slug             string          `gorm:"not null;uniqueIndex:idx_tenant_website_slug" json:"slug"`
	MimeType         string          `gorm:"not null" json:"mime_type"`
	FileSize         int64           `gorm:"not null" json:"file_size"`
	FileHash         string          `gorm:"not null;index" json:"file_hash"`
	StorageType      StorageType     `gorm:"default:'local'" json:"storage_type"`
	StoragePath      string          `gorm:"not null" json:"storage_path"`
	PublicURL        string          `gorm:"not null" json:"public_url"`
	Width            *int            `json:"width"`
	Height           *int            `json:"height"`
	Duration         *int            `json:"duration"`
	Metadata         json.RawMessage `gorm:"type:json;default:'{}'" json:"metadata"`
	FileType         FileType        `gorm:"not null" json:"file_type"`
	Category         string          `gorm:"default:'general'" json:"category"`
	AltText          string          `json:"alt_text"`
	Title            string          `json:"title"`
	Description      string          `gorm:"type:text" json:"description"`
	Visibility       VisibilityType  `gorm:"default:'public'" json:"visibility"`
	AccessPermissions json.RawMessage `gorm:"type:json;default:'[]'" json:"access_permissions"`
	ViewCount        int             `gorm:"default:0" json:"view_count"`
	DownloadCount    int             `gorm:"default:0" json:"download_count"`
	LastAccessedAt   *time.Time      `json:"last_accessed_at"`
	Status           FileStatus      `gorm:"default:'uploading'" json:"status"`
	ProcessingStatus json.RawMessage `gorm:"type:json;default:'{}'" json:"processing_status"`
	ErrorMessage     string          `gorm:"type:text" json:"error_message,omitempty"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`

	// Relationships
	Folder     *MediaFolder      `gorm:"foreignKey:FolderID" json:"folder,omitempty"`
	Tags       []MediaTag        `gorm:"many2many:media_file_tags;" json:"tags,omitempty"`
	Thumbnails []MediaThumbnail  `gorm:"foreignKey:FileID" json:"thumbnails,omitempty"`
}

func (MediaFile) TableName() string {
	return "media_files"
}

func (m *MediaFile) IsImage() bool {
	return m.FileType == FileTypeImage
}

func (m *MediaFile) IsVideo() bool {
	return m.FileType == FileTypeVideo
}

func (m *MediaFile) IsAudio() bool {
	return m.FileType == FileTypeAudio
}

func (m *MediaFile) IsDocument() bool {
	return m.FileType == FileTypeDocument
}

func (m *MediaFile) IsReady() bool {
	return m.Status == FileStatusReady
}

func (m *MediaFile) IsPublic() bool {
	return m.Visibility == VisibilityPublic
}

func (m *MediaFile) CanBeAccessed(userID uint) bool {
	if m.IsPublic() {
		return true
	}
	if m.UserID == userID {
		return true
	}
	// Additional access permission logic can be implemented here
	return false
}