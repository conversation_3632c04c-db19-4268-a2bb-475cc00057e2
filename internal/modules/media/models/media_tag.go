package models

import (
	"time"
)

type MediaTag struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	TenantID    uint      `gorm:"not null;index;uniqueIndex:idx_tenant_name" json:"tenant_id"`
	Name        string    `gorm:"not null;uniqueIndex:idx_tenant_name" json:"name"`
	Slug        string    `gorm:"not null;uniqueIndex:idx_tenant_slug" json:"slug"`
	Description string    `gorm:"type:text" json:"description"`
	Color       string    `json:"color"`
	UsageCount  int       `gorm:"default:0" json:"usage_count"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// Relationships
	Files []MediaFile `gorm:"many2many:media_file_tags;" json:"files,omitempty"`
}

func (MediaTag) TableName() string {
	return "media_tags"
}

type MediaFileTag struct {
	FileID    uint      `gorm:"primaryKey" json:"file_id"`
	TagID     uint      `gorm:"primaryKey" json:"tag_id"`
	CreatedAt time.Time `json:"created_at"`
}

func (MediaFileTag) TableName() string {
	return "media_file_tags"
}