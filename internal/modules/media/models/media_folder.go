package models

import (
	"encoding/json"
	"time"
)

type FolderStatus string

const (
	FolderStatusActive  FolderStatus = "active"
	FolderStatusDeleted FolderStatus = "deleted"
)

type MediaFolder struct {
	ID          uint            `gorm:"primaryKey" json:"id"`
	TenantID    uint            `gorm:"not null;index" json:"tenant_id"`
	WebsiteID   uint            `gorm:"not null;index" json:"website_id"`
	ParentID    *uint           `gorm:"index" json:"parent_id"`
	Name        string          `gorm:"not null" json:"name"`
	Slug        string          `gorm:"not null;uniqueIndex:idx_tenant_website_parent_slug" json:"slug"`
	Path        string          `gorm:"not null;index" json:"path"`
	Description string          `gorm:"type:text" json:"description"`
	Color       string          `json:"color"`
	Icon        string          `json:"icon"`
	SortOrder   int             `gorm:"default:0" json:"sort_order"`
	IsSystem    bool            `gorm:"default:false" json:"is_system"`
	Settings    json.RawMessage `gorm:"type:json;default:'{}'" json:"settings"`
	Status      FolderStatus    `gorm:"default:'active'" json:"status"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`

	// Relationships
	Parent   *MediaFolder  `gorm:"foreignKey:ParentID" json:"parent,omitempty"`
	Children []MediaFolder `gorm:"foreignKey:ParentID" json:"children,omitempty"`
	Files    []MediaFile   `gorm:"foreignKey:FolderID" json:"files,omitempty"`
}

func (MediaFolder) TableName() string {
	return "media_folders"
}

func (f *MediaFolder) IsActive() bool {
	return f.Status == FolderStatusActive
}

func (f *MediaFolder) IsRoot() bool {
	return f.ParentID == nil
}

func (f *MediaFolder) GetFullPath() string {
	if f.Path == "" {
		return f.Slug
	}
	return f.Path + "/" + f.Slug
}