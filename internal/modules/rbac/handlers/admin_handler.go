package handlers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

// AdminHandler handles admin-related RBAC operations
type AdminHandler struct {
	roleService       services.RoleService
	permissionService services.PermissionService
	userRoleService   services.UserRoleService
	rbacEngine        services.RBACEngine
}

// NewAdminHandler creates a new admin handler
func NewAdminHandler(
	roleService services.RoleService,
	permissionService services.PermissionService,
	userRoleService services.UserRoleService,
	rbacEngine services.RBACEngine,
) *AdminHandler {
	return &AdminHandler{
		roleService:       roleService,
		permissionService: permissionService,
		userRoleService:   userRoleService,
		rbacEngine:        rbacEngine,
	}
}

// GetSystemStats returns system-wide RBAC statistics
func (h *AdminHandler) GetSystemStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}

	// Get role statistics
	allRoles, _, err := h.roleService.GetRolesByTenant(c.Request.Context(), tenantID.(uint), 1, 1000)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get role statistics")
		return
	}

	roleStats := map[string]int{
		"total":    len(allRoles),
		"active":   0,
		"inactive": 0,
		"system":   0,
		"custom":   0,
	}

	for _, role := range allRoles {
		if role.Status == "active" {
			roleStats["active"]++
		} else {
			roleStats["inactive"]++
		}

		if role.IsSystemRole {
			roleStats["system"]++
		} else {
			roleStats["custom"]++
		}
	}

	// Get permission statistics
	allPermissions, err := h.permissionService.GetActivePermissions(c.Request.Context())
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get permission statistics")
		return
	}

	permissionStats := map[string]int{
		"total":  len(allPermissions),
		"system": 0,
		"custom": 0,
	}

	riskLevelStats := map[string]int{
		"low":      0,
		"medium":   0,
		"high":     0,
		"critical": 0,
	}

	for _, perm := range allPermissions {
		if perm.IsSystemPermission {
			permissionStats["system"]++
		} else {
			permissionStats["custom"]++
		}

		riskLevelStats[string(perm.RiskLevel)]++
	}

	// Get cache statistics
	cacheStats, err := h.rbacEngine.GetCacheStats(c.Request.Context())
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get cache statistics")
		return
	}

	// Get unused permissions
	unusedPermissions, err := h.permissionService.GetUnusedPermissions(c.Request.Context())
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get unused permissions")
		return
	}

	responseData := map[string]interface{}{
		"tenant_id": tenantID,
		"timestamp": time.Now(),
		"stats": map[string]interface{}{
			"roles":              roleStats,
			"permissions":        permissionStats,
			"risk_levels":        riskLevelStats,
			"cache":              cacheStats,
			"unused_permissions": len(unusedPermissions),
		},
	}

	response.SuccessWithContext(c, responseData)
}

// GetRoleHierarchy returns role hierarchy visualization data
func (h *AdminHandler) GetRoleHierarchy(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}

	// Get role hierarchy
	roles, err := h.roleService.GetRoleHierarchy(c.Request.Context(), tenantID.(uint))
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get role hierarchy")
		return
	}

	// Build hierarchy visualization data
	type HierarchyNode struct {
		ID          uint             `json:"id"`
		Name        string           `json:"name"`
		DisplayName string           `json:"display_name"`
		Level       int              `json:"level"`
		IsSystem    bool             `json:"is_system"`
		Status      string           `json:"status"`
		UserCount   int64            `json:"user_count"`
		Children    []*HierarchyNode `json:"children,omitempty"`
	}

	// Create nodes map
	nodes := make(map[uint]*HierarchyNode)
	for _, role := range roles {
		// Get user count for this role
		userCount, err := h.roleService.GetRoleUserCount(c.Request.Context(), role.ID)
		if err != nil {
			userCount = 0
		}

		nodes[role.ID] = &HierarchyNode{
			ID:          role.ID,
			Name:        role.Name,
			DisplayName: role.DisplayName,
			Level:       int(role.Level),
			IsSystem:    role.IsSystemRole,
			Status:      string(role.Status),
			UserCount:   userCount,
			Children:    make([]*HierarchyNode, 0),
		}
	}

	// Build hierarchy (simplified - in real implementation, you'd use parent-child relationships)
	var rootNodes []*HierarchyNode
	for _, node := range nodes {
		if node.Level == 0 {
			rootNodes = append(rootNodes, node)
		}
	}

	responseData := map[string]interface{}{
		"tenant_id": tenantID,
		"hierarchy": rootNodes,
	}
	response.SuccessWithContext(c, responseData)
}

// GetPermissionMatrix returns permission matrix for roles
func (h *AdminHandler) GetPermissionMatrix(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}

	// Get all roles
	roles, _, err := h.roleService.GetRolesByTenant(c.Request.Context(), tenantID.(uint), 1, 1000)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get roles")
		return
	}

	// Get all permissions
	permissions, err := h.permissionService.GetActivePermissions(c.Request.Context())
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get permissions")
		return
	}

	// Build permission matrix
	matrix := make(map[uint]map[uint]bool)
	for _, role := range roles {
		matrix[role.ID] = make(map[uint]bool)

		// Get role permissions
		rolePermissions, err := h.roleService.GetRolePermissions(c.Request.Context(), role.ID)
		if err != nil {
			continue
		}

		// Mark permissions as granted
		for _, perm := range rolePermissions {
			matrix[role.ID][perm.ID] = true
		}

		// Mark missing permissions as false
		for _, perm := range permissions {
			if _, exists := matrix[role.ID][perm.ID]; !exists {
				matrix[role.ID][perm.ID] = false
			}
		}
	}

	responseData := map[string]interface{}{
		"tenant_id":   tenantID,
		"roles":       roles,
		"permissions": permissions,
		"matrix":      matrix,
	}
	response.SuccessWithContext(c, responseData)
}

// GetRoleAnalytics returns role usage analytics
func (h *AdminHandler) GetRoleAnalytics(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}

	// Get all roles
	roles, _, err := h.roleService.GetRolesByTenant(c.Request.Context(), tenantID.(uint), 1, 1000)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get roles")
		return
	}

	// Build analytics data
	type RoleAnalytics struct {
		RoleID      uint                   `json:"role_id"`
		Name        string                 `json:"name"`
		DisplayName string                 `json:"display_name"`
		UserCount   int64                  `json:"user_count"`
		UsageStats  map[string]interface{} `json:"usage_stats"`
	}

	var analytics []*RoleAnalytics
	for _, role := range roles {
		// Get usage stats
		usageStats, err := h.userRoleService.GetRoleUsageStats(c.Request.Context(), role.ID)
		if err != nil {
			usageStats = make(map[string]interface{})
		}

		analytics = append(analytics, &RoleAnalytics{
			RoleID:      role.ID,
			Name:        role.Name,
			DisplayName: role.DisplayName,
			UserCount:   usageStats["total_users"].(int64),
			UsageStats:  usageStats,
		})
	}

	responseData := map[string]interface{}{
		"tenant_id": tenantID,
		"analytics": analytics,
	}
	response.SuccessWithContext(c, responseData)
}

// GetPermissionAnalytics returns permission usage analytics
func (h *AdminHandler) GetPermissionAnalytics(c *gin.Context) {
	// Get all permissions
	permissions, err := h.permissionService.GetActivePermissions(c.Request.Context())
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get permissions")
		return
	}

	// Build analytics data
	type PermissionAnalytics struct {
		PermissionID uint   `json:"permission_id"`
		Name         string `json:"name"`
		DisplayName  string `json:"display_name"`
		Module       string `json:"module"`
		RiskLevel    string `json:"risk_level"`
		UsageCount   int64  `json:"usage_count"`
	}

	var analytics []*PermissionAnalytics
	for _, perm := range permissions {
		// Get usage count
		usageCount, err := h.permissionService.GetPermissionUsageCount(c.Request.Context(), perm.ID)
		if err != nil {
			usageCount = 0
		}

		analytics = append(analytics, &PermissionAnalytics{
			PermissionID: perm.ID,
			Name:         perm.Name,
			DisplayName:  perm.DisplayName,
			Module:       perm.Module,
			RiskLevel:    string(perm.RiskLevel),
			UsageCount:   usageCount,
		})
	}

	responseData := map[string]interface{}{
		"analytics": analytics,
	}
	response.SuccessWithContext(c, responseData)
}

// CleanupExpiredRoles removes expired roles
func (h *AdminHandler) CleanupExpiredRoles(c *gin.Context) {
	// Get current time
	now := time.Now()

	// Cleanup expired roles
	count, err := h.userRoleService.CleanupExpiredRoles(c.Request.Context())
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to cleanup expired roles")
		return
	}

	responseData := map[string]interface{}{
		"message":       "Expired roles cleaned up successfully",
		"cleaned_count": count,
		"timestamp":     now,
	}
	response.SuccessWithContext(c, responseData)
}

// CleanupRevokedRoles removes old revoked roles
func (h *AdminHandler) CleanupRevokedRoles(c *gin.Context) {
	// Get days parameter (default 30 days)
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 {
		days = 30
	}

	// Calculate cutoff time
	cutoff := time.Now().AddDate(0, 0, -days)

	// Cleanup old revoked roles
	count, err := h.userRoleService.CleanupRevokedRoles(c.Request.Context(), cutoff)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to cleanup revoked roles")
		return
	}

	responseData := map[string]interface{}{
		"message":       "Revoked roles cleaned up successfully",
		"cleaned_count": count,
		"cutoff_date":   cutoff,
		"days_old":      days,
	}
	response.SuccessWithContext(c, responseData)
}

// GetExpiredRoles returns expired roles
func (h *AdminHandler) GetExpiredRoles(c *gin.Context) {
	// Get current time
	now := time.Now()

	// Get expired roles
	expiredRoles, err := h.userRoleService.GetExpiredRoles(c.Request.Context(), now)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get expired roles")
		return
	}

	responseData := map[string]interface{}{
		"timestamp":     now,
		"expired_roles": expiredRoles,
		"count":         len(expiredRoles),
	}
	response.SuccessWithContext(c, responseData)
}

// GetAuditLog returns RBAC audit log (simplified version)
func (h *AdminHandler) GetAuditLog(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "50"))
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 50
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}

	// For now, return a simplified audit log
	// In a real implementation, you would have an audit service
	auditLog := []gin.H{
		{
			"id":          1,
			"tenant_id":   tenantID,
			"action":      "role_created",
			"resource":    "role",
			"resource_id": 1,
			"user_id":     1,
			"timestamp":   time.Now().Add(-time.Hour),
			"details":     gin.H{"role_name": "admin"},
		},
		{
			"id":          2,
			"tenant_id":   tenantID,
			"action":      "permission_granted",
			"resource":    "role_permission",
			"resource_id": 1,
			"user_id":     1,
			"timestamp":   time.Now().Add(-time.Hour * 2),
			"details":     gin.H{"role_id": 1, "permission_id": 1},
		},
	}

	responseData := map[string]interface{}{
		"data": auditLog,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       len(auditLog),
			"total_pages": 1,
		},
	}
	response.SuccessWithContext(c, responseData)
}

// ExportRoleConfiguration exports role configuration
func (h *AdminHandler) ExportRoleConfiguration(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}

	// Get all roles
	roles, _, err := h.roleService.GetRolesByTenant(c.Request.Context(), tenantID.(uint), 1, 1000)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get roles")
		return
	}

	// Get all permissions
	permissions, err := h.permissionService.GetActivePermissions(c.Request.Context())
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get permissions")
		return
	}

	// Build export data
	exportData := gin.H{
		"tenant_id":   tenantID,
		"timestamp":   time.Now(),
		"version":     "1.0",
		"roles":       roles,
		"permissions": permissions,
	}

	// Set response headers for download
	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", "attachment; filename=rbac_config.json")
	response.SuccessWithContext(c, exportData)
}

// ImportRoleConfiguration imports role configuration
func (h *AdminHandler) ImportRoleConfiguration(c *gin.Context) {
	// Parse request body
	var req struct {
		Version     string        `json:"version"`
		Roles       []interface{} `json:"roles"`
		Permissions []interface{} `json:"permissions"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, err)
		return
	}

	// Validate version
	if req.Version != "1.0" {
		response.BadRequestWithContext(c, "Unsupported configuration version")
		return
	}

	// For now, return success message
	// In a real implementation, you would process the import
	responseData := map[string]interface{}{
		"message":              "Configuration imported successfully",
		"imported_roles":       len(req.Roles),
		"imported_permissions": len(req.Permissions),
	}
	response.SuccessWithContext(c, responseData)
}

// ValidateRoleConfiguration validates role configuration
func (h *AdminHandler) ValidateRoleConfiguration(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}

	// Get all roles
	roles, _, err := h.roleService.GetRolesByTenant(c.Request.Context(), tenantID.(uint), 1, 1000)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get roles")
		return
	}

	// Validate configuration
	issues := []string{}
	warnings := []string{}

	// Check for roles without permissions
	for _, role := range roles {
		permissions, err := h.roleService.GetRolePermissions(c.Request.Context(), role.ID)
		if err != nil {
			continue
		}

		if len(permissions) == 0 {
			warnings = append(warnings, "Role '"+role.Name+"' has no permissions assigned")
		}
	}

	// Check for unused permissions
	unusedPermissions, err := h.permissionService.GetUnusedPermissions(c.Request.Context())
	if err == nil && len(unusedPermissions) > 0 {
		warnings = append(warnings, "Found "+strconv.Itoa(len(unusedPermissions))+" unused permissions")
	}

	// Return validation results
	responseData := map[string]interface{}{
		"valid":     len(issues) == 0,
		"issues":    issues,
		"warnings":  warnings,
		"tenant_id": tenantID,
		"timestamp": time.Now(),
	}
	response.SuccessWithContext(c, responseData)
}
