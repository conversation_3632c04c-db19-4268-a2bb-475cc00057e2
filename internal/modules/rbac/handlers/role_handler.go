package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

// RoleHandler handles role-related HTTP requests
type RoleHandler struct {
	roleService services.RoleService
	rbacEngine  services.RBACEngine
}

// NewRoleHandler creates a new role handler
func NewRoleHandler(roleService services.RoleService, rbacEngine services.RBACEngine) *RoleHandler {
	return &RoleHandler{
		roleService: roleService,
		rbacEngine:  rbacEngine,
	}
}

// CreateRole creates a new role
func (h *RoleHandler) CreateRole(c *gin.Context) {
	// Parse and validate request body
	var req dto.CreateRoleDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Convert DTO to service request
	serviceReq := &models.RoleCreateRequest{
		TenantID:      tenantID.(uint),
		Name:          req.Name,
		DisplayName:   req.DisplayName,
		Description:   req.Description,
		Color:         req.Color,
		IsSystemRole:  req.IsSystemRole,
		IsDefaultRole: req.IsDefaultRole,
		Level:         req.Level,
		Scope:         models.RoleScope(req.Scope),
		ContextID:     req.ContextID,
		Icon:          req.Icon,
		Capabilities:  models.RoleCapabilities(req.Capabilities),
		Restrictions:  models.RoleRestrictions(req.Restrictions),
	}

	// Create role
	role, err := h.roleService.CreateRole(c.Request.Context(), serviceReq)
	if err != nil {
		if err == services.ErrRoleAlreadyExists {
			response.Conflict(c.Writer, "Role already exists")
			return
		}
		response.InternalServerError(c.Writer, "Failed to create role")
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	// Convert to response DTO
	responseData := &dto.RoleResponseDTO{
		ID:            role.ID,
		TenantID:      role.TenantID,
		Name:          role.Name,
		DisplayName:   role.DisplayName,
		Description:   role.Description,
		Color:         role.Color,
		IsSystemRole:  role.IsSystemRole,
		IsDefaultRole: role.IsDefaultRole,
		Level:         role.Level,
		Scope:         string(role.Scope),
		ContextID:     role.ContextID,
		Icon:          role.Icon,
		Capabilities:  []string(role.Capabilities),
		Restrictions:  map[string]interface{}(role.Restrictions),
		Status:        string(role.Status),
		CreatedAt:     role.CreatedAt,
		UpdatedAt:     role.UpdatedAt,
	}

	response.Created(c.Writer, responseData)
}

// GetRole retrieves a role by ID
func (h *RoleHandler) GetRole(c *gin.Context) {
	// Parse role ID from URL parameter
	var params dto.IDParamDTO
	if err := c.ShouldBindUri(&params); err != nil {
		response.BadRequest(c.Writer, "Invalid role ID")
		return
	}

	role, err := h.roleService.GetRole(c.Request.Context(), params.ID)
	if err != nil {
		if err == services.ErrRoleNotFound {
			response.NotFound(c.Writer, "Role not found")
			return
		}
		response.InternalServerError(c.Writer, "Failed to get role")
		return
	}

	// Convert to response DTO
	responseData := &dto.RoleResponseDTO{
		ID:            role.ID,
		TenantID:      role.TenantID,
		Name:          role.Name,
		DisplayName:   role.DisplayName,
		Description:   role.Description,
		Color:         role.Color,
		IsSystemRole:  role.IsSystemRole,
		IsDefaultRole: role.IsDefaultRole,
		Level:         role.Level,
		Scope:         string(role.Scope),
		ContextID:     role.ContextID,
		Icon:          role.Icon,
		Capabilities:  []string(role.Capabilities),
		Restrictions:  map[string]interface{}(role.Restrictions),
		Status:        string(role.Status),
		CreatedAt:     role.CreatedAt,
		UpdatedAt:     role.UpdatedAt,
	}

	response.Success(c.Writer, responseData)
}

// UpdateRole updates a role
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	// Parse role ID from URL parameter
	var params dto.IDParamDTO
	if err := c.ShouldBindUri(&params); err != nil {
		response.BadRequest(c.Writer, "Invalid role ID")
		return
	}

	// Parse and validate request body
	var req dto.UpdateRoleDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert DTO to service request
	serviceReq := &models.RoleUpdateRequest{
		Name:        &req.Name,
		Description: &req.Description,
		Color:       &req.Color,
	}

	// Update role
	role, err := h.roleService.UpdateRole(c.Request.Context(), params.ID, serviceReq)
	if err != nil {
		if err == services.ErrRoleNotFound {
			response.NotFound(c.Writer, "Role not found")
			return
		}
		response.InternalServerError(c.Writer, "Failed to update role")
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	// Convert to response DTO
	responseData := &dto.RoleResponseDTO{
		ID:            role.ID,
		TenantID:      role.TenantID,
		Name:          role.Name,
		DisplayName:   role.DisplayName,
		Description:   role.Description,
		Color:         role.Color,
		IsSystemRole:  role.IsSystemRole,
		IsDefaultRole: role.IsDefaultRole,
		Level:         role.Level,
		Scope:         string(role.Scope),
		ContextID:     role.ContextID,
		Icon:          role.Icon,
		Capabilities:  []string(role.Capabilities),
		Restrictions:  map[string]interface{}(role.Restrictions),
		Status:        string(role.Status),
		CreatedAt:     role.CreatedAt,
		UpdatedAt:     role.UpdatedAt,
	}

	response.Success(c.Writer, responseData)
}

// DeleteRole deletes a role
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	// Parse role ID from URL parameter
	var params dto.IDParamDTO
	if err := c.ShouldBindUri(&params); err != nil {
		response.BadRequest(c.Writer, "Invalid role ID")
		return
	}

	err := h.roleService.DeleteRole(c.Request.Context(), params.ID)
	if err != nil {
		if err == services.ErrRoleNotFound {
			response.NotFound(c.Writer, "Role not found")
			return
		}
		if err == services.ErrCannotDeleteSystemRole {
			response.Forbidden(c.Writer, "Cannot delete system role")
			return
		}
		if err == services.ErrRoleInUse {
			response.Conflict(c.Writer, "Role is in use and cannot be deleted")
			return
		}
		response.InternalServerError(c.Writer, "Failed to delete role")
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	response.Success(c.Writer, dto.CommonResponseDTO{
		Message: "Role deleted successfully",
	})
}

// ListRoles lists roles with cursor pagination
func (h *RoleHandler) ListRoles(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse query parameters
	var query dto.RoleQueryDTO
	if err := c.ShouldBindQuery(&query); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Set default limit if not provided
	if query.Limit == 0 {
		query.Limit = 50
	}

	// Get roles by tenant (using existing service method)
	roles, _, err := h.roleService.GetRolesByTenant(c.Request.Context(), tenantID.(uint), 1, 1000)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get roles")
		return
	}

	// Apply basic filtering if needed
	var filteredRoles []*models.Role
	for _, role := range roles {
		// Basic name filtering if provided
		if query.Name != "" && role.Name != query.Name {
			continue
		}
		// Basic search filtering if provided
		if query.Search != "" && !containsIgnoreCase(role.Name, query.Search) && !containsIgnoreCasePtr(role.Description, query.Search) {
			continue
		}
		filteredRoles = append(filteredRoles, role)
	}

	// Convert to response DTOs
	var responseData []dto.RoleResponseDTO
	for _, role := range filteredRoles {
		responseData = append(responseData, dto.RoleResponseDTO{
			ID:            role.ID,
			TenantID:      role.TenantID,
			Name:          role.Name,
			DisplayName:   role.DisplayName,
			Description:   role.Description,
			Color:         role.Color,
			IsSystemRole:  role.IsSystemRole,
			IsDefaultRole: role.IsDefaultRole,
			Level:         role.Level,
			Scope:         string(role.Scope),
			ContextID:     role.ContextID,
			Icon:          role.Icon,
			Capabilities:  []string(role.Capabilities),
			Restrictions:  map[string]interface{}(role.Restrictions),
			Status:        string(role.Status),
			CreatedAt:     role.CreatedAt,
			UpdatedAt:     role.UpdatedAt,
		})
	}

	response.Success(c.Writer, dto.RoleListResponseDTO{
		Roles: responseData,
	})
}

// GetRolesByScope retrieves roles by scope
func (h *RoleHandler) GetRolesByScope(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	scope := c.Param("scope")
	if scope == "" {
		response.BadRequest(c.Writer, "Scope is required")
		return
	}

	// Validate scope
	var roleScope models.RoleScope
	switch scope {
	case "tenant":
		roleScope = models.RoleScopeTenant
	case "website":
		roleScope = models.RoleScopeWebsite
	case "global":
		roleScope = models.RoleScopeGlobal
	default:
		response.BadRequest(c.Writer, "Invalid scope")
		return
	}

	// Get context ID if provided
	var contextID *uint
	if contextIDStr := c.Query("context_id"); contextIDStr != "" {
		contextIDUint, err := strconv.ParseUint(contextIDStr, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid context ID")
			return
		}
		contextIDVal := uint(contextIDUint)
		contextID = &contextIDVal
	}

	// Get roles by scope
	roles, err := h.roleService.GetRolesByScope(c.Request.Context(), tenantID.(uint), roleScope, contextID)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get roles by scope")
		return
	}

	response.OK(c.Writer, roles)
}

// GetSystemRoles retrieves system roles
func (h *RoleHandler) GetSystemRoles(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	roles, err := h.roleService.GetSystemRoles(c.Request.Context(), tenantID.(uint))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get system roles")
		return
	}

	response.OK(c.Writer, roles)
}

// GetRoleHierarchy retrieves role hierarchy
func (h *RoleHandler) GetRoleHierarchy(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	roles, err := h.roleService.GetRoleHierarchy(c.Request.Context(), tenantID.(uint))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get role hierarchy")
		return
	}

	response.OK(c.Writer, roles)
}

// GetRolePermissions retrieves permissions for a role
func (h *RoleHandler) GetRolePermissions(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid role ID")
		return
	}

	permissions, err := h.roleService.GetRolePermissions(c.Request.Context(), uint(roleID))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get role permissions")
		return
	}

	response.OK(c.Writer, permissions)
}

// AssignPermissionsToRole assigns permissions to a role
func (h *RoleHandler) AssignPermissionsToRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid role ID")
		return
	}

	var req struct {
		PermissionIDs []uint `json:"permission_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	// Get current user ID for audit
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c.Writer, "User not authenticated")
		return
	}

	// Assign permissions
	grantedBy := userID.(uint)
	err = h.roleService.AssignPermissionsToRole(c.Request.Context(), uint(roleID), req.PermissionIDs, &grantedBy)
	if err != nil {
		if err == services.ErrRoleNotFound {
			response.NotFound(c.Writer, "Role not found")
			return
		}
		response.InternalServerError(c.Writer, "Failed to assign permissions")
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	response.Success(c.Writer, dto.CommonResponseDTO{
		Message: "Permissions assigned successfully",
	})
}

// RevokePermissionsFromRole revokes permissions from a role
func (h *RoleHandler) RevokePermissionsFromRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid role ID")
		return
	}

	var req struct {
		PermissionIDs []uint `json:"permission_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get current user ID for audit
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c.Writer, "User not authenticated")
		return
	}

	// Revoke permissions
	revokedBy := userID.(uint)
	err = h.roleService.RevokePermissionsFromRole(c.Request.Context(), uint(roleID), req.PermissionIDs, &revokedBy)
	if err != nil {
		if err == services.ErrRoleNotFound {
			response.NotFound(c.Writer, "Role not found")
			return
		}
		response.InternalServerError(c.Writer, "Failed to revoke permissions")
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	response.Success(c.Writer, dto.CommonResponseDTO{
		Message: "Permissions revoked successfully",
	})
}

// GetRoleUsers retrieves users for a role
func (h *RoleHandler) GetRoleUsers(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid role ID")
		return
	}

	userRoles, err := h.roleService.GetRoleUsers(c.Request.Context(), uint(roleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get role users",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": userRoles,
	})
}

// ActivateRole activates a role
func (h *RoleHandler) ActivateRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	err = h.roleService.ActivateRole(c.Request.Context(), uint(roleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to activate role",
			"details": err.Error(),
		})
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"message": "Role activated successfully",
	})
}

// DeactivateRole deactivates a role
func (h *RoleHandler) DeactivateRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	err = h.roleService.DeactivateRole(c.Request.Context(), uint(roleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to deactivate role",
			"details": err.Error(),
		})
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"message": "Role deactivated successfully",
	})
}

// SearchRoles searches roles
func (h *RoleHandler) SearchRoles(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant ID is required",
		})
		return
	}

	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Search query is required",
		})
		return
	}

	// Build filters (simplified for now)
	filters := &repositories.RoleFilters{
		// Add filter implementation based on query parameters
	}

	roles, err := h.roleService.SearchRoles(c.Request.Context(), tenantID.(uint), query, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to search roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": roles,
	})
}

// containsIgnoreCase checks if a string contains another string (case insensitive)
func containsIgnoreCase(s string, substr string) bool {
	if substr == "" {
		return true
	}
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

// containsIgnoreCasePtr checks if a string pointer contains another string (case insensitive)
func containsIgnoreCasePtr(s *string, substr string) bool {
	if s == nil || substr == "" {
		return true
	}
	return strings.Contains(strings.ToLower(*s), strings.ToLower(substr))
}
