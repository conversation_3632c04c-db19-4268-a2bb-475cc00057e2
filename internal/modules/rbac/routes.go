package rbac

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/config"
	authmysql "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	authservices "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/handlers"
	rbacmysql "github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
	usermysql "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories/mysql"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all RBAC routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger) {
	// Load config for JWT service
	cfg, err := config.Load()
	if err != nil {
		logger.WithError(err).Error("Failed to load config for RBAC module")
		return
	}

	// Initialize auth repositories and services for JWT middleware
	tokenBlacklistRepo := authmysql.NewTokenBlacklistRepository(db)
	tenantMembershipRepo := usermysql.NewTenantMembershipRepository(db, logger)

	// Initialize services for JWT
	tokenBlacklistService := authservices.NewTokenBlacklistService(tokenBlacklistRepo, nil)
	jwtService, err := authservices.NewJWTService(cfg, tokenBlacklistService, tenantMembershipRepo)
	if err != nil {
		logger.WithError(err).Error("Failed to create JWT service for RBAC")
		return
	}

	// Initialize RBAC repositories
	roleRepo := rbacmysql.NewRoleRepository(db)
	permissionRepo := rbacmysql.NewPermissionRepository(db)
	rolePermissionRepo := rbacmysql.NewRolePermissionRepository(db)
	userRoleRepo := rbacmysql.NewUserRoleRepository(db)

	// Initialize RBAC services
	roleService := services.NewRoleService(roleRepo, rolePermissionRepo, userRoleRepo, permissionRepo)
	permissionService := services.NewPermissionService(permissionRepo, rolePermissionRepo, userRoleRepo)
	userRoleService := services.NewUserRoleService(userRoleRepo, roleService, nil) // RBACEngine will be set later
	rbacEngine := services.NewRBACEngine(roleService, permissionService, userRoleRepo, rolePermissionRepo)
	// Create handlers
	roleHandler := handlers.NewRoleHandler(roleService, rbacEngine)
	permissionHandler := handlers.NewPermissionHandler(permissionService, rbacEngine)
	userRoleHandler := handlers.NewUserRoleHandler(userRoleService, rbacEngine)
	permissionCheckHandler := handlers.NewPermissionCheckHandler(rbacEngine)
	adminHandler := handlers.NewAdminHandler(roleService, permissionService, userRoleService, rbacEngine)
	meHandler := handlers.NewMeHandler(userRoleService, roleService, rbacEngine, logger)
	permissionMiddleware := handlers.NewPermissionMiddleware(rbacEngine)

	// RBAC API group với authentication middleware
	rbacGroup := router.Group("/rbac")
	rbacGroup.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	rbacGroup.Use(httpmiddleware.TenantContextMiddleware())

	// Current user endpoints
	rbacGroup.GET("/me/roles", meHandler.GetMyRoles)
	rbacGroup.GET("/me/permissions", meHandler.GetMyPermissions)

	// Permission checking routes
	permissionGroup := rbacGroup.Group("/permissions")
	{
		// Current user permission routes
		permissionGroup.GET("/me/list", permissionCheckHandler.GetCurrentUserPermissions)
		permissionGroup.GET("/me/roles", permissionCheckHandler.GetCurrentUserRoles)
		permissionGroup.POST("/me/refresh", permissionCheckHandler.RefreshUserPermissions)

		// Permission checking
		permissionGroup.GET("/check/:permission", permissionCheckHandler.CheckPermission)
		permissionGroup.POST("/check/bulk", permissionCheckHandler.CheckPermissions)
		permissionGroup.GET("/check/pattern/:pattern", permissionCheckHandler.CheckPermissionPattern)

		// Admin permission checking
		permissionGroup.GET("/users/:userId/check/:permission",
			permissionMiddleware.RequirePermission("rbac.admin.users.check"),
			permissionCheckHandler.CheckUserPermission)
		permissionGroup.POST("/users/check/bulk",
			permissionMiddleware.RequirePermission("rbac.admin.users.check"),
			permissionCheckHandler.CheckUsersPermissions)

		// Cache management
		permissionGroup.GET("/cache/stats",
			permissionMiddleware.RequirePermission("rbac.admin.cache.read"),
			permissionCheckHandler.GetCacheStats)
		permissionGroup.POST("/cache/clear",
			permissionMiddleware.RequirePermission("rbac.admin.cache.clear"),
			permissionCheckHandler.ClearCache)
		permissionGroup.POST("/cache/warmup",
			permissionMiddleware.RequirePermission("rbac.admin.cache.warmup"),
			permissionCheckHandler.WarmupCache)
		permissionGroup.POST("/cache/preload",
			permissionMiddleware.RequirePermission("rbac.admin.cache.preload"),
			permissionCheckHandler.PreloadUserPermissions)
	}

	// Role management routes
	roleGroup := rbacGroup.Group("/roles")
	roleGroup.Use(permissionMiddleware.RequirePermission("rbac.roles.read"))
	{
		// Basic CRUD
		roleGroup.GET("", roleHandler.ListRoles)
		roleGroup.GET("/:id", roleHandler.GetRole)
		roleGroup.POST("", permissionMiddleware.RequirePermission("rbac.roles.create"), roleHandler.CreateRole)
		roleGroup.PUT("/:id", permissionMiddleware.RequirePermission("rbac.roles.update"), roleHandler.UpdateRole)
		roleGroup.DELETE("/:id", permissionMiddleware.RequirePermission("rbac.roles.delete"), roleHandler.DeleteRole)

		// Role queries
		roleGroup.GET("/scope/:scope", roleHandler.GetRolesByScope)
		roleGroup.GET("/system", roleHandler.GetSystemRoles)
		roleGroup.GET("/hierarchy", roleHandler.GetRoleHierarchy)
		roleGroup.GET("/search", roleHandler.SearchRoles)

		// Role permission management
		roleGroup.GET("/:id/permissions", roleHandler.GetRolePermissions)
		roleGroup.POST("/:id/permissions", permissionMiddleware.RequirePermission("rbac.roles.permissions.assign"), roleHandler.AssignPermissionsToRole)
		roleGroup.DELETE("/:id/permissions", permissionMiddleware.RequirePermission("rbac.roles.permissions.revoke"), roleHandler.RevokePermissionsFromRole)

		// Role user management
		roleGroup.GET("/:id/users", roleHandler.GetRoleUsers)

		// Role status management
		roleGroup.POST("/:id/activate", permissionMiddleware.RequirePermission("rbac.roles.update"), roleHandler.ActivateRole)
		roleGroup.POST("/:id/deactivate", permissionMiddleware.RequirePermission("rbac.roles.update"), roleHandler.DeactivateRole)
	}

	// User role management routes
	userRoleGroup := rbacGroup.Group("/users")
	userRoleGroup.Use(permissionMiddleware.RequirePermission("rbac.users.read"))
	{
		// User role assignment
		userRoleGroup.GET("/:id/roles", userRoleHandler.GetUserRoles)
		userRoleGroup.POST("/:id/roles", permissionMiddleware.RequirePermission("rbac.users.roles.assign"), userRoleHandler.AssignRole)
		userRoleGroup.POST("/:id/roles/bulk", permissionMiddleware.RequirePermission("rbac.users.roles.assign"), userRoleHandler.AssignRoles)
		userRoleGroup.POST("/:id/roles/temporary", permissionMiddleware.RequirePermission("rbac.users.roles.assign"), userRoleHandler.AssignTemporaryRole)
		userRoleGroup.DELETE("/:id/roles/:roleId", permissionMiddleware.RequirePermission("rbac.users.roles.revoke"), userRoleHandler.RevokeRole)
		userRoleGroup.DELETE("/:id/user-roles/:userRoleId", permissionMiddleware.RequirePermission("rbac.users.roles.revoke"), userRoleHandler.RevokeUserRole)

		// User permissions
		userRoleGroup.GET("/:id/permissions", userRoleHandler.GetUserPermissions)
		userRoleGroup.GET("/:id/permissions/effective", userRoleHandler.GetUserEffectivePermissions)

		// User role context
		userRoleGroup.GET("/:id/roles/context", userRoleHandler.GetUserRolesByContext)
		userRoleGroup.GET("/:id/roles/primary", userRoleHandler.GetPrimaryRole)
		userRoleGroup.POST("/:id/roles/primary", permissionMiddleware.RequirePermission("rbac.users.roles.assign"), userRoleHandler.SetPrimaryRole)
		userRoleGroup.GET("/:id/roles/temporary", userRoleHandler.GetTemporaryRoles)

		// User role management
		userRoleGroup.PUT("/:id/user-roles/:userRoleId/extend", permissionMiddleware.RequirePermission("rbac.users.roles.update"), userRoleHandler.ExtendRoleValidity)
		userRoleGroup.POST("/:id/user-roles/:userRoleId/suspend", permissionMiddleware.RequirePermission("rbac.users.roles.update"), userRoleHandler.SuspendUserRole)
		userRoleGroup.POST("/:id/user-roles/:userRoleId/reactivate", permissionMiddleware.RequirePermission("rbac.users.roles.update"), userRoleHandler.ReactivateUserRole)

		// User role queries
		userRoleGroup.GET("/:id/roles/history", userRoleHandler.GetUserRoleHistory)
		userRoleGroup.GET("/search", userRoleHandler.SearchUserRoles)

		// Role-based user queries
		userRoleGroup.GET("/roles/:roleId/users", userRoleHandler.GetUsersByRole)
		userRoleGroup.GET("/roles/:roleId/stats", userRoleHandler.GetRoleUsageStats)
	}

	// Admin routes (super admin only)
	adminGroup := rbacGroup.Group("/admin")
	adminGroup.Use(permissionMiddleware.RequirePermission("rbac.admin.read"))
	{
		// System management
		adminGroup.GET("/stats", adminHandler.GetSystemStats)
		adminGroup.GET("/hierarchy", adminHandler.GetRoleHierarchy)
		adminGroup.GET("/matrix", adminHandler.GetPermissionMatrix)
		adminGroup.GET("/analytics/roles", adminHandler.GetRoleAnalytics)
		adminGroup.GET("/analytics/permissions", adminHandler.GetPermissionAnalytics)
		adminGroup.GET("/expired-roles", adminHandler.GetExpiredRoles)
		adminGroup.GET("/audit-log", adminHandler.GetAuditLog)

		// Permission management
		adminGroup.GET("/permissions", permissionHandler.ListPermissions)
		adminGroup.GET("/permissions/:id", permissionHandler.GetPermission)
		adminGroup.GET("/permissions/name/:name", permissionHandler.GetPermissionByName)
		adminGroup.POST("/permissions", permissionMiddleware.RequirePermission("rbac.admin.permissions.create"), permissionHandler.CreatePermission)
		adminGroup.PUT("/permissions/:id", permissionMiddleware.RequirePermission("rbac.admin.permissions.update"), permissionHandler.UpdatePermission)
		adminGroup.DELETE("/permissions/:id", permissionMiddleware.RequirePermission("rbac.admin.permissions.delete"), permissionHandler.DeletePermission)

		// Permission queries
		adminGroup.GET("/permissions/module/:module", permissionHandler.GetPermissionsByModule)
		adminGroup.GET("/permissions/scope/:scope", permissionHandler.GetPermissionsByScope)
		adminGroup.GET("/permissions/risk/:risk_level", permissionHandler.GetPermissionsByRiskLevel)
		adminGroup.GET("/permissions/system", permissionHandler.GetSystemPermissions)
		adminGroup.GET("/permissions/unused", permissionHandler.GetUnusedPermissions)
		adminGroup.GET("/permissions/search", permissionHandler.SearchPermissions)

		// Permission analysis
		adminGroup.GET("/permissions/:id/roles", permissionHandler.GetPermissionRoles)
		adminGroup.GET("/permissions/:id/usage", permissionHandler.GetPermissionUsageStats)

		// Permission status management
		adminGroup.POST("/permissions/:id/activate", permissionMiddleware.RequirePermission("rbac.admin.permissions.update"), permissionHandler.ActivatePermission)
		adminGroup.POST("/permissions/:id/deactivate", permissionMiddleware.RequirePermission("rbac.admin.permissions.update"), permissionHandler.DeactivatePermission)

		// Discovery endpoints
		adminGroup.GET("/modules", permissionHandler.GetModules)
		adminGroup.GET("/modules/:module/resources", permissionHandler.GetResourcesByModule)
		adminGroup.GET("/resources/:resource/actions", permissionHandler.GetActionsByResource)

		// Bulk operations
		adminGroup.POST("/permissions/bulk", permissionMiddleware.RequirePermission("rbac.admin.permissions.bulk"), permissionHandler.CreatePermissionsBulk)
		adminGroup.PUT("/permissions/bulk", permissionMiddleware.RequirePermission("rbac.admin.permissions.bulk"), permissionHandler.UpdatePermissionsBulk)

		// Cleanup operations
		adminGroup.POST("/cleanup/expired-roles", permissionMiddleware.RequirePermission("rbac.admin.cleanup"), adminHandler.CleanupExpiredRoles)
		adminGroup.POST("/cleanup/revoked-roles", permissionMiddleware.RequirePermission("rbac.admin.cleanup"), adminHandler.CleanupRevokedRoles)

		// Configuration management
		adminGroup.GET("/config/export", permissionMiddleware.RequirePermission("rbac.admin.config.export"), adminHandler.ExportRoleConfiguration)
		adminGroup.POST("/config/import", permissionMiddleware.RequirePermission("rbac.admin.config.import"), adminHandler.ImportRoleConfiguration)
		adminGroup.POST("/config/validate", permissionMiddleware.RequirePermission("rbac.admin.config.validate"), adminHandler.ValidateRoleConfiguration)
	}
}

// RegisterMiddleware registers RBAC middleware for use in other modules
func RegisterMiddleware(rbacEngine services.RBACEngine) *handlers.PermissionMiddleware {
	return handlers.NewPermissionMiddleware(rbacEngine)
}

// Example usage in other modules:
//
// // In your module's routes.go
// func RegisterRoutes(router *gin.Engine, rbacMiddleware *handlers.PermissionMiddleware) {
//     apiGroup := router.Group("/api/posts")
//
//     // Public routes
//     apiGroup.GET("/", postHandler.ListPosts)
//     apiGroup.GET("/:id", postHandler.GetPost)
//
//     // Protected routes
//     apiGroup.POST("/", rbacMiddleware.RequirePermission("posts.create"), postHandler.CreatePost)
//     apiGroup.PUT("/:id", rbacMiddleware.RequireOwnership("posts.update", "id"), postHandler.UpdatePost)
//     apiGroup.DELETE("/:id", rbacMiddleware.RequireOwnership("posts.delete", "id"), postHandler.DeletePost)
//
//     // Admin routes
//     apiGroup.POST("/bulk", rbacMiddleware.RequireRole("admin"), postHandler.BulkCreatePosts)
//
//     // Context-specific routes
//     apiGroup.GET("/websites/:websiteId/posts", rbacMiddleware.RequireContext("posts.read", "websiteId"), postHandler.GetWebsitePosts)
// }
