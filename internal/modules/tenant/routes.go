package tenant

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth"
	settingServices "github.com/tranthanhloi/wn-api-v3/internal/modules/settings/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	userRepos "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories/mysql"
	userServices "github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all tenant module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, validator validator.Validator) {
	// Initialize logger
	logger := utils.NewLogger()

	// Initialize repositories
	tenantRepo := repositories.NewMySQLTenantRepository(db)
	planRepo := repositories.NewMySQLTenantPlanRepository(db)
	settingRepo := repositories.NewMySQLTenantSettingRepository(db)
	featureRepo := repositories.NewMySQLTenantFeatureRepository(db)
	catalogRepo := repositories.NewMySQLFeatureCatalogRepository(db)
	subscriptionRepo := repositories.NewMySQLSubscriptionRepository(db)
	tenantMembershipRepo := userRepos.NewTenantMembershipRepository(db, logger)

	// Initialize encryption service
	encryptionService, err := settingServices.NewEncryptionService()
	if err != nil {
		panic("Failed to initialize encryption service: " + err.Error())
	}

	// Initialize services
	tenantService := services.NewTenantService(tenantRepo, planRepo)
	planService := services.NewTenantPlanService(planRepo, tenantRepo)
	settingService := services.NewTenantSettingService(settingRepo, tenantRepo, encryptionService)
	featureService := services.NewTenantFeatureService(featureRepo, catalogRepo, tenantRepo, planRepo)
	catalogService := services.NewFeatureCatalogService(catalogRepo, featureRepo, tenantRepo)
	subscriptionService := services.NewSubscriptionService(subscriptionRepo, tenantRepo, planRepo)
	tenantMembershipService := userServices.NewTenantMembershipService(tenantMembershipRepo, logger)

	// Initialize handlers
	tenantHandler := handlers.NewTenantHandler(tenantService, tenantMembershipService, validator)
	planHandler := handlers.NewPlanHandler(planService, validator)
	settingsHandler := handlers.NewSettingsHandler(settingService, validator)
	featureHandler := handlers.NewFeatureHandler(featureService, catalogService, validator)
	subscriptionHandler := handlers.NewSubscriptionHandler(subscriptionService, validator)

	// Get JWT service from auth module
	_, jwtService, _, err := auth.GetAuthServiceDependencies(db, logger)
	if err != nil {
		logger.WithError(err).Error("Failed to get auth dependencies for tenant routes")
		panic("Failed to initialize JWT service for tenant routes")
	}

	// Tenant routes with rate limiting
	tenantRoutes := router.Group("/tenants")
	tenantRoutes.Use(middleware.RateLimitByTenantMiddleware(100, 200)) // 100 requests per minute per tenant

	// Public routes (no authentication needed)
	tenantRoutes.GET("/domain/:domain", tenantHandler.GetTenantByDomain) // Public for domain resolution

	// Protected routes (require authentication)
	protectedTenantRoutes := tenantRoutes.Group("")
	protectedTenantRoutes.Use(middleware.JWTAuthMiddleware(*jwtService))
	protectedTenantRoutes.Use(middleware.RequireAuthentication())
	{
		// User's own tenant operations
		protectedTenantRoutes.GET("/me/list", tenantHandler.GetMyTenants)

		// Admin tenant operations
		protectedTenantRoutes.POST("", middleware.RequirePermission("tenant.create"), tenantHandler.CreateTenant)
		protectedTenantRoutes.GET("", middleware.RequirePermission("tenant.list"), tenantHandler.ListTenants)
		protectedTenantRoutes.GET("/:id", middleware.RequirePermission("tenant.read"), tenantHandler.GetTenant)
		protectedTenantRoutes.PUT("/:id", middleware.RequirePermission("tenant.update"), tenantHandler.UpdateTenant)
		protectedTenantRoutes.DELETE("/:id", middleware.RequirePermission("tenant.delete"), tenantHandler.DeleteTenant)
		protectedTenantRoutes.PATCH("/:id/status", middleware.RequirePermission("tenant.status.update"), tenantHandler.UpdateTenantStatus)
		protectedTenantRoutes.POST("/:id/assign-plan", middleware.RequirePermission("tenant.plan.assign"), tenantHandler.AssignPlan)
		protectedTenantRoutes.GET("/:id/stats", middleware.RequirePermission("tenant.stats.read"), tenantHandler.GetTenantStats)
	}

	// Tenant Plan routes
	planRoutes := router.Group("/tenant-plans")

	// Public plan routes (for registration/pricing display)
	planRoutes.GET("", planHandler.ListPlans)
	planRoutes.GET("/active", planHandler.GetActivePlans)
	planRoutes.GET("/:id", planHandler.GetPlan)
	planRoutes.GET("/slug/:slug", planHandler.GetPlanBySlug)

	// Protected plan routes (admin operations)
	protectedPlanRoutes := planRoutes.Group("")
	protectedPlanRoutes.Use(middleware.JWTAuthMiddleware(*jwtService))
	protectedPlanRoutes.Use(middleware.RequireAuthentication())
	{
		protectedPlanRoutes.POST("", middleware.RequirePermission("tenant.plan.create"), planHandler.CreatePlan)
		protectedPlanRoutes.PUT("/:id", middleware.RequirePermission("tenant.plan.update"), planHandler.UpdatePlan)
		protectedPlanRoutes.DELETE("/:id", middleware.RequirePermission("tenant.plan.delete"), planHandler.DeletePlan)
		protectedPlanRoutes.GET("/:id/tenant-count", middleware.RequirePermission("tenant.plan.read"), planHandler.GetPlanTenantCount)
	}

	// Tenant Settings routes - Apply JWT auth and tenant isolation middleware
	tenantSettings := router.Group("/tenants/:id/settings")
	tenantSettings.Use(middleware.JWTAuthMiddleware(*jwtService))
	tenantSettings.Use(middleware.RequireAuthentication())
	tenantSettings.Use(middleware.TenantIsolationMiddleware(tenantService))
	tenantSettings.Use(middleware.PreventCrossTenantAccess())
	tenantSettings.Use(middleware.MultiTenantDatabaseScope())
	{
		tenantSettings.POST("", middleware.RequirePermission("tenant.settings.update"), settingsHandler.SetSetting)
		tenantSettings.GET("/:key", middleware.RequirePermission("tenant.settings.read"), settingsHandler.GetSetting)
		tenantSettings.DELETE("/:key", middleware.RequirePermission("tenant.settings.delete"), settingsHandler.DeleteSetting)
		tenantSettings.GET("/category/:category", middleware.RequirePermission("tenant.settings.read"), settingsHandler.GetSettingsByCategory)
		tenantSettings.PUT("/bulk", middleware.RequirePermission("tenant.settings.update"), settingsHandler.BulkUpdateSettings)
		tenantSettings.GET("/public", settingsHandler.GetPublicSettings) // No permission required for public settings
		tenantSettings.POST("/validate", middleware.RequirePermission("tenant.settings.read"), settingsHandler.ValidateSetting)
	}

	// Tenant Feature routes - Apply JWT auth and tenant isolation middleware
	tenantFeatures := router.Group("/tenants/:id/features")
	tenantFeatures.Use(middleware.JWTAuthMiddleware(*jwtService))
	tenantFeatures.Use(middleware.RequireAuthentication())
	tenantFeatures.Use(middleware.TenantIsolationMiddleware(tenantService))
	tenantFeatures.Use(middleware.PreventCrossTenantAccess())
	tenantFeatures.Use(middleware.MultiTenantDatabaseScope())
	{
		tenantFeatures.POST("/:featureKey/enable", middleware.RequirePermission("tenant.features.update"), featureHandler.EnableFeature)
		tenantFeatures.POST("/:featureKey/disable", middleware.RequirePermission("tenant.features.update"), featureHandler.DisableFeature)
		tenantFeatures.POST("/:featureKey/toggle", middleware.RequirePermission("tenant.features.update"), featureHandler.ToggleFeature)
		tenantFeatures.GET("/:featureKey/enabled", middleware.RequirePermission("tenant.features.read"), featureHandler.IsFeatureEnabled)
		tenantFeatures.PUT("/:featureKey/config", middleware.RequirePermission("tenant.features.update"), featureHandler.UpdateFeatureConfiguration)
		tenantFeatures.GET("/:featureKey/usage", middleware.RequirePermission("tenant.features.read"), featureHandler.GetFeatureUsage)
		tenantFeatures.GET("/enabled", middleware.RequirePermission("tenant.features.read"), featureHandler.GetEnabledFeatures)
		tenantFeatures.POST("/bulk-toggle", middleware.RequirePermission("tenant.features.update"), featureHandler.BulkToggleFeatures)
	}

	// Feature Catalog routes
	catalogRoutes := router.Group("/feature-catalog")

	// Public catalog routes (for plan comparison)
	catalogRoutes.GET("", featureHandler.ListFeatureCatalog)
	catalogRoutes.GET("/:key", featureHandler.GetFeatureCatalog)
	catalogRoutes.GET("/:key/adoption", featureHandler.GetFeatureAdoption)
	catalogRoutes.GET("/plan/:planId", featureHandler.GetFeaturesForPlan)

	// Protected catalog routes (admin operations)
	protectedCatalogRoutes := catalogRoutes.Group("")
	protectedCatalogRoutes.Use(middleware.JWTAuthMiddleware(*jwtService))
	protectedCatalogRoutes.Use(middleware.RequireAuthentication())
	{
		protectedCatalogRoutes.POST("", middleware.RequirePermission("tenant.feature-catalog.create"), featureHandler.CreateFeatureCatalog)
		protectedCatalogRoutes.PUT("/:key", middleware.RequirePermission("tenant.feature-catalog.update"), featureHandler.UpdateFeatureCatalog)
		protectedCatalogRoutes.POST("/:key/deprecate", middleware.RequirePermission("tenant.feature-catalog.deprecate"), featureHandler.DeprecateFeatureCatalog)
	}

	// Subscription routes
	subscriptionRoutes := router.Group("/subscriptions")
	subscriptionRoutes.Use(middleware.JWTAuthMiddleware(*jwtService))
	subscriptionRoutes.Use(middleware.RequireAuthentication())
	{
		subscriptionRoutes.POST("", middleware.RequirePermission("tenant.subscription.create"), subscriptionHandler.CreateSubscription)
	}

	// Tenant-scoped subscription routes
	tenantSubscriptions := router.Group("/tenants/:id")
	tenantSubscriptions.Use(middleware.JWTAuthMiddleware(*jwtService))
	tenantSubscriptions.Use(middleware.RequireAuthentication())
	tenantSubscriptions.Use(middleware.TenantIsolationMiddleware(tenantService))
	tenantSubscriptions.Use(middleware.PreventCrossTenantAccess())
	tenantSubscriptions.Use(middleware.MultiTenantDatabaseScope())
	{
		tenantSubscriptions.GET("/subscription", middleware.RequirePermission("tenant.subscription.read"), subscriptionHandler.GetSubscriptionByTenant)
	}

	// Subscription management routes (require JWT auth and tenant context)
	subscriptionMgmtRoutes := router.Group("/subscriptions/:id")
	subscriptionMgmtRoutes.Use(middleware.JWTAuthMiddleware(*jwtService))
	subscriptionMgmtRoutes.Use(middleware.RequireAuthentication())
	subscriptionMgmtRoutes.Use(middleware.TenantResourceOwnershipMiddleware("subscription"))
	{
		subscriptionMgmtRoutes.POST("/upgrade", middleware.RequirePermission("tenant.subscription.upgrade"), subscriptionHandler.UpgradePlan)
		subscriptionMgmtRoutes.POST("/downgrade", middleware.RequirePermission("tenant.subscription.downgrade"), subscriptionHandler.DowngradePlan)
		subscriptionMgmtRoutes.POST("/cancel", middleware.RequirePermission("tenant.subscription.cancel"), subscriptionHandler.CancelSubscription)
		subscriptionMgmtRoutes.POST("/payment-failure", middleware.RequirePermission("tenant.subscription.payment"), subscriptionHandler.ProcessPaymentFailure)
		subscriptionMgmtRoutes.POST("/process-trial-expiration", middleware.RequirePermission("tenant.subscription.admin"), subscriptionHandler.ProcessTrialExpiration)
	}

	// Admin subscription management routes
	adminRoutes := router.Group("/admin/subscriptions")
	adminRoutes.Use(middleware.JWTAuthMiddleware(*jwtService))
	adminRoutes.Use(middleware.RequireAuthentication())
	{
		adminRoutes.GET("/expired-trials", middleware.RequirePermission("tenant.subscription.admin"), subscriptionHandler.GetExpiredTrials)
		adminRoutes.POST("/process-pending-transitions", middleware.RequirePermission("tenant.subscription.admin"), subscriptionHandler.ProcessPendingTransitions)
	}
}
