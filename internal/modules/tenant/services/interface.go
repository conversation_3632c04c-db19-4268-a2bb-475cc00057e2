package services

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

// TenantService defines operations for tenant management
type TenantService interface {
	// Create creates a new tenant with default settings
	Create(ctx context.Context, input CreateTenantInput) (*models.Tenant, error)

	// GetByID retrieves a tenant by ID
	GetByID(ctx context.Context, id uint) (*models.Tenant, error)

	// GetByDomain retrieves a tenant by domain
	GetByDomain(ctx context.Context, domain string) (*models.Tenant, error)

	// Update updates tenant information
	Update(ctx context.Context, id uint, input UpdateTenantInput) (*models.Tenant, error)

	// UpdateStatus updates tenant status
	UpdateStatus(ctx context.Context, id uint, status models.TenantStatus) error

	// Delete soft deletes a tenant
	Delete(ctx context.Context, id uint) error

	// List retrieves tenants with pagination
	List(ctx context.Context, filter ListTenantFilter) (*TenantListResponse, error)

	// AssignPlan assigns a plan to tenant
	AssignPlan(ctx context.Context, tenantID, planID uint) error

	// CheckResourceLimit checks if tenant has reached resource limit
	CheckResourceLimit(ctx context.Context, tenantID uint, resource string) (bool, error)

	// GetTenantStats retrieves tenant statistics
	GetTenantStats(ctx context.Context, tenantID uint) (*TenantStats, error)
}

// TenantPlanService defines operations for tenant plan management
type TenantPlanService interface {
	// Create creates a new plan
	Create(ctx context.Context, input CreatePlanInput) (*models.TenantPlan, error)

	// GetByID retrieves a plan by ID
	GetByID(ctx context.Context, id uint) (*models.TenantPlan, error)

	// GetBySlug retrieves a plan by slug
	GetBySlug(ctx context.Context, slug string) (*models.TenantPlan, error)

	// Update updates plan information
	Update(ctx context.Context, id uint, input UpdatePlanInput) (*models.TenantPlan, error)

	// Delete soft deletes a plan
	Delete(ctx context.Context, id uint) error

	// List retrieves plans with pagination
	List(ctx context.Context, filter models.PlanFilter) (*PlanListResponse, error)

	// GetActivePlans retrieves all active visible plans
	GetActivePlans(ctx context.Context) ([]*models.TenantPlan, error)

	// GetPlanTenantCount gets count of tenants on a plan
	GetPlanTenantCount(ctx context.Context, planID uint) (int64, error)
}

// TenantSettingService defines operations for tenant settings
type TenantSettingService interface {
	// Get retrieves a setting value
	Get(ctx context.Context, tenantID uint, category, key string) (interface{}, error)

	// Set updates or creates a setting
	Set(ctx context.Context, tenantID uint, category, key string, value interface{}) error

	// Delete removes a setting
	Delete(ctx context.Context, tenantID uint, category, key string) error

	// GetByCategory retrieves all settings in a category
	GetByCategory(ctx context.Context, tenantID uint, category string) (map[string]interface{}, error)

	// BulkUpdate updates multiple settings
	BulkUpdate(ctx context.Context, tenantID uint, updates []SettingUpdate) error

	// GetPublicSettings retrieves public settings for a tenant
	GetPublicSettings(ctx context.Context, tenantID uint) (map[string]map[string]interface{}, error)

	// ValidateSetting validates a setting value against rules
	ValidateSetting(ctx context.Context, category, key string, value interface{}) error
}

// TenantFeatureService defines operations for tenant features
type TenantFeatureService interface {
	// Enable enables a feature for tenant
	Enable(ctx context.Context, tenantID uint, featureKey string) error

	// Disable disables a feature for tenant
	Disable(ctx context.Context, tenantID uint, featureKey string) error

	// Toggle toggles a feature state
	Toggle(ctx context.Context, tenantID uint, featureKey string) error

	// IsEnabled checks if a feature is enabled for tenant and user
	IsEnabled(ctx context.Context, tenantID uint, featureKey, userID string) (bool, error)

	// GetEnabledFeatures gets all enabled features for tenant
	GetEnabledFeatures(ctx context.Context, tenantID uint) ([]*models.TenantFeature, error)

	// BulkToggle enables/disables multiple features
	BulkToggle(ctx context.Context, tenantID uint, featureKeys []string, enabled bool) error

	// UpdateConfiguration updates feature configuration
	UpdateConfiguration(ctx context.Context, tenantID uint, featureKey string, config map[string]interface{}) error

	// GetFeatureUsage gets feature usage statistics
	GetFeatureUsage(ctx context.Context, tenantID uint, featureKey string) (*FeatureUsage, error)
}

// FeatureCatalogService defines operations for feature catalog
type FeatureCatalogService interface {
	// Create creates a new feature in catalog
	Create(ctx context.Context, input CreateFeatureInput) (*models.FeatureCatalog, error)

	// GetByKey retrieves a feature by key
	GetByKey(ctx context.Context, featureKey string) (*models.FeatureCatalog, error)

	// Update updates feature information
	Update(ctx context.Context, id uint, input UpdateFeatureInput) (*models.FeatureCatalog, error)

	// Deprecate marks a feature as deprecated
	Deprecate(ctx context.Context, id uint) error

	// List retrieves features with filters
	List(ctx context.Context, filter models.FeatureCatalogFilter) (*FeatureListResponse, error)

	// GetFeaturesForPlan gets features available for a plan
	GetFeaturesForPlan(ctx context.Context, planID uint) ([]*models.FeatureCatalog, error)

	// GetFeatureAdoption gets adoption statistics
	GetFeatureAdoption(ctx context.Context) ([]*FeatureAdoptionStats, error)
}

// Input DTOs
type CreateTenantInput struct {
	Name          string                 `json:"name" validate:"required,min=2,max=100"`
	Slug          string                 `json:"slug" validate:"omitempty,min=2,max=100,slug"`
	Domain        string                 `json:"domain" validate:"required,hostname|fqdn"`
	PlanID        uint                   `json:"plan_id" validate:"required"`
	AdminEmail    string                 `json:"admin_email" validate:"required,email"`
	AdminName     string                 `json:"admin_name" validate:"required"`
	CompanyInfo   map[string]interface{} `json:"company_info"`
	CustomDomains []string               `json:"custom_domains"`
	Status        *models.TenantStatus   `json:"status,omitempty"`
	TrialDays     *int                   `json:"trial_days,omitempty"`
}

type UpdateTenantInput struct {
	Name          *string                `json:"name" validate:"omitempty,min=2,max=100"`
	Slug          *string                `json:"slug" validate:"omitempty,min=2,max=100,slug"`
	Domain        *string                `json:"domain" validate:"omitempty,hostname|fqdn"`
	PlanID        *uint                  `json:"plan_id" validate:"omitempty,min=1"`
	Status        *models.TenantStatus   `json:"status,omitempty"`
	CustomDomains []string               `json:"custom_domains"`
	Settings      map[string]interface{} `json:"settings"`
	CompanyInfo   map[string]interface{} `json:"company_info"`
	Metadata      map[string]interface{} `json:"metadata"`
}

type CreatePlanInput struct {
	Name              string   `json:"name" validate:"required"`
	Slug              string   `json:"slug" validate:"required,lowercase,alphanum"`
	Description       string   `json:"description"`
	MonthlyPrice      float64  `json:"monthly_price" validate:"min=0"`
	YearlyPrice       float64  `json:"yearly_price" validate:"min=0"`
	TrialDays         int      `json:"trial_days" validate:"min=0,max=365"`
	MaxUsers          int      `json:"max_users" validate:"min=-1"`
	MaxProjects       int      `json:"max_projects" validate:"min=-1"`
	MaxStorage        int64    `json:"max_storage" validate:"min=-1"`
	MaxAPICallsPerDay int      `json:"max_api_calls_per_day" validate:"min=-1"`
	Features          []string `json:"features"`
	IsFeatured        bool     `json:"is_featured"`
	IsVisible         bool     `json:"is_visible"`
	DisplayOrder      int      `json:"display_order"`
}

type UpdatePlanInput struct {
	Name              *string            `json:"name"`
	Description       *string            `json:"description"`
	MonthlyPrice      *float64           `json:"monthly_price" validate:"omitempty,min=0"`
	YearlyPrice       *float64           `json:"yearly_price" validate:"omitempty,min=0"`
	TrialDays         *int               `json:"trial_days" validate:"omitempty,min=0,max=365"`
	MaxUsers          *int               `json:"max_users" validate:"omitempty,min=-1"`
	MaxProjects       *int               `json:"max_projects" validate:"omitempty,min=-1"`
	MaxStorage        *int64             `json:"max_storage" validate:"omitempty,min=-1"`
	MaxAPICallsPerDay *int               `json:"max_api_calls_per_day" validate:"omitempty,min=-1"`
	Features          []string           `json:"features"`
	IsFeatured        *bool              `json:"is_featured"`
	IsVisible         *bool              `json:"is_visible"`
	DisplayOrder      *int               `json:"display_order"`
	Status            *models.PlanStatus `json:"status"`
}

type CreateFeatureInput struct {
	FeatureKey       string `json:"feature_key" validate:"required,lowercase,alphanum"`
	FeatureName      string `json:"feature_name" validate:"required"`
	Description      string `json:"description"`
	Category         string `json:"category" validate:"required"`
	RequiredPlans    []uint `json:"required_plans"`
	ExcludedPlans    []uint `json:"excluded_plans"`
	IsBeta           bool   `json:"is_beta"`
	IsExperimental   bool   `json:"is_experimental"`
	DocumentationURL string `json:"documentation_url" validate:"omitempty,url"`
}

type UpdateFeatureInput struct {
	FeatureName      *string               `json:"feature_name"`
	Description      *string               `json:"description"`
	Category         *string               `json:"category"`
	RequiredPlans    []uint                `json:"required_plans"`
	ExcludedPlans    []uint                `json:"excluded_plans"`
	IsBeta           *bool                 `json:"is_beta"`
	IsExperimental   *bool                 `json:"is_experimental"`
	DocumentationURL *string               `json:"documentation_url" validate:"omitempty,url"`
	Status           *models.FeatureStatus `json:"status"`
}

type SettingUpdate struct {
	Category string      `json:"category" validate:"required"`
	Key      string      `json:"key" validate:"required"`
	Value    interface{} `json:"value" validate:"required"`
}

// Filter DTOs
type ListTenantFilter struct {
	Search         string              `json:"search"`
	Status         models.TenantStatus `json:"status"`
	PlanID         uint                `json:"plan_id"`
	Cursor         string              `json:"cursor"`
	Limit          int                 `json:"limit"`
	SortBy         string              `json:"sort_by"`
	SortOrder      string              `json:"sort_order"`
	IncludeDeleted bool                `json:"include_deleted"`
}

// Response DTOs
type TenantListResponse struct {
	Tenants    []*models.Tenant `json:"tenants"`
	NextCursor string           `json:"next_cursor"`
	HasMore    bool             `json:"has_more"`
}

type PlanListResponse struct {
	Plans      []*models.TenantPlan `json:"plans"`
	Total      int64                `json:"total"`
	Page       int                  `json:"page"`
	PageSize   int                  `json:"page_size"`
	TotalPages int                  `json:"total_pages"`
}

type FeatureListResponse struct {
	Features   []*models.FeatureCatalog `json:"features"`
	Total      int64                    `json:"total"`
	Page       int                      `json:"page"`
	PageSize   int                      `json:"page_size"`
	TotalPages int                      `json:"total_pages"`
}

type TenantStats struct {
	UserCount      int64                  `json:"user_count"`
	ProjectCount   int64                  `json:"project_count"`
	StorageUsed    int64                  `json:"storage_used"`
	APICallsToday  int64                  `json:"api_calls_today"`
	ResourceLimits map[string]interface{} `json:"resource_limits"`
	PlanInfo       *models.TenantPlan     `json:"plan_info"`
}

type FeatureUsage struct {
	EnabledAt   *string                `json:"enabled_at"`
	TotalUsers  int64                  `json:"total_users"`
	ActiveUsers int64                  `json:"active_users"`
	UsageStats  map[string]interface{} `json:"usage_stats"`
}

type FeatureAdoptionStats struct {
	FeatureKey          string  `json:"feature_key"`
	FeatureName         string  `json:"feature_name"`
	Category            string  `json:"category"`
	EnabledTenantsCount int     `json:"enabled_tenants_count"`
	TotalTenants        int     `json:"total_tenants"`
	AdoptionRate        float64 `json:"adoption_rate"`
}
