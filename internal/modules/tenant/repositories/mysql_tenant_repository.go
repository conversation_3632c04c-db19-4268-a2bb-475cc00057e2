package repositories

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
)

// MySQLTenantRepository implements TenantRepository using MySQL
type MySQLTenantRepository struct {
	db *gorm.DB
}

// NewMySQLTenantRepository creates a new MySQL tenant repository
func NewMySQLTenantRepository(db *gorm.DB) TenantRepository {
	return &MySQLTenantRepository{db: db}
}

// Create creates a new tenant
func (r *MySQLTenantRepository) Create(ctx context.Context, tenant *models.Tenant) error {
	return r.db.WithContext(ctx).Create(tenant).Error
}

// GetByID retrieves a tenant by ID
func (r *MySQLTenantRepository) GetByID(ctx context.Context, id uint) (*models.Tenant, error) {
	var tenant models.Tenant
	err := r.db.WithContext(ctx).
		Where("id = ? AND status != ?", id, models.TenantStatusDeleted).
		First(&tenant).Error

	if err != nil {
		return nil, err
	}
	return &tenant, nil
}

// GetBySlug retrieves a tenant by slug
func (r *MySQLTenantRepository) GetBySlug(ctx context.Context, slug string) (*models.Tenant, error) {
	var tenant models.Tenant
	err := r.db.WithContext(ctx).
		Where("slug = ? AND status != ?", slug, models.TenantStatusDeleted).
		First(&tenant).Error

	if err != nil {
		return nil, err
	}
	return &tenant, nil
}

// GetByDomain retrieves a tenant by domain
func (r *MySQLTenantRepository) GetByDomain(ctx context.Context, domain string) (*models.Tenant, error) {
	var tenant models.Tenant
	err := r.db.WithContext(ctx).
		Where("domain = ? AND status != ?", domain, models.TenantStatusDeleted).
		First(&tenant).Error

	if err != nil {
		return nil, err
	}
	return &tenant, nil
}

// GetByOwnerEmail retrieves tenants by owner email
func (r *MySQLTenantRepository) GetByOwnerEmail(ctx context.Context, email string) ([]*models.Tenant, error) {
	var tenants []*models.Tenant
	err := r.db.WithContext(ctx).
		Where("owner_email = ? AND status != ?", email, models.TenantStatusDeleted).
		Find(&tenants).Error

	return tenants, err
}

// Update updates a tenant
func (r *MySQLTenantRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	// Remove fields that shouldn't be updated directly
	delete(updates, "id")
	delete(updates, "created_at")

	return r.db.WithContext(ctx).
		Model(&models.Tenant{}).
		Where("id = ? AND status != ?", id, models.TenantStatusDeleted).
		Updates(updates).Error
}

// Delete soft deletes a tenant
func (r *MySQLTenantRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.Tenant{}).
		Where("id = ?", id).
		Update("status", models.TenantStatusDeleted).Error
}

// List retrieves tenants based on filter
func (r *MySQLTenantRepository) List(ctx context.Context, filter models.TenantFilter) ([]*models.Tenant, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.Tenant{})

	// Apply filters
	if !filter.IncludeDeleted {
		query = query.Where("status != ?", models.TenantStatusDeleted)
	}

	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	if filter.PlanID > 0 {
		query = query.Where("plan_id = ?", filter.PlanID)
	}

	if filter.Search != "" {
		search := "%" + filter.Search + "%"
		query = query.Where("name LIKE ? OR slug LIKE ? OR domain LIKE ? OR owner_email LIKE ?",
			search, search, search, search)
	}

	// Apply sorting
	sortBy := filter.SortBy
	if sortBy == "" {
		sortBy = "id"
	}
	sortOrder := filter.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply cursor-based pagination
	if filter.Cursor != "" {
		// Parse cursor to get the last ID
		var lastID uint
		if err := json.Unmarshal([]byte(filter.Cursor), &lastID); err == nil {
			if sortOrder == "desc" {
				query = query.Where("id < ?", lastID)
			} else {
				query = query.Where("id > ?", lastID)
			}
		}
	}

	// Set default limit
	limit := filter.Limit
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	// Fetch one extra record to check if there are more
	query = query.Limit(limit + 1)

	// Fetch records
	var tenants []*models.Tenant
	if err := query.Preload("Plan").Find(&tenants).Error; err != nil {
		return nil, 0, err
	}

	// Check if there are more records
	hasMore := len(tenants) > limit
	if hasMore {
		tenants = tenants[:limit]
	}

	// Convert hasMore to int64 for consistency with old signature
	var total int64
	if hasMore {
		total = 1 // Indicates there are more records
	} else {
		total = 0 // Indicates no more records
	}

	return tenants, total, nil
}

// CheckSlugExists checks if a slug already exists
func (r *MySQLTenantRepository) CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.Tenant{}).
		Where("slug = ? AND status != ?", slug, models.TenantStatusDeleted)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

// CheckDomainExists checks if a domain already exists
func (r *MySQLTenantRepository) CheckDomainExists(ctx context.Context, domain string, excludeID uint) (bool, error) {
	if domain == "" {
		return false, nil
	}

	var count int64
	query := r.db.WithContext(ctx).Model(&models.Tenant{}).
		Where("domain = ? AND status != ?", domain, models.TenantStatusDeleted)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}
