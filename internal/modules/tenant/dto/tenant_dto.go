package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

// TenantCreateRequest represents the request to create a new tenant
type TenantCreateRequest struct {
	Name           string `json:"name" validate:"required,min=3,max=100" example:"My Company"`
	Domain         string `json:"domain" validate:"required,min=3,max=100" example:"mycompany"`
	ContactEmail   string `json:"contact_email" validate:"required,email" example:"<EMAIL>"`
	ContactPhone   string `json:"contact_phone" validate:"omitempty,e164" example:"+1234567890"`
	CompanyName    string `json:"company_name" validate:"omitempty,max=200" example:"My Company Inc."`
	CompanyAddress string `json:"company_address" validate:"omitempty,max=500" example:"123 Business St, City, State"`
	CompanyTaxID   string `json:"company_tax_id" validate:"omitempty,max=50" example:"TAX123456"`
	PlanID         uint   `json:"plan_id" validate:"required" example:"1"`
}

// TenantUpdateRequest represents the request to update a tenant
type TenantUpdateRequest struct {
	Name           string  `json:"name" validate:"omitempty,min=3,max=100" example:"Updated Company"`
	ContactEmail   string  `json:"contact_email" validate:"omitempty,email" example:"<EMAIL>"`
	ContactPhone   string  `json:"contact_phone" validate:"omitempty,e164" example:"+9876543210"`
	CompanyName    *string `json:"company_name" validate:"omitempty,max=200" example:"Updated Company Inc."`
	CompanyAddress *string `json:"company_address" validate:"omitempty,max=500" example:"456 New Address St"`
	CompanyTaxID   *string `json:"company_tax_id" validate:"omitempty,max=50" example:"NEWTAX789"`
	LogoURL        *string `json:"logo_url" validate:"omitempty,url" example:"https://example.com/logo.png"`
	FaviconURL     *string `json:"favicon_url" validate:"omitempty,url" example:"https://example.com/favicon.ico"`
	PrimaryColor   *string `json:"primary_color" validate:"omitempty,hexcolor" example:"#3B82F6"`
	CustomCSS      *string `json:"custom_css" validate:"omitempty,max=10000" example:"body { font-family: Arial; }"`
}

// TenantUpdateStatusRequest represents the request to update tenant status
type TenantUpdateStatusRequest struct {
	Status string `json:"status" validate:"required,oneof=active suspended inactive trial deleted" example:"active"`
}

// TenantAssignPlanRequest represents the request to assign a plan to a tenant
type TenantAssignPlanRequest struct {
	PlanID uint `json:"plan_id" validate:"required" example:"2"`
}

// TenantListFilter represents the filter for listing tenants
type TenantListFilter struct {
	Search    string              `json:"search,omitempty" example:"company"`
	Status    models.TenantStatus `json:"status,omitempty" example:"active"`
	PlanID    uint                `json:"plan_id,omitempty" example:"1"`
	Page      int                 `json:"page,omitempty" example:"1"`
	PageSize  int                 `json:"page_size,omitempty" example:"20"`
	SortBy    string              `json:"sort_by,omitempty" example:"created_at"`
	SortOrder string              `json:"sort_order,omitempty" example:"desc"`
}

// TenantResponse represents the response for tenant operations
// KEEP_OMITEMPTY: Optional company data, branding, and nullable subscription dates
type TenantResponse struct {
	ID               uint                `json:"id" example:"1"`
	Name             string              `json:"name" example:"My Company"`
	Domain           string              `json:"domain" example:"mycompany"`
	Status           models.TenantStatus `json:"status" example:"active"`
	PlanID           uint                `json:"plan_id" example:"1"`
	ContactEmail     string              `json:"contact_email" example:"<EMAIL>"`
	ContactPhone     string              `json:"contact_phone,omitempty" example:"+1234567890"`                   // KEEP_OMITEMPTY: Optional contact
	CompanyName      string              `json:"company_name,omitempty" example:"My Company Inc."`                // KEEP_OMITEMPTY: Optional company data
	CompanyAddress   string              `json:"company_address,omitempty" example:"123 Business St"`             // KEEP_OMITEMPTY: Optional company data
	CompanyTaxID     string              `json:"company_tax_id,omitempty" example:"TAX123456"`                    // KEEP_OMITEMPTY: Optional company data
	LogoURL          string              `json:"logo_url,omitempty" example:"https://example.com/logo.png"`       // KEEP_OMITEMPTY: Optional branding
	FaviconURL       string              `json:"favicon_url,omitempty" example:"https://example.com/favicon.ico"` // KEEP_OMITEMPTY: Optional branding
	PrimaryColor     string              `json:"primary_color,omitempty" example:"#3B82F6"`                       // KEEP_OMITEMPTY: Optional branding
	CustomCSS        string              `json:"custom_css,omitempty"`                                            // KEEP_OMITEMPTY: Optional branding
	SubscriptionEnds *time.Time          `json:"subscription_ends,omitempty"`                                     // KEEP_OMITEMPTY: Nullable subscription date
	TrialEnds        *time.Time          `json:"trial_ends,omitempty"`                                            // KEEP_OMITEMPTY: Nullable trial date
	CreatedAt        time.Time           `json:"created_at"`
	UpdatedAt        time.Time           `json:"updated_at"`
}

// TenantListResponse represents the response for listing tenants
type TenantListResponse struct {
	Tenants    []TenantResponse `json:"tenants"`
	Total      int64            `json:"total" example:"50"`
	Page       int              `json:"page" example:"1"`
	PageSize   int              `json:"page_size" example:"20"`
	TotalPages int              `json:"total_pages" example:"3"`
}

// TenantStatsResponse represents tenant statistics
// KEEP_OMITEMPTY: Optional plan info that may not be loaded
type TenantStatsResponse struct {
	UserCount      int64                  `json:"user_count" example:"25"`
	ProjectCount   int64                  `json:"project_count" example:"5"`
	StorageUsed    int64                  `json:"storage_used" example:"1073741824"`
	APICallsToday  int64                  `json:"api_calls_today" example:"1250"`
	ResourceLimits map[string]interface{} `json:"resource_limits"`
	PlanInfo       *TenantPlanResponse    `json:"plan_info,omitempty"` // KEEP_OMITEMPTY: Optional nested object
}

// TenantDomainCheckRequest represents request to check domain availability
type TenantDomainCheckRequest struct {
	Domain string `json:"domain" validate:"required,min=3,max=100" example:"newcompany"`
}

// TenantDomainCheckResponse represents response for domain availability check
type TenantDomainCheckResponse struct {
	Domain    string `json:"domain" example:"newcompany"`
	Available bool   `json:"available" example:"true"`
	Message   string `json:"message" example:"Domain is available"`
}

// TenantUsageResponse represents tenant resource usage
type TenantUsageResponse struct {
	TenantID     uint                   `json:"tenant_id" example:"1"`
	PlanLimits   map[string]interface{} `json:"plan_limits"`
	CurrentUsage map[string]interface{} `json:"current_usage"`
	Percentage   map[string]float64     `json:"percentage"`
	Warnings     []string               `json:"warnings"`
}

// MyTenantItem represents a single tenant in the current user's tenant list
type MyTenantItem struct {
	ID               uint      `json:"id" example:"1"`
	Name             string    `json:"name" example:"My Company"`
	Domain           string    `json:"domain" example:"mycompany"`
	Status           string    `json:"status" example:"active"`
	MembershipStatus string    `json:"membership_status" example:"active"`
	IsPrimary        bool      `json:"is_primary" example:"true"`
	JoinedAt         time.Time `json:"joined_at"`
	LogoURL          string    `json:"logo_url,omitempty" example:"https://example.com/logo.png"`
}

// MyTenantsResponse represents the response for GET /tenants/my with cursor pagination
// DEPRECATED: Use standard response format with CursorResponse instead
type MyTenantsResponse struct {
	Tenants []MyTenantItem `json:"tenants"`
	Meta    struct {
		HasMore    bool   `json:"has_more" example:"true"`
		NextCursor string `json:"next_cursor,omitempty" example:"eyJpZCI6MTUsInRpbWUiOiIyMDI0LTAxLTE1VDA5OjMwOjAwWiJ9"`
		Total      int64  `json:"total" example:"3"`
	} `json:"meta"`
}

// AdminTenantCreateRequest represents the admin request to create a new tenant
type AdminTenantCreateRequest struct {
	Name         string               `json:"name" validate:"required,min=2,max=255" example:"Acme Corporation"`
	Slug         string               `json:"slug" validate:"required,min=2,max=100,slug" example:"acme-corp"`
	Domain       string               `json:"domain,omitempty" validate:"omitempty,fqdn" example:"acme.example.com"`
	PlanID       uint                 `json:"plan_id" validate:"required,min=1" example:"2"`
	Status       models.TenantStatus  `json:"status,omitempty" validate:"omitempty,oneof=active suspended inactive trial" example:"active"`
	OwnerEmail   string               `json:"owner_email" validate:"required,email" example:"<EMAIL>"`
	BillingEmail string               `json:"billing_email,omitempty" validate:"omitempty,email" example:"<EMAIL>"`
	SupportEmail string               `json:"support_email,omitempty" validate:"omitempty,email" example:"<EMAIL>"`
	TrialDays    int                  `json:"trial_days,omitempty" validate:"omitempty,min=0,max=365" example:"30"`
	Metadata     models.JSONMap       `json:"metadata,omitempty"`
}

// AdminTenantUpdateRequest represents the admin request to update a tenant
type AdminTenantUpdateRequest struct {
	Name         *string              `json:"name,omitempty" validate:"omitempty,min=2,max=255" example:"Updated Corporation"`
	Slug         *string              `json:"slug,omitempty" validate:"omitempty,min=2,max=100,slug" example:"updated-corp"`
	Domain       *string              `json:"domain,omitempty" validate:"omitempty,fqdn" example:"updated.example.com"`
	PlanID       *uint                `json:"plan_id,omitempty" validate:"omitempty,min=1" example:"3"`
	Status       *models.TenantStatus `json:"status,omitempty" validate:"omitempty,oneof=active suspended inactive trial deleted" example:"active"`
	OwnerEmail   *string              `json:"owner_email,omitempty" validate:"omitempty,email" example:"<EMAIL>"`
	BillingEmail *string              `json:"billing_email,omitempty" validate:"omitempty,email" example:"<EMAIL>"`
	SupportEmail *string              `json:"support_email,omitempty" validate:"omitempty,email" example:"<EMAIL>"`
	Metadata     models.JSONMap       `json:"metadata,omitempty"`
}

// AdminTenantStatusUpdateRequest represents the admin request to update tenant status
type AdminTenantStatusUpdateRequest struct {
	Status string `json:"status" validate:"required,oneof=active suspended inactive trial deleted" example:"suspended"`
	Reason string `json:"reason,omitempty" validate:"omitempty,max=500" example:"Payment failure"`
}

// AdminTenantResponse represents the admin response for tenant operations
type AdminTenantResponse struct {
	ID           uint                `json:"id" example:"1"`
	Name         string              `json:"name" example:"Acme Corporation"`
	Slug         string              `json:"slug" example:"acme-corp"`
	Domain       string              `json:"domain" example:"acme.example.com"`
	PlanID       uint                `json:"plan_id" example:"2"`
	Status       string              `json:"status" example:"active"`
	OwnerEmail   string              `json:"owner_email" example:"<EMAIL>"`
	BillingEmail string              `json:"billing_email,omitempty" example:"<EMAIL>"`
	SupportEmail string              `json:"support_email,omitempty" example:"<EMAIL>"`
	TrialEndsAt  *time.Time          `json:"trial_ends_at,omitempty"`
	Metadata     models.JSONMap      `json:"metadata,omitempty"`
	Plan         *TenantPlanResponse `json:"plan,omitempty"`
	CreatedAt    time.Time           `json:"created_at"`
	UpdatedAt    time.Time           `json:"updated_at"`
}

// AdminTenantStatsResponse represents admin-level tenant statistics
type AdminTenantStatsResponse struct {
	TenantStatsResponse
	DatabaseSize      int64                  `json:"database_size" example:"536870912"`
	LastActivityAt    *time.Time             `json:"last_activity_at,omitempty"`
	MonthlyAPIUsage   int64                  `json:"monthly_api_usage" example:"1250000"`
	ActiveUsers       int64                  `json:"active_users" example:"18"`
	InactiveUsers     int64                  `json:"inactive_users" example:"7"`
	FeatureUsage      map[string]interface{} `json:"feature_usage"`
	SubscriptionInfo  map[string]interface{} `json:"subscription_info,omitempty"`
}
