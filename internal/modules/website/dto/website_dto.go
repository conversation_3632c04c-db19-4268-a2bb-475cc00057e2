package dto

import (
	"errors"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// WebsiteCreateRequest represents the request to create a new website
type WebsiteCreateRequest struct {
	Name        string         `json:"name" validate:"required,min=1,max=255" example:"My Website"`
	Domain      *string        `json:"domain,omitempty" validate:"omitempty,fqdn" example:"example.com"`
	Subdomain   string         `json:"subdomain,omitempty" validate:"omitempty,min=2,max=100,slug" example:"my-site"`
	Description string         `json:"description,omitempty" example:"A description of my website"`
	ActiveTheme string         `json:"active_theme,omitempty" validate:"omitempty,max=100" example:"default"`
	CustomCSS   string         `json:"custom_css,omitempty" example:"body { color: #333; }"`
	CustomJS    string         `json:"custom_js,omitempty" example:"console.log('Hello world');"`
	SiteLogo    string         `json:"site_logo,omitempty" validate:"omitempty,url" example:"https://example.com/logo.png"`
	Favicon     string         `json:"favicon,omitempty" validate:"omitempty,url" example:"https://example.com/favicon.ico"`
	Timezone    string         `json:"timezone,omitempty" validate:"omitempty,timezone" example:"America/New_York"`
	Language    string         `json:"language,omitempty" validate:"omitempty,min=2,max=10" example:"en"`
	SocialMedia models.JSONMap `json:"social_media,omitempty"`
}

// ValidateExclusiveFields validates that only one of domain or subdomain is provided
func (r *WebsiteCreateRequest) ValidateExclusiveFields() error {
	hasDomain := r.Domain != nil && *r.Domain != ""
	hasSubdomain := r.Subdomain != ""

	// Both provided - not allowed
	if hasDomain && hasSubdomain {
		return errors.New("only one of 'domain' or 'subdomain' can be provided, not both")
	}

	// Neither provided - not allowed (at least one is required)
	if !hasDomain && !hasSubdomain {
		return errors.New("either 'domain' or 'subdomain' must be provided")
	}

	return nil
}

// WebsiteUpdateRequest represents the request to update a website
type WebsiteUpdateRequest struct {
	Name               *string               `json:"name,omitempty" validate:"omitempty,min=1,max=255" example:"Updated Website"`
	Domain             *string               `json:"domain,omitempty" validate:"omitempty,fqdn" example:"updated.com"`
	Subdomain          *string               `json:"subdomain,omitempty" validate:"omitempty,min=2,max=100,slug" example:"updated-site"`
	Description        *string               `json:"description,omitempty" example:"Updated description"`
	ActiveTheme        *string               `json:"active_theme,omitempty" validate:"omitempty,max=100" example:"modern"`
	CustomCSS          *string               `json:"custom_css,omitempty" example:"body { color: #666; }"`
	CustomJS           *string               `json:"custom_js,omitempty" example:"console.log('Updated');"`
	SiteLogo           *string               `json:"site_logo,omitempty" validate:"omitempty,url" example:"https://example.com/new-logo.png"`
	Favicon            *string               `json:"favicon,omitempty" validate:"omitempty,url" example:"https://example.com/new-favicon.ico"`
	Timezone           *string               `json:"timezone,omitempty" validate:"omitempty,timezone" example:"Europe/London"`
	Language           *string               `json:"language,omitempty" validate:"omitempty,min=2,max=10" example:"fr"`
	GoogleAnalyticsID  *string               `json:"google_analytics_id,omitempty" validate:"omitempty,max=50" example:"GA-123456-7"`
	GoogleTagManagerID *string               `json:"google_tag_manager_id,omitempty" validate:"omitempty,max=50" example:"GTM-ABC123"`
	FacebookPixelID    *string               `json:"facebook_pixel_id,omitempty" validate:"omitempty,max=50" example:"123456789"`
	SocialMedia        *models.JSONMap       `json:"social_media,omitempty"`
	Status             *models.WebsiteStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended" example:"active"`
}

// WebsiteResponse represents the response for website operations
type WebsiteResponse struct {
	ID                 uint                 `json:"id" example:"1"`
	TenantID           uint                 `json:"tenant_id" example:"1"`
	Name               string               `json:"name" example:"My Website"`
	Domain             *string              `json:"domain,omitempty" example:"example.com"`                      // KEEP_OMITEMPTY: Optional custom domain
	Subdomain          string               `json:"subdomain,omitempty" example:"my-site"`                       // KEEP_OMITEMPTY: Optional subdomain
	Description        string               `json:"description,omitempty" example:"A description of my website"` // KEEP_OMITEMPTY: Optional content
	ActiveTheme        string               `json:"active_theme,omitempty" example:"default"`                    // KEEP_OMITEMPTY: Optional theme
	CustomCSS          string               `json:"custom_css,omitempty"`                                        // KEEP_OMITEMPTY: Optional customization
	CustomJS           string               `json:"custom_js,omitempty"`                                         // KEEP_OMITEMPTY: Optional customization
	SiteLogo           string               `json:"site_logo,omitempty" example:"https://example.com/logo.png"`  // KEEP_OMITEMPTY: Optional branding
	Favicon            string               `json:"favicon,omitempty" example:"https://example.com/favicon.ico"` // KEEP_OMITEMPTY: Optional branding
	Timezone           string               `json:"timezone" example:"America/New_York"`
	Language           string               `json:"language" example:"en"`
	GoogleAnalyticsID  string               `json:"google_analytics_id,omitempty" example:"GA-123456-7"`  // KEEP_OMITEMPTY: Optional analytics
	GoogleTagManagerID string               `json:"google_tag_manager_id,omitempty" example:"GTM-ABC123"` // KEEP_OMITEMPTY: Optional analytics
	FacebookPixelID    string               `json:"facebook_pixel_id,omitempty" example:"123456789"`      // KEEP_OMITEMPTY: Optional analytics
	SocialMedia        models.JSONMap       `json:"social_media,omitempty"`                               // KEEP_OMITEMPTY: Optional social links
	Status             models.WebsiteStatus `json:"status" example:"active"`
	CreatedAt          time.Time            `json:"created_at"`
	UpdatedAt          time.Time            `json:"updated_at"`
}

// WebsiteListResponse represents the response for listing websites
type WebsiteListResponse struct {
	Websites []WebsiteResponse `json:"websites"`
}

// WebsiteFilter represents filter parameters for listing websites
type WebsiteFilter struct {
	pagination.CursorRequest
	Name      string                `json:"name,omitempty" form:"name" validate:"omitempty,max=255" example:"My Website"`
	Domain    *string               `json:"domain,omitempty" form:"domain" validate:"omitempty,fqdn" example:"example.com"`
	Status    *models.WebsiteStatus `json:"status,omitempty" form:"status" validate:"omitempty,oneof=active inactive suspended" example:"active"`
	Language  string                `json:"language,omitempty" form:"language" validate:"omitempty,min=2,max=10" example:"en"`
	SortBy    string                `json:"sort_by,omitempty" form:"sort_by" validate:"omitempty,oneof=id name domain created_at updated_at" example:"created_at"`
	SortOrder string                `json:"sort_order,omitempty" form:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}
