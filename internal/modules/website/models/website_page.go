package models

import (
	"time"
)

// PageType represents the type of website page
type PageType string

const (
	PageTypeStatic   PageType = "static"   // Static content page
	PageTypeDynamic  PageType = "dynamic"  // Dynamic content page
	PageTypeTemplate PageType = "template" // Template-based page
)

// PageVisibility represents the visibility level of a page
type PageVisibility string

const (
	PageVisibilityPublic   PageVisibility = "public"   // Publicly accessible
	PageVisibilityPrivate  PageVisibility = "private"  // Private, requires authentication
	PageVisibilityPassword PageVisibility = "password" // Password protected
	PageVisibilityDraft    PageVisibility = "draft"    // Draft, not visible to public
)

// PageStatus represents the publication status of a page
type PageStatus string

const (
	PageStatusDraft     PageStatus = "draft"     // Draft content
	PageStatusReview    PageStatus = "review"    // Under review
	PageStatusPublished PageStatus = "published" // Published and live
	PageStatusArchived  PageStatus = "archived"  // Archived content
	PageStatusDeleted   PageStatus = "deleted"   // Soft deleted
)

// WebsitePage represents a website page
type WebsitePage struct {
	ID                uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	WebsiteID         uint           `gorm:"not null;index" json:"website_id"`
	TenantID          uint           `gorm:"not null;index" json:"tenant_id"`
	TemplateID        *uint          `gorm:"index" json:"template_id,omitempty"`
	Title             string         `gorm:"type:varchar(255);not null" json:"title"`
	Slug              string         `gorm:"type:varchar(255);not null;index" json:"slug"`
	Description       string         `gorm:"type:text" json:"description,omitempty"`
	CanonicalURL      string         `gorm:"type:varchar(500)" json:"canonical_url,omitempty"`
	PageType          PageType       `gorm:"type:enum('static','dynamic','template');default:'static'" json:"page_type"`
	LayoutType        string         `gorm:"type:varchar(100);default:'default'" json:"layout_type"`
	IsHomepage        bool           `gorm:"default:false;index" json:"is_homepage"`
	IsPublished       bool           `gorm:"default:false;index" json:"is_published"`
	RequiresAuth      bool           `gorm:"default:false" json:"requires_auth"`
	PasswordProtected bool           `gorm:"default:false" json:"password_protected"`
	PagePassword      string         `gorm:"type:varchar(255)" json:"page_password,omitempty"`
	Visibility        PageVisibility `gorm:"type:enum('public','private','password','draft');default:'draft'" json:"visibility"`
	FeaturedImage     string         `gorm:"type:varchar(500)" json:"featured_image,omitempty"`
	Status            PageStatus     `gorm:"type:enum('draft','review','published','archived','deleted');default:'draft';index" json:"status"`
	PublishedAt       *time.Time     `gorm:"index" json:"published_at,omitempty"`
	ScheduledAt       *time.Time     `gorm:"index" json:"scheduled_at,omitempty"`
	CreatedBy         uint           `gorm:"not null;index" json:"created_by"`
	UpdatedBy         *uint          `gorm:"index" json:"updated_by,omitempty"`
	ViewCount         uint           `gorm:"default:0" json:"view_count"`
	LastViewedAt      *time.Time     `json:"last_viewed_at,omitempty"`
	CustomFields      JSONMap        `gorm:"type:json" json:"custom_fields,omitempty"`
	PageSettings      JSONMap        `gorm:"type:json" json:"page_settings,omitempty"`
	CreatedAt         time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"autoUpdateTime" json:"updated_at"`

	// Relations
	Website  *Website           `gorm:"foreignKey:WebsiteID" json:"website,omitempty"`
	Template *WebsiteTemplate   `gorm:"foreignKey:TemplateID" json:"template,omitempty"`
	Blocks   []WebsitePageBlock `gorm:"foreignKey:PageID" json:"blocks,omitempty"`
}

// TableName returns the table name for WebsitePage
func (WebsitePage) TableName() string {
	return "website_pages"
}

// IsLive checks if the page is published and live
func (p *WebsitePage) IsLive() bool {
	if !p.IsPublished || p.Status != PageStatusPublished {
		return false
	}

	now := time.Now()
	if p.PublishedAt != nil && p.PublishedAt.After(now) {
		return false
	}

	return true
}

// CanBeViewed checks if the page can be viewed based on visibility and authentication
func (p *WebsitePage) CanBeViewed(isAuthenticated bool, hasPassword bool) bool {
	if !p.IsLive() {
		return false
	}

	switch p.Visibility {
	case PageVisibilityPublic:
		return true
	case PageVisibilityPrivate:
		return isAuthenticated
	case PageVisibilityPassword:
		return hasPassword
	case PageVisibilityDraft:
		return false
	default:
		return false
	}
}

// GetSEOTitle returns the SEO-optimized title
func (p *WebsitePage) GetSEOTitle() string {
	return p.Title
}

// GetSEODescription returns the SEO-optimized description
func (p *WebsitePage) GetSEODescription() string {
	return p.Description
}

// WebsitePageFilter represents filtering options for website pages
type WebsitePageFilter struct {
	WebsiteID    *uint           `json:"website_id,omitempty"`
	TenantID     *uint           `json:"tenant_id,omitempty"`
	TemplateID   *uint           `json:"template_id,omitempty"`
	PageType     *PageType       `json:"page_type,omitempty"`
	Status       *PageStatus     `json:"status,omitempty"`
	Visibility   *PageVisibility `json:"visibility,omitempty"`
	IsPublished  *bool           `json:"is_published,omitempty"`
	IsHomepage   *bool           `json:"is_homepage,omitempty"`
	CreatedBy    *uint           `json:"created_by,omitempty"`
	SearchTerm   string          `json:"search_term,omitempty"`
	DateFrom     *time.Time      `json:"date_from,omitempty"`
	DateTo       *time.Time      `json:"date_to,omitempty"`
	LayoutType   string          `json:"layout_type,omitempty"`
	RequiresAuth *bool           `json:"requires_auth,omitempty"`
}

// WebsitePageCreateRequest represents the request to create a new website page
type WebsitePageCreateRequest struct {
	WebsiteID         uint           `json:"website_id" binding:"required"`
	TemplateID        *uint          `json:"template_id,omitempty"`
	Title             string         `json:"title" binding:"required"`
	Slug              string         `json:"slug" binding:"required"`
	Description       string         `json:"description,omitempty"`
	MetaTitle         string         `json:"meta_title,omitempty"`
	MetaDescription   string         `json:"meta_description,omitempty"`
	MetaKeywords      string         `json:"meta_keywords,omitempty"`
	CanonicalURL      string         `json:"canonical_url,omitempty"`
	PageType          PageType       `json:"page_type"`
	Visibility        PageVisibility `json:"visibility"`
	Status            PageStatus     `json:"status"`
	Content           JSONMap        `json:"content,omitempty"`
	LayoutConfig      JSONMap        `json:"layout_config,omitempty"`
	CustomCSS         string         `json:"custom_css,omitempty"`
	CustomJS          string         `json:"custom_js,omitempty"`
	IsHomepage        bool           `json:"is_homepage"`
	RequiresAuth      bool           `json:"requires_auth"`
	Password          string         `json:"password,omitempty"`
	PasswordProtected bool           `json:"password_protected"`
	PagePassword      string         `json:"page_password,omitempty"`
	LayoutType        string         `json:"layout_type,omitempty"`
	FeaturedImage     string         `json:"featured_image,omitempty"`
	PageSettings      JSONMap        `json:"page_settings,omitempty"`
	PublishedAt       *time.Time     `json:"published_at,omitempty"`
	ExpiresAt         *time.Time     `json:"expires_at,omitempty"`
	CustomFields      JSONMap        `json:"custom_fields,omitempty"`
}

// WebsitePageUpdateRequest represents the request to update a website page
type WebsitePageUpdateRequest struct {
	TemplateID        *uint           `json:"template_id,omitempty"`
	Title             *string         `json:"title,omitempty"`
	Slug              *string         `json:"slug,omitempty"`
	Description       *string         `json:"description,omitempty"`
	MetaTitle         *string         `json:"meta_title,omitempty"`
	MetaDescription   *string         `json:"meta_description,omitempty"`
	MetaKeywords      *string         `json:"meta_keywords,omitempty"`
	CanonicalURL      *string         `json:"canonical_url,omitempty"`
	PageType          *PageType       `json:"page_type,omitempty"`
	Visibility        *PageVisibility `json:"visibility,omitempty"`
	Status            *PageStatus     `json:"status,omitempty"`
	Content           *JSONMap        `json:"content,omitempty"`
	LayoutConfig      *JSONMap        `json:"layout_config,omitempty"`
	CustomCSS         *string         `json:"custom_css,omitempty"`
	CustomJS          *string         `json:"custom_js,omitempty"`
	IsHomepage        *bool           `json:"is_homepage,omitempty"`
	RequiresAuth      *bool           `json:"requires_auth,omitempty"`
	Password          *string         `json:"password,omitempty"`
	PasswordProtected *bool           `json:"password_protected,omitempty"`
	PagePassword      *string         `json:"page_password,omitempty"`
	LayoutType        *string         `json:"layout_type,omitempty"`
	FeaturedImage     *string         `json:"featured_image,omitempty"`
	PageSettings      *JSONMap        `json:"page_settings,omitempty"`
	PublishedAt       *time.Time      `json:"published_at,omitempty"`
	ExpiresAt         *time.Time      `json:"expires_at,omitempty"`
	CustomFields      *JSONMap        `json:"custom_fields,omitempty"`
}

// WebsitePageScheduleRequest represents the request to schedule a page
type WebsitePageScheduleRequest struct {
	ScheduledAt string `json:"scheduled_at" binding:"required"`
}

// WebsitePageDuplicateRequest represents the request to duplicate a page
type WebsitePageDuplicateRequest struct {
	NewTitle string `json:"new_title" binding:"required"`
	NewSlug  string `json:"new_slug" binding:"required"`
}
