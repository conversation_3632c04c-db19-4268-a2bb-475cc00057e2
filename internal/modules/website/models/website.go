package models

import (
	"database/sql/driver"
	"time"

	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
)

// WebsiteStatus represents the status of a website
// @Enum active,inactive,suspended
type WebsiteStatus string

const (
	WebsiteStatusActive    WebsiteStatus = "active"
	WebsiteStatusInactive  WebsiteStatus = "inactive"
	WebsiteStatusSuspended WebsiteStatus = "suspended"
)

// Scan implements sql.Scanner interface
func (s *WebsiteStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = WebsiteStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s WebsiteStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// Website represents a website in the multi-tenant system
type Website struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	TenantID    uint   `json:"tenant_id" gorm:"not null" validate:"required,min=1"`
	Name        string `json:"name" gorm:"not null" validate:"required,min=1,max=255"`
	Domain      *string `json:"domain,omitempty" gorm:"type:varchar(255)" validate:"omitempty,fqdn"`
	Subdomain   string `json:"subdomain,omitempty" gorm:"type:varchar(255);uniqueIndex" validate:"omitempty,min=2,max=100,alphanum"`
	Description string `json:"description,omitempty"`

	// Theme and Customization
	ActiveTheme string `json:"active_theme,omitempty" validate:"omitempty,max=100"`
	CustomCSS   string `json:"custom_css,omitempty"`
	CustomJS    string `json:"custom_js,omitempty"`

	// SEO and Branding
	SiteLogo string `json:"site_logo,omitempty" validate:"omitempty,url"`
	Favicon  string `json:"favicon,omitempty" validate:"omitempty,url"`
	Timezone string `json:"timezone" gorm:"default:UTC" validate:"omitempty,min=3,max=50"`
	Language string `json:"language" gorm:"default:en" validate:"required,min=2,max=10"`

	// Analytics Integration
	GoogleAnalyticsID  string `json:"google_analytics_id,omitempty" validate:"omitempty,max=50"`
	GoogleTagManagerID string `json:"google_tag_manager_id,omitempty" validate:"omitempty,max=50"`
	FacebookPixelID    string `json:"facebook_pixel_id,omitempty" validate:"omitempty,max=50"`

	// Social Media Links
	SocialMedia JSONMap `json:"social_media,omitempty" gorm:"type:json"`

	// Status and Timestamps
	Status    WebsiteStatus `json:"status" gorm:"type:enum('active','inactive','suspended');default:'active';not null"`
	CreatedAt time.Time     `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time     `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Tenant         *tenantModels.Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Settings       *[]WebsiteSetting    `json:"settings,omitempty" gorm:"foreignKey:WebsiteID"`
	Themes         *[]WebsiteTheme      `json:"themes,omitempty" gorm:"foreignKey:WebsiteID"`
	ActiveThemeObj *WebsiteTheme        `json:"active_theme_obj,omitempty" gorm:"foreignKey:ActiveTheme;references:Name"`
}

// TableName specifies the table name for Website
func (Website) TableName() string {
	return "websites"
}

// IsActive checks if website is active
func (w *Website) IsActive() bool {
	return w.Status == WebsiteStatusActive
}

// IsInactive checks if website is inactive
func (w *Website) IsInactive() bool {
	return w.Status == WebsiteStatusInactive
}

// IsSuspended checks if website is suspended
func (w *Website) IsSuspended() bool {
	return w.Status == WebsiteStatusSuspended
}

// GetFullDomain returns the full domain for the website
func (w *Website) GetFullDomain() string {
	if w.Domain != nil && *w.Domain != "" {
		return *w.Domain
	}
	if w.Subdomain != "" {
		return w.Subdomain + ".example.com" // Configure base domain
	}
	return ""
}

// BeforeCreate hook for Website
func (w *Website) BeforeCreate(tx *gorm.DB) error {
	if w.SocialMedia == nil {
		w.SocialMedia = make(JSONMap)
	}
	if w.Timezone == "" {
		w.Timezone = "UTC"
	}
	if w.Language == "" {
		w.Language = "en"
	}
	return nil
}

// WebsiteFilter represents filters for querying websites
type WebsiteFilter struct {
	TenantID       uint          `json:"tenant_id,omitempty"`
	Status         WebsiteStatus `json:"status,omitempty"`
	Domain         *string       `json:"domain,omitempty"`
	Subdomain      string        `json:"subdomain,omitempty"`
	Search         string        `json:"search,omitempty"`
	IncludeDeleted bool          `json:"include_deleted,omitempty"`
	Page           int           `json:"page,omitempty"`
	PageSize       int           `json:"page_size,omitempty"`
	SortBy         string        `json:"sort_by,omitempty"`
	SortOrder      string        `json:"sort_order,omitempty"`
}

// WebsiteCreateRequest represents the request to create a website
// TODO
type WebsiteCreateRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=255"`
	Domain      *string `json:"domain,omitempty"`
	Subdomain   string  `json:"subdomain,omitempty" validate:"omitempty,min=2,max=100,alphanum"`
	Description string  `json:"description,omitempty"`
	ActiveTheme string  `json:"active_theme,omitempty" validate:"omitempty,max=100"`
	CustomCSS   string  `json:"custom_css,omitempty"`
	CustomJS    string  `json:"custom_js,omitempty"`
	SiteLogo    string  `json:"site_logo,omitempty" validate:"omitempty,url"`
	Favicon     string  `json:"favicon,omitempty" validate:"omitempty,url"`
	Timezone    string  `json:"timezone,omitempty" validate:"omitempty,min=3,max=50"`
	Language    string  `json:"language,omitempty" validate:"omitempty,min=2,max=10"`
	SocialMedia JSONMap `json:"social_media,omitempty"`
}

// WebsiteUpdateRequest represents the request to update a website
type WebsiteUpdateRequest struct {
	Name               string        `json:"name,omitempty" validate:"omitempty,min=1,max=255"`
	Domain             *string       `json:"domain,omitempty" validate:"omitempty,fqdn"`
	Subdomain          string        `json:"subdomain,omitempty" validate:"omitempty,min=2,max=100,alphanum"`
	Description        string        `json:"description,omitempty"`
	ActiveTheme        string        `json:"active_theme,omitempty" validate:"omitempty,max=100"`
	CustomCSS          string        `json:"custom_css,omitempty"`
	CustomJS           string        `json:"custom_js,omitempty"`
	SiteLogo           string        `json:"site_logo,omitempty" validate:"omitempty,url"`
	Favicon            string        `json:"favicon,omitempty" validate:"omitempty,url"`
	Timezone           string        `json:"timezone,omitempty" validate:"omitempty,min=3,max=50"`
	Language           string        `json:"language,omitempty" validate:"omitempty,min=2,max=10"`
	GoogleAnalyticsID  string        `json:"google_analytics_id,omitempty" validate:"omitempty,max=50"`
	GoogleTagManagerID string        `json:"google_tag_manager_id,omitempty" validate:"omitempty,max=50"`
	FacebookPixelID    string        `json:"facebook_pixel_id,omitempty" validate:"omitempty,max=50"`
	SocialMedia        JSONMap       `json:"social_media,omitempty"`
	Status             WebsiteStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended"`
}

// WebsiteResponse represents the response for website operations
type WebsiteResponse struct {
	ID          uint          `json:"id"`
	TenantID    uint          `json:"tenant_id"`
	Name        string        `json:"name"`
	Domain      *string       `json:"domain,omitempty"`
	Subdomain   string        `json:"subdomain,omitempty"`
	Description string        `json:"description,omitempty"`
	ActiveTheme string        `json:"active_theme,omitempty"`
	SiteLogo    string        `json:"site_logo,omitempty"`
	Favicon     string        `json:"favicon,omitempty"`
	Timezone    string        `json:"timezone"`
	Language    string        `json:"language"`
	SocialMedia JSONMap       `json:"social_media,omitempty"`
	Status      WebsiteStatus `json:"status"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
}

// WebsiteListResponse represents the response for website listing
type WebsiteListResponse struct {
	Websites   []WebsiteResponse `json:"websites"`
	TotalCount int64             `json:"total_count"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}
