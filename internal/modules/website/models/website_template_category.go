package models

import (
	"time"
)

// CategoryStatus represents the status of template category
type CategoryStatus string

const (
	CategoryStatusActive   CategoryStatus = "active"
	CategoryStatusInactive CategoryStatus = "inactive"
	CategoryStatusDeleted  CategoryStatus = "deleted"
)

// TemplateCategory represents a website template category
type TemplateCategory struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"not null" validate:"required,min=1,max=255"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty"`

	// Category Properties
	Icon      string `json:"icon,omitempty"`
	Color     string `json:"color,omitempty" validate:"omitempty,hexcolor"`
	SortOrder uint   `json:"sort_order" gorm:"default:0"`

	// Category Status
	IsActive   bool `json:"is_active" gorm:"default:true"`
	IsFeatured bool `json:"is_featured" gorm:"default:false"`

	// Hierarchy Support
	ParentID *uint              `json:"parent_id,omitempty"`
	Parent   *TemplateCategory  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children []TemplateCategory `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Level    uint               `json:"level" gorm:"default:0"`
	Path     string             `json:"path" gorm:"not null;default:'/'"`

	// Statistics
	TemplateCount uint `json:"template_count" gorm:"default:0"`

	// Status
	Status CategoryStatus `json:"status" gorm:"type:enum('active','inactive','deleted');default:'active'"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relations
	Templates []WebsiteTemplate `json:"templates,omitempty" gorm:"foreignKey:CategoryID"`
}

// TableName sets the table name for TemplateCategory
func (TemplateCategory) TableName() string {
	return "website_template_categories"
}

// IsMainCategory returns true if this is a main category (level 0)
func (tc *TemplateCategory) IsMainCategory() bool {
	return tc.Level == 0 && tc.ParentID == nil
}

// IsSubcategory returns true if this is a subcategory (level > 0)
func (tc *TemplateCategory) IsSubcategory() bool {
	return tc.Level > 0 && tc.ParentID != nil
}

// GetStatus returns the category status as string
func (tc *TemplateCategory) GetStatus() string {
	return string(tc.Status)
}

// IsAvailable returns true if the category is available for use
func (tc *TemplateCategory) IsAvailable() bool {
	return tc.Status == CategoryStatusActive && tc.IsActive
}

// MarkAsActive marks the category as active
func (tc *TemplateCategory) MarkAsActive() {
	tc.Status = CategoryStatusActive
	tc.IsActive = true
}

// MarkAsInactive marks the category as inactive
func (tc *TemplateCategory) MarkAsInactive() {
	tc.Status = CategoryStatusInactive
	tc.IsActive = false
}

// IncrementTemplateCount increments the template count
func (tc *TemplateCategory) IncrementTemplateCount() {
	tc.TemplateCount++
}

// DecrementTemplateCount decrements the template count
func (tc *TemplateCategory) DecrementTemplateCount() {
	if tc.TemplateCount > 0 {
		tc.TemplateCount--
	}
}

// UpdatePath updates the category path based on parent
func (tc *TemplateCategory) UpdatePath(parentPath string) {
	if tc.ParentID == nil {
		tc.Path = "/"
		tc.Level = 0
	} else {
		tc.Path = parentPath + tc.Slug + "/"
		tc.Level = tc.calculateLevel(parentPath)
	}
}

// calculateLevel calculates the category level based on path
func (tc *TemplateCategory) calculateLevel(path string) uint {
	if path == "/" {
		return 1
	}

	level := uint(0)
	for _, char := range path {
		if char == '/' {
			level++
		}
	}
	return level
}
