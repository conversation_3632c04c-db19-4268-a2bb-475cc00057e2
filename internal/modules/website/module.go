package website

import (
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	pkgvalidator "github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// RegisterRoutes registers all website module routes and dependencies
func RegisterRoutes(r *gin.RouterGroup, db *gorm.DB, v pkgvalidator.Validator, logger utils.Logger) {
	// Create repositories
	websiteRepo := mysql.NewWebsiteRepository(db)
	templateRepo := mysql.NewWebsiteTemplateRepository(db)
	categoryRepo := mysql.NewTemplateCategoryRepository(db)

	// Create services
	// Create a new validator instance for internal use
	internalValidator := validator.New()

	// For now, we'll create a simplified domain service without separate repository
	domainService := services.NewDomainService(websiteRepo, "localhost") // Using default base domain
	websiteService := services.NewWebsiteService(websiteRepo, domainService, internalValidator)
	templateService := services.NewWebsiteTemplateService(templateRepo, categoryRepo, logger)
	categoryService := services.NewTemplateCategoryService(categoryRepo, templateRepo, logger)

	// Create handlers
	websiteHandler := handlers.NewWebsiteHandler(websiteService, v)
	domainHandler := handlers.NewDomainHandler(domainService, websiteService, v)
	templateHandler := handlers.NewWebsiteTemplateHandler(templateService, v, logger)
	categoryHandler := handlers.NewTemplateCategoryHandler(categoryService, v, logger)

	// Register routes with all handlers
	RegisterWebsiteRoutes(r, websiteHandler, domainHandler, templateHandler, categoryHandler, db, logger)
}
