package mysql

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

type websitePageRepository struct {
	db *gorm.DB
}

// NewWebsitePageRepository creates a new website page repository
func NewWebsitePageRepository(db *gorm.DB) repositories.WebsitePageRepository {
	return &websitePageRepository{db: db}
}

func (r *websitePageRepository) Create(ctx context.Context, page *models.WebsitePage) error {
	return r.db.WithContext(ctx).Create(page).Error
}

func (r *websitePageRepository) GetByID(ctx context.Context, id uint) (*models.WebsitePage, error) {
	var page models.WebsitePage
	err := r.db.WithContext(ctx).First(&page, id).Error
	if err != nil {
		return nil, err
	}
	return &page, nil
}

func (r *websitePageRepository) GetBySlug(ctx context.Context, websiteID uint, slug string) (*models.WebsitePage, error) {
	var page models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND slug = ?", websiteID, slug).
		First(&page).Error
	if err != nil {
		return nil, err
	}
	return &page, nil
}

func (r *websitePageRepository) GetByTenantID(ctx context.Context, tenantID, pageID uint) (*models.WebsitePage, error) {
	var page models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, pageID).
		First(&page).Error
	if err != nil {
		return nil, err
	}
	return &page, nil
}

func (r *websitePageRepository) GetByWebsite(ctx context.Context, websiteID uint) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ?", websiteID).
		Order("created_at DESC").
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) GetPublishedPages(ctx context.Context, websiteID uint) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND status = ? AND is_published = ?", websiteID, models.PageStatusPublished, true).
		Order("published_at DESC").
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) GetHomepage(ctx context.Context, websiteID uint) (*models.WebsitePage, error) {
	var page models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND is_homepage = ?", websiteID, true).
		First(&page).Error
	if err != nil {
		return nil, err
	}
	return &page, nil
}

func (r *websitePageRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).Where("id = ?", id).Updates(updates).Error
}

func (r *websitePageRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("id = ?", id).
		Update("status", models.PageStatusDeleted).Error
}

func (r *websitePageRepository) List(ctx context.Context, filter models.WebsitePageFilter) ([]*models.WebsitePage, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.WebsitePage{})

	// Apply filters
	if filter.WebsiteID != nil {
		query = query.Where("website_id = ?", *filter.WebsiteID)
	}
	if filter.TenantID != nil {
		query = query.Where("tenant_id = ?", *filter.TenantID)
	}
	if filter.TemplateID != nil {
		query = query.Where("template_id = ?", *filter.TemplateID)
	}
	if filter.PageType != nil {
		query = query.Where("page_type = ?", *filter.PageType)
	}
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.Visibility != nil {
		query = query.Where("visibility = ?", *filter.Visibility)
	}
	if filter.IsPublished != nil {
		query = query.Where("is_published = ?", *filter.IsPublished)
	}
	if filter.IsHomepage != nil {
		query = query.Where("is_homepage = ?", *filter.IsHomepage)
	}
	if filter.CreatedBy != nil {
		query = query.Where("created_by = ?", *filter.CreatedBy)
	}
	if filter.SearchTerm != "" {
		query = query.Where("title LIKE ? OR description LIKE ?",
			"%"+filter.SearchTerm+"%", "%"+filter.SearchTerm+"%")
	}
	if filter.DateFrom != nil {
		query = query.Where("created_at >= ?", *filter.DateFrom)
	}
	if filter.DateTo != nil {
		query = query.Where("created_at <= ?", *filter.DateTo)
	}
	if filter.LayoutType != "" {
		query = query.Where("layout_type = ?", filter.LayoutType)
	}
	if filter.RequiresAuth != nil {
		query = query.Where("requires_auth = ?", *filter.RequiresAuth)
	}

	// Count total
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Get pages
	var pages []*models.WebsitePage
	err := query.Order("created_at DESC").Find(&pages).Error
	return pages, count, err
}

func (r *websitePageRepository) ListWithCursor(ctx context.Context, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]*models.WebsitePage, *pagination.CursorResponse, error) {
	query := r.db.WithContext(ctx).Model(&models.WebsitePage{}).Where("website_id = ?", websiteID)

	// Apply additional filters
	for key, value := range filters {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	// Apply cursor pagination
	if req.Cursor != "" {
		decoded, err := pagination.DecodeCursor(req.Cursor)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid cursor: %w", err)
		}
		if decoded != nil {
			query = query.Where("id < ?", uint(decoded.ID))
		}
	}

	// Get pages
	var pages []*models.WebsitePage
	err := query.Order("id DESC").Limit(int(req.Limit)).Find(&pages).Error
	if err != nil {
		return nil, nil, err
	}

	// Create cursor response
	var nextCursor string
	hasMore := len(pages) == int(req.Limit)
	if hasMore && len(pages) > 0 {
		lastPage := pages[len(pages)-1]
		nextCursor, _ = pagination.EncodeCursor(lastPage.ID, lastPage.CreatedAt)
	}

	response := &pagination.CursorResponse{
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}

	return pages, response, nil
}

func (r *websitePageRepository) Search(ctx context.Context, websiteID uint, query string, limit int) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND (title LIKE ? OR description LIKE ?)",
			websiteID, "%"+query+"%", "%"+query+"%").
		Order("view_count DESC, created_at DESC").
		Limit(limit).
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) UpdateStatus(ctx context.Context, id uint, status models.PageStatus) error {
	updates := map[string]interface{}{"status": status}
	if status == models.PageStatusPublished {
		updates["is_published"] = true
		updates["published_at"] = time.Now()
	} else if status != models.PageStatusPublished {
		updates["is_published"] = false
	}
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).Where("id = ?", id).Updates(updates).Error
}

func (r *websitePageRepository) PublishPage(ctx context.Context, id uint) error {
	return r.UpdateStatus(ctx, id, models.PageStatusPublished)
}

func (r *websitePageRepository) UnpublishPage(ctx context.Context, id uint) error {
	updates := map[string]interface{}{
		"is_published": false,
		"status":       models.PageStatusDraft,
	}
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).Where("id = ?", id).Updates(updates).Error
}

func (r *websitePageRepository) SchedulePage(ctx context.Context, id uint, scheduledAt *time.Time) error {
	updates := map[string]interface{}{
		"scheduled_at": scheduledAt,
		"status":       models.PageStatusDraft,
	}
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).Where("id = ?", id).Updates(updates).Error
}

func (r *websitePageRepository) GetScheduledPages(ctx context.Context, websiteID uint) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND scheduled_at IS NOT NULL AND scheduled_at <= ? AND status != ?",
			websiteID, time.Now(), models.PageStatusPublished).
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) ProcessScheduledPages(ctx context.Context) error {
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("scheduled_at IS NOT NULL AND scheduled_at <= ? AND status != ?",
			time.Now(), models.PageStatusPublished).
		Updates(map[string]interface{}{
			"status":       models.PageStatusPublished,
			"is_published": true,
			"published_at": time.Now(),
		}).Error
}

func (r *websitePageRepository) SetAsHomepage(ctx context.Context, websiteID, pageID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Unset current homepage
		if err := tx.Model(&models.WebsitePage{}).
			Where("website_id = ? AND is_homepage = ?", websiteID, true).
			Update("is_homepage", false).Error; err != nil {
			return err
		}

		// Set new homepage
		return tx.Model(&models.WebsitePage{}).
			Where("id = ? AND website_id = ?", pageID, websiteID).
			Update("is_homepage", true).Error
	})
}

func (r *websitePageRepository) UnsetHomepage(ctx context.Context, pageID uint) error {
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("id = ?", pageID).
		Update("is_homepage", false).Error
}

func (r *websitePageRepository) IncrementViewCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"view_count":     gorm.Expr("view_count + 1"),
			"last_viewed_at": time.Now(),
		}).Error
}

func (r *websitePageRepository) GetPagesByTemplate(ctx context.Context, templateID uint) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("template_id = ?", templateID).
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) GetPagesByStatus(ctx context.Context, websiteID uint, status models.PageStatus) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND status = ?", websiteID, status).
		Order("created_at DESC").
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) GetPagesByVisibility(ctx context.Context, websiteID uint, visibility models.PageVisibility) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND visibility = ?", websiteID, visibility).
		Order("created_at DESC").
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) GetPagesByCreator(ctx context.Context, createdBy uint) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("created_by = ?", createdBy).
		Order("created_at DESC").
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) GetPagesByDateRange(ctx context.Context, websiteID uint, from, to time.Time) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND created_at BETWEEN ? AND ?", websiteID, from, to).
		Order("created_at DESC").
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) CheckSlugExists(ctx context.Context, websiteID uint, slug string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("website_id = ? AND slug = ?", websiteID, slug)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

func (r *websitePageRepository) GetWithBlocks(ctx context.Context, id uint) (*models.WebsitePage, error) {
	var page models.WebsitePage
	err := r.db.WithContext(ctx).Preload("Blocks").First(&page, id).Error
	if err != nil {
		return nil, err
	}
	return &page, nil
}

func (r *websitePageRepository) GetWithTemplate(ctx context.Context, id uint) (*models.WebsitePage, error) {
	var page models.WebsitePage
	err := r.db.WithContext(ctx).Preload("Template").First(&page, id).Error
	if err != nil {
		return nil, err
	}
	return &page, nil
}

func (r *websitePageRepository) GetWithAll(ctx context.Context, id uint) (*models.WebsitePage, error) {
	var page models.WebsitePage
	err := r.db.WithContext(ctx).
		Preload("Website").
		Preload("Template").
		Preload("Blocks").
		First(&page, id).Error
	if err != nil {
		return nil, err
	}
	return &page, nil
}

func (r *websitePageRepository) CountByWebsite(ctx context.Context, websiteID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("website_id = ?", websiteID).
		Count(&count).Error
	return count, err
}

func (r *websitePageRepository) CountByStatus(ctx context.Context, websiteID uint, status models.PageStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("website_id = ? AND status = ?", websiteID, status).
		Count(&count).Error
	return count, err
}

func (r *websitePageRepository) CountByVisibility(ctx context.Context, websiteID uint, visibility models.PageVisibility) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("website_id = ? AND visibility = ?", websiteID, visibility).
		Count(&count).Error
	return count, err
}

func (r *websitePageRepository) GetPageAnalytics(ctx context.Context, pageID uint) (map[string]interface{}, error) {
	var page models.WebsitePage
	err := r.db.WithContext(ctx).Select("view_count", "last_viewed_at", "created_at", "published_at").
		First(&page, pageID).Error
	if err != nil {
		return nil, err
	}

	analytics := map[string]interface{}{
		"view_count":     page.ViewCount,
		"last_viewed_at": page.LastViewedAt,
		"created_at":     page.CreatedAt,
		"published_at":   page.PublishedAt,
	}

	return analytics, nil
}

func (r *websitePageRepository) BulkUpdateStatus(ctx context.Context, pageIDs []uint, status models.PageStatus) error {
	updates := map[string]interface{}{"status": status}
	if status == models.PageStatusPublished {
		updates["is_published"] = true
		updates["published_at"] = time.Now()
	}
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("id IN ?", pageIDs).
		Updates(updates).Error
}

func (r *websitePageRepository) BulkDelete(ctx context.Context, pageIDs []uint) error {
	return r.db.WithContext(ctx).Model(&models.WebsitePage{}).
		Where("id IN ?", pageIDs).
		Update("status", models.PageStatusDeleted).Error
}

func (r *websitePageRepository) DuplicatePage(ctx context.Context, sourceID uint, newPage *models.WebsitePage) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get source page
		var sourcePage models.WebsitePage
		if err := tx.First(&sourcePage, sourceID).Error; err != nil {
			return err
		}

		// Copy data to new page
		newPage.Title = sourcePage.Title + " (Copy)"
		newPage.Description = sourcePage.Description
		newPage.PageType = sourcePage.PageType
		newPage.LayoutType = sourcePage.LayoutType
		newPage.RequiresAuth = sourcePage.RequiresAuth
		newPage.PasswordProtected = sourcePage.PasswordProtected
		newPage.Visibility = models.PageVisibilityDraft
		newPage.Status = models.PageStatusDraft
		newPage.IsPublished = false
		newPage.IsHomepage = false
		newPage.CustomFields = sourcePage.CustomFields
		newPage.PageSettings = sourcePage.PageSettings

		return tx.Create(newPage).Error
	})
}

func (r *websitePageRepository) GetPagesByTenant(ctx context.Context, tenantID uint) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("created_at DESC").
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) GetSitemap(ctx context.Context, websiteID uint) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Select("id", "slug", "title", "updated_at", "published_at").
		Where("website_id = ? AND status = ? AND visibility = ?",
			websiteID, models.PageStatusPublished, models.PageVisibilityPublic).
		Order("updated_at DESC").
		Find(&pages).Error
	return pages, err
}

func (r *websitePageRepository) GetRSSFeed(ctx context.Context, websiteID uint, limit int) ([]*models.WebsitePage, error) {
	var pages []*models.WebsitePage
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND status = ? AND visibility = ?",
			websiteID, models.PageStatusPublished, models.PageVisibilityPublic).
		Order("published_at DESC").
		Limit(limit).
		Find(&pages).Error
	return pages, err
}
