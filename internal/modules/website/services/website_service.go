package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// websiteService implements the WebsiteService interface
type websiteService struct {
	websiteRepo repositories.WebsiteRepository
	domainSvc   DomainService
	validator   *validator.Validate
}

// NewWebsiteService creates a new website service
func NewWebsiteService(
	websiteRepo repositories.WebsiteRepository,
	domainSvc DomainService,
	validator *validator.Validate,
) WebsiteService {
	return &websiteService{
		websiteRepo: websiteRepo,
		domainSvc:   domainSvc,
		validator:   validator,
	}
}

// CreateWebsite creates a new website with validation
func (s *websiteService) CreateWebsite(ctx context.Context, tenantID uint, req *models.WebsiteCreateRequest) (*models.WebsiteResponse, error) {
	// Validate request
	if err := s.validator.Struct(req); err != nil {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeValidationFailed,
			Message: fmt.Sprintf("validation failed: %v", err),
		}
	}

	// Validate domain/subdomain
	var domainStr string
	if req.Domain != nil {
		domainStr = *req.Domain
	}
	if err := s.validateDomainOrSubdomain(ctx, domainStr, req.Subdomain, 0); err != nil {
		return nil, err
	}

	// Create website model
	website := &models.Website{
		TenantID:    tenantID,
		Name:        req.Name,
		Description: req.Description,
		ActiveTheme: req.ActiveTheme,
		CustomCSS:   req.CustomCSS,
		CustomJS:    req.CustomJS,
		SiteLogo:    req.SiteLogo,
		Favicon:     req.Favicon,
		Timezone:    req.Timezone,
		Language:    req.Language,
		SocialMedia: req.SocialMedia,
		Status:      models.WebsiteStatusActive,
	}

	// Only set domain if not empty
	if req.Domain != nil && *req.Domain != "" {
		normalized := s.domainSvc.NormalizeDomain(*req.Domain)
		website.Domain = &normalized
	}

	// Only set subdomain if not empty
	if req.Subdomain != "" {
		website.Subdomain = s.domainSvc.NormalizeSubdomain(req.Subdomain)
	}

	// Set defaults
	if website.Timezone == "" {
		website.Timezone = "UTC"
	}
	if website.Language == "" {
		website.Language = "en"
	}

	// Create website
	if err := s.websiteRepo.Create(ctx, website); err != nil {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to create website: %v", err),
		}
	}

	return s.websiteToResponse(website), nil
}

// GetWebsite retrieves a website by ID with tenant validation
func (s *websiteService) GetWebsite(ctx context.Context, tenantID, websiteID uint) (*models.WebsiteResponse, error) {
	website, err := s.websiteRepo.GetByTenantID(ctx, tenantID, websiteID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeWebsiteNotFound,
				Message: "website not found",
			}
		}
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to get website: %v", err),
		}
	}

	return s.websiteToResponse(website), nil
}

// GetWebsiteByDomain retrieves a website by domain
func (s *websiteService) GetWebsiteByDomain(ctx context.Context, domain string) (*models.WebsiteResponse, error) {
	website, err := s.websiteRepo.GetByDomain(ctx, s.domainSvc.NormalizeDomain(domain))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeWebsiteNotFound,
				Message: "website not found for domain",
			}
		}
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to get website by domain: %v", err),
		}
	}

	return s.websiteToResponse(website), nil
}

// GetWebsiteBySubdomain retrieves a website by subdomain
func (s *websiteService) GetWebsiteBySubdomain(ctx context.Context, subdomain string) (*models.WebsiteResponse, error) {
	website, err := s.websiteRepo.GetBySubdomain(ctx, s.domainSvc.NormalizeSubdomain(subdomain))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeWebsiteNotFound,
				Message: "website not found for subdomain",
			}
		}
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to get website by subdomain: %v", err),
		}
	}

	return s.websiteToResponse(website), nil
}

// UpdateWebsite updates a website with validation
func (s *websiteService) UpdateWebsite(ctx context.Context, tenantID, websiteID uint, req *models.WebsiteUpdateRequest) (*models.WebsiteResponse, error) {
	// Validate request
	if err := s.validator.Struct(req); err != nil {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeValidationFailed,
			Message: fmt.Sprintf("validation failed: %v", err),
		}
	}

	// Check if website exists and belongs to tenant
	if err := s.ValidateWebsiteAccess(ctx, tenantID, websiteID); err != nil {
		return nil, err
	}

	// Validate domain/subdomain if provided
	var domainStr string
	if req.Domain != nil {
		domainStr = *req.Domain
	}
	if domainStr != "" || req.Subdomain != "" {
		if err := s.validateDomainOrSubdomain(ctx, domainStr, req.Subdomain, websiteID); err != nil {
			return nil, err
		}
	}

	// Build updates map
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Domain != nil && *req.Domain != "" {
		updates["domain"] = s.domainSvc.NormalizeDomain(*req.Domain)
	}
	if req.Subdomain != "" {
		updates["subdomain"] = s.domainSvc.NormalizeSubdomain(req.Subdomain)
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.ActiveTheme != "" {
		updates["active_theme"] = req.ActiveTheme
	}
	if req.CustomCSS != "" {
		updates["custom_css"] = req.CustomCSS
	}
	if req.CustomJS != "" {
		updates["custom_js"] = req.CustomJS
	}
	if req.SiteLogo != "" {
		updates["site_logo"] = req.SiteLogo
	}
	if req.Favicon != "" {
		updates["favicon"] = req.Favicon
	}
	if req.Timezone != "" {
		updates["timezone"] = req.Timezone
	}
	if req.Language != "" {
		updates["language"] = req.Language
	}
	if req.GoogleAnalyticsID != "" {
		updates["google_analytics_id"] = req.GoogleAnalyticsID
	}
	if req.GoogleTagManagerID != "" {
		updates["google_tag_manager_id"] = req.GoogleTagManagerID
	}
	if req.FacebookPixelID != "" {
		updates["facebook_pixel_id"] = req.FacebookPixelID
	}
	if req.SocialMedia != nil {
		updates["social_media"] = req.SocialMedia
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	// Update website
	if err := s.websiteRepo.Update(ctx, websiteID, updates); err != nil {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to update website: %v", err),
		}
	}

	return s.GetWebsite(ctx, tenantID, websiteID)
}

// DeleteWebsite soft deletes a website
func (s *websiteService) DeleteWebsite(ctx context.Context, tenantID, websiteID uint) error {
	// Check if website exists and belongs to tenant
	if err := s.ValidateWebsiteAccess(ctx, tenantID, websiteID); err != nil {
		return err
	}

	// Delete website
	if err := s.websiteRepo.Delete(ctx, websiteID); err != nil {
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to delete website: %v", err),
		}
	}

	return nil
}

// ListWebsites retrieves websites for a tenant
func (s *websiteService) ListWebsites(ctx context.Context, tenantID uint, filter models.WebsiteFilter) (*models.WebsiteListResponse, error) {
	// Ensure filter is for the correct tenant
	filter.TenantID = tenantID

	websites, total, err := s.websiteRepo.List(ctx, filter)
	if err != nil {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to list websites: %v", err),
		}
	}

	// Convert to response format
	response := &models.WebsiteListResponse{
		Websites:   make([]models.WebsiteResponse, len(websites)),
		TotalCount: total,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
	}

	for i, website := range websites {
		response.Websites[i] = *s.websiteToResponse(website)
	}

	if filter.PageSize > 0 {
		response.TotalPages = int((total + int64(filter.PageSize) - 1) / int64(filter.PageSize))
	}

	return response, nil
}

// ListWebsitesWithCursor retrieves websites using cursor-based pagination
func (s *websiteService) ListWebsitesWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) (*dto.WebsiteListResponse, error) {
	websites, _, err := s.websiteRepo.ListWithCursor(ctx, tenantID, req, filters)
	if err != nil {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to list websites with cursor: %v", err),
		}
	}

	// Convert websites to response DTOs
	websiteResponses := make([]dto.WebsiteResponse, len(websites))
	for i, website := range websites {
		websiteResponses[i] = s.websiteToResponseDTO(website)
	}

	return &dto.WebsiteListResponse{
		Websites: websiteResponses,
	}, nil
}

// ActivateWebsite activates a website
func (s *websiteService) ActivateWebsite(ctx context.Context, tenantID, websiteID uint) error {
	if err := s.ValidateWebsiteAccess(ctx, tenantID, websiteID); err != nil {
		return err
	}

	if err := s.websiteRepo.ActivateWebsite(ctx, websiteID); err != nil {
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to activate website: %v", err),
		}
	}

	return nil
}

// DeactivateWebsite deactivates a website
func (s *websiteService) DeactivateWebsite(ctx context.Context, tenantID, websiteID uint) error {
	if err := s.ValidateWebsiteAccess(ctx, tenantID, websiteID); err != nil {
		return err
	}

	if err := s.websiteRepo.DeactivateWebsite(ctx, websiteID); err != nil {
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to deactivate website: %v", err),
		}
	}

	return nil
}

// SuspendWebsite suspends a website
func (s *websiteService) SuspendWebsite(ctx context.Context, tenantID, websiteID uint) error {
	if err := s.ValidateWebsiteAccess(ctx, tenantID, websiteID); err != nil {
		return err
	}

	if err := s.websiteRepo.SuspendWebsite(ctx, websiteID); err != nil {
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to suspend website: %v", err),
		}
	}

	return nil
}

// ValidateDomain validates domain availability and format
func (s *websiteService) ValidateDomain(ctx context.Context, domain string, excludeWebsiteID uint) error {
	return s.domainSvc.ValidateDomain(ctx, domain, excludeWebsiteID)
}

// ValidateSubdomain validates subdomain availability and format
func (s *websiteService) ValidateSubdomain(ctx context.Context, subdomain string, excludeWebsiteID uint) error {
	return s.domainSvc.ValidateSubdomain(ctx, subdomain, excludeWebsiteID)
}

// GetWebsiteStats retrieves website statistics
func (s *websiteService) GetWebsiteStats(ctx context.Context, tenantID uint) (*WebsiteStats, error) {
	// Get websites for tenant
	websites, err := s.websiteRepo.GetByTenant(ctx, tenantID)
	if err != nil {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to get websites for stats: %v", err),
		}
	}

	// Calculate stats
	stats := &WebsiteStats{
		TotalWebsites:    int64(len(websites)),
		WebsitesByStatus: make(map[string]int64),
		LastUpdated:      time.Now().Format(time.RFC3339),
	}

	for _, website := range websites {
		statusStr := string(website.Status)
		stats.WebsitesByStatus[statusStr]++

		switch website.Status {
		case models.WebsiteStatusActive:
			stats.ActiveWebsites++
		case models.WebsiteStatusInactive:
			stats.InactiveWebsites++
		case models.WebsiteStatusSuspended:
			stats.SuspendedWebsites++
		}

		if website.Domain != nil && *website.Domain != "" {
			stats.CustomDomains++
		}
		if website.Subdomain != "" {
			stats.Subdomains++
		}
	}

	return stats, nil
}

// GetWebsiteContext creates website context from domain or subdomain
func (s *websiteService) GetWebsiteContext(ctx context.Context, domain string) (*models.WebsiteContext, error) {
	var website *models.Website
	var err error

	// Try to get website by domain first
	if website, err = s.websiteRepo.GetByDomain(ctx, domain); err != nil {
		// If not found by domain, try subdomain
		if errors.Is(err, gorm.ErrRecordNotFound) {
			if website, err = s.websiteRepo.GetBySubdomain(ctx, domain); err != nil {
				return nil, &WebsiteServiceError{
					Code:    ErrCodeWebsiteNotFound,
					Message: "website not found for domain",
				}
			}
		} else {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeInternalError,
				Message: fmt.Sprintf("failed to get website context: %v", err),
			}
		}
	}

	return &models.WebsiteContext{
		Website:   website,
		WebsiteID: website.ID,
		Domain:    domain,
		TenantID:  website.TenantID,
	}, nil
}

// ValidateWebsiteAccess validates if tenant has access to website
func (s *websiteService) ValidateWebsiteAccess(ctx context.Context, tenantID, websiteID uint) error {
	website, err := s.websiteRepo.GetByTenantID(ctx, tenantID, websiteID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &WebsiteServiceError{
				Code:    ErrCodeWebsiteNotFound,
				Message: "website not found",
			}
		}
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to validate website access: %v", err),
		}
	}

	if website.TenantID != tenantID {
		return &WebsiteServiceError{
			Code:    ErrCodeWebsiteAccessDenied,
			Message: "access denied to website",
		}
	}

	return nil
}

// GetWebsiteWithDetails retrieves website with all details
func (s *websiteService) GetWebsiteWithDetails(ctx context.Context, tenantID, websiteID uint) (*models.Website, error) {
	if err := s.ValidateWebsiteAccess(ctx, tenantID, websiteID); err != nil {
		return nil, err
	}

	website, err := s.websiteRepo.GetWithAll(ctx, websiteID)
	if err != nil {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to get website with details: %v", err),
		}
	}

	return website, nil
}

// CloneWebsite clones a website for the same tenant
func (s *websiteService) CloneWebsite(ctx context.Context, tenantID, sourceWebsiteID uint, newName string) (*models.WebsiteResponse, error) {
	// Get source website
	sourceWebsite, err := s.GetWebsiteWithDetails(ctx, tenantID, sourceWebsiteID)
	if err != nil {
		return nil, err
	}

	// Create new website based on source
	req := &models.WebsiteCreateRequest{
		Name:        newName,
		Description: sourceWebsite.Description,
		ActiveTheme: sourceWebsite.ActiveTheme,
		CustomCSS:   sourceWebsite.CustomCSS,
		CustomJS:    sourceWebsite.CustomJS,
		SiteLogo:    sourceWebsite.SiteLogo,
		Favicon:     sourceWebsite.Favicon,
		Timezone:    sourceWebsite.Timezone,
		Language:    sourceWebsite.Language,
		SocialMedia: sourceWebsite.SocialMedia,
	}

	return s.CreateWebsite(ctx, tenantID, req)
}

// TransferWebsite transfers website to another tenant (admin only)
func (s *websiteService) TransferWebsite(ctx context.Context, websiteID, newTenantID uint) error {
	updates := map[string]interface{}{
		"tenant_id": newTenantID,
	}

	if err := s.websiteRepo.Update(ctx, websiteID, updates); err != nil {
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to transfer website: %v", err),
		}
	}

	return nil
}

// Helper methods

// validateDomainOrSubdomain validates domain or subdomain
func (s *websiteService) validateDomainOrSubdomain(ctx context.Context, domain, subdomain string, excludeWebsiteID uint) error {
	if domain != "" && subdomain != "" {
		return &WebsiteServiceError{
			Code:    ErrCodeValidationFailed,
			Message: "cannot specify both domain and subdomain",
		}
	}

	if domain == "" && subdomain == "" {
		return &WebsiteServiceError{
			Code:    ErrCodeValidationFailed,
			Message: "either domain or subdomain must be specified",
		}
	}

	if domain != "" {
		return s.ValidateDomain(ctx, domain, excludeWebsiteID)
	}

	return s.ValidateSubdomain(ctx, subdomain, excludeWebsiteID)
}

// websiteToResponse converts Website model to WebsiteResponse
func (s *websiteService) websiteToResponse(website *models.Website) *models.WebsiteResponse {
	return &models.WebsiteResponse{
		ID:          website.ID,
		TenantID:    website.TenantID,
		Name:        website.Name,
		Domain:      website.Domain,
		Subdomain:   website.Subdomain,
		Description: website.Description,
		ActiveTheme: website.ActiveTheme,
		SiteLogo:    website.SiteLogo,
		Favicon:     website.Favicon,
		Timezone:    website.Timezone,
		Language:    website.Language,
		SocialMedia: website.SocialMedia,
		Status:      website.Status,
		CreatedAt:   website.CreatedAt,
		UpdatedAt:   website.UpdatedAt,
	}
}

// websiteToResponseDTO converts Website model to DTO WebsiteResponse
func (s *websiteService) websiteToResponseDTO(website *models.Website) dto.WebsiteResponse {
	return dto.WebsiteResponse{
		ID:                 website.ID,
		TenantID:           website.TenantID,
		Name:               website.Name,
		Domain:             website.Domain,
		Subdomain:          website.Subdomain,
		Description:        website.Description,
		ActiveTheme:        website.ActiveTheme,
		CustomCSS:          website.CustomCSS,
		CustomJS:           website.CustomJS,
		SiteLogo:           website.SiteLogo,
		Favicon:            website.Favicon,
		Timezone:           website.Timezone,
		Language:           website.Language,
		GoogleAnalyticsID:  website.GoogleAnalyticsID,
		GoogleTagManagerID: website.GoogleTagManagerID,
		FacebookPixelID:    website.FacebookPixelID,
		SocialMedia:        website.SocialMedia,
		Status:             website.Status,
		CreatedAt:          website.CreatedAt,
		UpdatedAt:          website.UpdatedAt,
	}
}
