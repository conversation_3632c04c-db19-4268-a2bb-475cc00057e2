package services

import (
	"context"
	"math"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// templateCategoryService implements TemplateCategoryService
type templateCategoryService struct {
	categoryRepo repositories.TemplateCategoryRepository
	templateRepo repositories.WebsiteTemplateRepository
	logger       utils.Logger
}

// NewTemplateCategoryService creates a new template category service
func NewTemplateCategoryService(
	categoryRepo repositories.TemplateCategoryRepository,
	templateRepo repositories.WebsiteTemplateRepository,
	logger utils.Logger,
) TemplateCategoryService {
	return &templateCategoryService{
		categoryRepo: categoryRepo,
		templateRepo: templateRepo,
		logger:       logger,
	}
}

// CreateCategory creates a new template category
func (s *templateCategoryService) CreateCategory(ctx context.Context, req *dto.CreateTemplateCategoryRequest) (*dto.TemplateCategoryResponse, error) {
	// Validate slug uniqueness
	if err := s.ValidateCategorySlug(ctx, req.Slug, 0); err != nil {
		return nil, err
	}

	// Validate parent category if provided
	var parentPath string = "/"
	if req.ParentID != nil {
		parent, err := s.categoryRepo.GetByID(ctx, *req.ParentID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, &WebsiteServiceError{
					Code:    ErrCodeCategoryNotFound,
					Message: "Parent category not found",
					Field:   "parent_id",
				}
			}
			return nil, err
		}
		parentPath = parent.Path
	}

	// Create category model
	category := &models.TemplateCategory{
		Name:        req.Name,
		Slug:        req.Slug,
		Description: req.Description,
		Icon:        req.Icon,
		Color:       req.Color,
		SortOrder:   req.SortOrder,
		IsActive:    req.IsActive,
		IsFeatured:  req.IsFeatured,
		ParentID:    req.ParentID,
		Status:      models.CategoryStatusActive,
	}

	// Update path and level based on parent
	category.UpdatePath(parentPath)

	// Create category
	if err := s.categoryRepo.Create(ctx, category); err != nil {
		s.logger.WithError(err).Error("Failed to create template category")
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: "Failed to create template category",
		}
	}

	// Get category with parent for response
	categoryWithParent, err := s.categoryRepo.GetWithParent(ctx, category.ID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get category with parent")
		categoryWithParent = category
	}

	return dto.NewTemplateCategoryResponse(categoryWithParent), nil
}

// GetCategory retrieves a category by ID or slug
func (s *templateCategoryService) GetCategory(ctx context.Context, req *dto.GetTemplateCategoryRequest) (*dto.TemplateCategoryResponse, error) {
	var category *models.TemplateCategory
	var err error

	if req.ID != nil {
		category, err = s.categoryRepo.GetByID(ctx, *req.ID)
	} else if req.Slug != "" {
		category, err = s.categoryRepo.GetBySlug(ctx, req.Slug)
	} else {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeValidationFailed,
			Message: "Either ID or slug must be provided",
		}
	}

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeCategoryNotFound,
				Message: "Template category not found",
			}
		}
		return nil, err
	}

	// Load relations based on request
	if req.IncludeParent {
		category, err = s.categoryRepo.GetWithParent(ctx, category.ID)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to load parent category")
		}
	}

	if req.IncludeChildren {
		category, err = s.categoryRepo.GetWithChildren(ctx, category.ID)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to load child categories")
		}
	}

	if req.IncludeTemplates {
		category, err = s.categoryRepo.GetWithTemplates(ctx, category.ID)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to load category templates")
		}
	}

	return dto.NewTemplateCategoryResponse(category), nil
}

// UpdateCategory updates a category
func (s *templateCategoryService) UpdateCategory(ctx context.Context, req *dto.UpdateTemplateCategoryRequest) (*dto.TemplateCategoryResponse, error) {
	// Get existing category
	category, err := s.categoryRepo.GetByID(ctx, req.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeCategoryNotFound,
				Message: "Template category not found",
			}
		}
		return nil, err
	}

	updates := make(map[string]interface{})
	needsPathUpdate := false

	// Validate slug uniqueness if changed
	if req.Slug != nil && *req.Slug != category.Slug {
		if err := s.ValidateCategorySlug(ctx, *req.Slug, req.ID); err != nil {
			return nil, err
		}
		updates["slug"] = *req.Slug
		needsPathUpdate = true
	}

	// Validate parent if changed
	if req.ParentID != nil && (category.ParentID == nil || *req.ParentID != *category.ParentID) {
		if _, err := s.categoryRepo.GetByID(ctx, *req.ParentID); err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, &WebsiteServiceError{
					Code:    ErrCodeCategoryNotFound,
					Message: "Parent category not found",
					Field:   "parent_id",
				}
			}
			return nil, err
		}
		updates["parent_id"] = *req.ParentID
		needsPathUpdate = true
	}

	// Update other fields
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Icon != nil {
		updates["icon"] = *req.Icon
	}
	if req.Color != nil {
		updates["color"] = *req.Color
	}
	if req.SortOrder != nil {
		updates["sort_order"] = *req.SortOrder
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
		if *req.IsActive {
			updates["status"] = models.CategoryStatusActive
		} else {
			updates["status"] = models.CategoryStatusInactive
		}
	}
	if req.IsFeatured != nil {
		updates["is_featured"] = *req.IsFeatured
	}

	// Update path if needed
	if needsPathUpdate {
		if err := s.categoryRepo.MoveCategoryToParent(ctx, req.ID, req.ParentID); err != nil {
			s.logger.WithError(err).Error("Failed to update category hierarchy")
			return nil, &WebsiteServiceError{
				Code:    ErrCodeInternalError,
				Message: "Failed to update category hierarchy",
			}
		}
	}

	// Apply updates
	if len(updates) > 0 {
		if err := s.categoryRepo.Update(ctx, req.ID, updates); err != nil {
			s.logger.WithError(err).Error("Failed to update template category")
			return nil, &WebsiteServiceError{
				Code:    ErrCodeInternalError,
				Message: "Failed to update template category",
			}
		}
	}

	// Get updated category with parent
	updatedCategory, err := s.categoryRepo.GetWithParent(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	return dto.NewTemplateCategoryResponse(updatedCategory), nil
}

// DeleteCategory soft deletes a category
func (s *templateCategoryService) DeleteCategory(ctx context.Context, id uint) error {
	// Check if category has templates
	templateCount, err := s.templateRepo.CountByCategory(ctx, id)
	if err != nil {
		return err
	}
	if templateCount > 0 {
		return &WebsiteServiceError{
			Code:    ErrCodeValidationFailed,
			Message: "Cannot delete category with templates",
		}
	}

	// Check if category has children
	children, err := s.categoryRepo.GetSubcategories(ctx, id)
	if err != nil {
		return err
	}
	if len(children) > 0 {
		return &WebsiteServiceError{
			Code:    ErrCodeValidationFailed,
			Message: "Cannot delete category with subcategories",
		}
	}

	// Delete category
	if err := s.categoryRepo.Delete(ctx, id); err != nil {
		s.logger.WithError(err).Error("Failed to delete template category")
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: "Failed to delete template category",
		}
	}

	return nil
}

// ListCategories retrieves categories with filtering and pagination
func (s *templateCategoryService) ListCategories(ctx context.Context, req *dto.ListTemplateCategoriesRequest) (*dto.TemplateCategoryListResponse, error) {
	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 50
	}

	// Build filter
	filter := models.CategoryFilter{
		ParentID:   req.ParentID,
		Level:      req.Level,
		IsActive:   req.IsActive,
		IsFeatured: req.IsFeatured,
		Search:     &req.Search,
		SortBy:     req.SortBy,
		SortOrder:  req.SortOrder,
		Offset:     (req.Page - 1) * req.Limit,
		Limit:      req.Limit,
	}

	// Set status
	if req.Status != "" {
		status := models.CategoryStatus(req.Status)
		filter.Status = &status
	}

	// Get categories
	categories, total, err := s.categoryRepo.List(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list template categories")
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: "Failed to list template categories",
		}
	}

	// Convert to DTOs
	categoryResponses := make([]dto.TemplateCategoryResponse, len(categories))
	for i, category := range categories {
		categoryResponses[i] = *dto.NewTemplateCategoryResponse(category)
	}

	// Calculate total pages
	totalPages := int(math.Ceil(float64(total) / float64(req.Limit)))

	return &dto.TemplateCategoryListResponse{
		Categories: categoryResponses,
		Total:      total,
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: totalPages,
	}, nil
}

// GetCategoryTree retrieves the complete category tree
func (s *templateCategoryService) GetCategoryTree(ctx context.Context) (*dto.TemplateCategoryTreeResponse, error) {
	categories, err := s.categoryRepo.GetCategoryTree(ctx)
	if err != nil {
		return nil, err
	}

	// Convert to models slice for BuildCategoryTree
	categoryModels := make([]models.TemplateCategory, len(categories))
	for i, category := range categories {
		categoryModels[i] = *category
	}

	tree := dto.BuildCategoryTree(categoryModels)

	return &dto.TemplateCategoryTreeResponse{
		Categories: tree,
	}, nil
}

// GetMainCategories retrieves main categories (level 0)
func (s *templateCategoryService) GetMainCategories(ctx context.Context) ([]*dto.TemplateCategoryResponse, error) {
	categories, err := s.categoryRepo.GetMainCategories(ctx)
	if err != nil {
		return nil, err
	}

	responses := make([]*dto.TemplateCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i] = dto.NewTemplateCategoryResponse(category)
	}

	return responses, nil
}

// GetSubcategories retrieves subcategories for a parent
func (s *templateCategoryService) GetSubcategories(ctx context.Context, parentID uint) ([]*dto.TemplateCategoryResponse, error) {
	categories, err := s.categoryRepo.GetSubcategories(ctx, parentID)
	if err != nil {
		return nil, err
	}

	responses := make([]*dto.TemplateCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i] = dto.NewTemplateCategoryResponse(category)
	}

	return responses, nil
}

// GetFeaturedCategories retrieves featured categories
func (s *templateCategoryService) GetFeaturedCategories(ctx context.Context) ([]*dto.TemplateCategoryResponse, error) {
	categories, err := s.categoryRepo.GetFeatured(ctx)
	if err != nil {
		return nil, err
	}

	responses := make([]*dto.TemplateCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i] = dto.NewTemplateCategoryResponse(category)
	}

	return responses, nil
}

// GetCategoryStats retrieves category statistics
func (s *templateCategoryService) GetCategoryStats(ctx context.Context) (*dto.TemplateCategoryStatsResponse, error) {
	stats, err := s.categoryRepo.GetStats(ctx)
	if err != nil {
		return nil, err
	}

	// Get most popular categories (by template count)
	popularCategories, err := s.categoryRepo.GetMainCategories(ctx)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get categories for stats")
		popularCategories = []*models.TemplateCategory{}
	}

	// Sort by template count and limit to top 5
	mostPopular := make([]dto.TemplateCategoryResponse, 0, 5)
	for i, category := range popularCategories {
		if i >= 5 {
			break
		}
		if category.TemplateCount > 0 {
			mostPopular = append(mostPopular, *dto.NewTemplateCategoryResponse(category))
		}
	}

	return &dto.TemplateCategoryStatsResponse{
		TotalCategories:    stats.TotalCategories,
		ActiveCategories:   stats.ActiveCategories,
		FeaturedCategories: stats.FeaturedCategories,
		MainCategories:     stats.MainCategories,
		Subcategories:      stats.Subcategories,
		MostPopular:        mostPopular,
		ByLevel:            stats.ByLevel,
	}, nil
}

// MoveCategoryToParent moves a category to a new parent
func (s *templateCategoryService) MoveCategoryToParent(ctx context.Context, categoryID uint, newParentID *uint) error {
	// Validate that category exists
	if _, err := s.categoryRepo.GetByID(ctx, categoryID); err != nil {
		if err == gorm.ErrRecordNotFound {
			return &WebsiteServiceError{
				Code:    ErrCodeCategoryNotFound,
				Message: "Template category not found",
			}
		}
		return err
	}

	// Validate new parent if provided
	if newParentID != nil {
		if _, err := s.categoryRepo.GetByID(ctx, *newParentID); err != nil {
			if err == gorm.ErrRecordNotFound {
				return &WebsiteServiceError{
					Code:    ErrCodeCategoryNotFound,
					Message: "Parent category not found",
				}
			}
			return err
		}

		// Prevent circular reference
		if *newParentID == categoryID {
			return &WebsiteServiceError{
				Code:    ErrCodeValidationFailed,
				Message: "Category cannot be its own parent",
			}
		}
	}

	return s.categoryRepo.MoveCategoryToParent(ctx, categoryID, newParentID)
}

// ValidateCategorySlug validates category slug uniqueness
func (s *templateCategoryService) ValidateCategorySlug(ctx context.Context, slug string, excludeID uint) error {
	exists, err := s.categoryRepo.CheckSlugExists(ctx, slug, excludeID)
	if err != nil {
		return err
	}
	if exists {
		return &WebsiteServiceError{
			Code:    ErrCodeCategorySlugExists,
			Message: "Category slug already exists",
			Field:   "slug",
		}
	}
	return nil
}

// UpdateTemplateCount updates template count for a category
func (s *templateCategoryService) UpdateTemplateCount(ctx context.Context, categoryID uint) error {
	count, err := s.templateRepo.CountByCategory(ctx, categoryID)
	if err != nil {
		return err
	}

	return s.categoryRepo.UpdateTemplateCount(ctx, categoryID, int(count))
}
