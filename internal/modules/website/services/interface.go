package services

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// WebsiteService defines the interface for website business logic
type WebsiteService interface {
	// CreateWebsite creates a new website with validation
	CreateWebsite(ctx context.Context, tenantID uint, req *models.WebsiteCreateRequest) (*models.WebsiteResponse, error)

	// GetWebsite retrieves a website by ID with tenant validation
	GetWebsite(ctx context.Context, tenantID, websiteID uint) (*models.WebsiteResponse, error)

	// GetWebsiteByDomain retrieves a website by domain
	GetWebsiteByDomain(ctx context.Context, domain string) (*models.WebsiteResponse, error)

	// GetWebsiteBySubdomain retrieves a website by subdomain
	GetWebsiteBySubdomain(ctx context.Context, subdomain string) (*models.WebsiteResponse, error)

	// UpdateWebsite updates a website with validation
	UpdateWebsite(ctx context.Context, tenantID, websiteID uint, req *models.WebsiteUpdateRequest) (*models.WebsiteResponse, error)

	// DeleteWebsite soft deletes a website
	DeleteWebsite(ctx context.Context, tenantID, websiteID uint) error

	// ListWebsites retrieves websites for a tenant
	ListWebsites(ctx context.Context, tenantID uint, filter models.WebsiteFilter) (*models.WebsiteListResponse, error)

	// ListWebsitesWithCursor retrieves websites using cursor-based pagination
	ListWebsitesWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) (*dto.WebsiteListResponse, error)

	// ActivateWebsite activates a website
	ActivateWebsite(ctx context.Context, tenantID, websiteID uint) error

	// DeactivateWebsite deactivates a website
	DeactivateWebsite(ctx context.Context, tenantID, websiteID uint) error

	// SuspendWebsite suspends a website
	SuspendWebsite(ctx context.Context, tenantID, websiteID uint) error

	// ValidateDomain validates domain availability and format
	ValidateDomain(ctx context.Context, domain string, excludeWebsiteID uint) error

	// ValidateSubdomain validates subdomain availability and format
	ValidateSubdomain(ctx context.Context, subdomain string, excludeWebsiteID uint) error

	// GetWebsiteStats retrieves website statistics
	GetWebsiteStats(ctx context.Context, tenantID uint) (*WebsiteStats, error)

	// GetWebsiteContext creates website context from domain or subdomain
	GetWebsiteContext(ctx context.Context, domain string) (*models.WebsiteContext, error)

	// ValidateWebsiteAccess validates if tenant has access to website
	ValidateWebsiteAccess(ctx context.Context, tenantID, websiteID uint) error

	// GetWebsiteWithDetails retrieves website with all details
	GetWebsiteWithDetails(ctx context.Context, tenantID, websiteID uint) (*models.Website, error)

	// CloneWebsite clones a website for the same tenant
	CloneWebsite(ctx context.Context, tenantID, sourceWebsiteID uint, newName string) (*models.WebsiteResponse, error)

	// TransferWebsite transfers website to another tenant (admin only)
	TransferWebsite(ctx context.Context, websiteID, newTenantID uint) error
}

// WebsitePageService defines the interface for website page business logic
type WebsitePageService interface {
	// Create creates a new website page
	Create(ctx context.Context, page *models.WebsitePage) error

	// GetByID retrieves a page by ID
	GetByID(ctx context.Context, id uint, userID uint) (*models.WebsitePage, error)

	// GetBySlug retrieves a page by slug
	GetBySlug(ctx context.Context, websiteID uint, slug string) (*models.WebsitePage, error)

	// GetPublishedPages retrieves published pages for a website
	GetPublishedPages(ctx context.Context, websiteID uint) ([]*models.WebsitePage, error)

	// GetHomepage retrieves the homepage for a website
	GetHomepage(ctx context.Context, websiteID uint) (*models.WebsitePage, error)

	// Update updates a page
	Update(ctx context.Context, id uint, updates map[string]interface{}, userID uint) error

	// Delete deletes a page
	Delete(ctx context.Context, id uint, userID uint) error

	// List retrieves pages with filtering
	List(ctx context.Context, filter models.WebsitePageFilter, userID uint) ([]*models.WebsitePage, int64, error)

	// ListWithCursor retrieves pages with cursor-based pagination
	ListWithCursor(ctx context.Context, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}, userID uint) ([]*models.WebsitePage, *pagination.CursorResponse, error)

	// Search searches pages by query
	Search(ctx context.Context, websiteID uint, query string, limit int, userID uint) ([]*models.WebsitePage, error)

	// PublishPage publishes a page
	PublishPage(ctx context.Context, id uint, userID uint) error

	// UnpublishPage unpublishes a page
	UnpublishPage(ctx context.Context, id uint, userID uint) error

	// SchedulePage schedules a page for publishing
	SchedulePage(ctx context.Context, id uint, scheduledAt *time.Time, userID uint) error

	// SetAsHomepage sets a page as homepage
	SetAsHomepage(ctx context.Context, websiteID, pageID uint, userID uint) error

	// IncrementViewCount increments page view count
	IncrementViewCount(ctx context.Context, id uint) error

	// ValidatePageData validates page data
	ValidatePageData(ctx context.Context, page *models.WebsitePage) error

	// CheckSlugAvailability checks if slug is available
	CheckSlugAvailability(ctx context.Context, websiteID uint, slug string, excludeID uint) error

	// DuplicatePage duplicates a page
	DuplicatePage(ctx context.Context, sourceID uint, newSlug, newTitle string, userID uint) (*models.WebsitePage, error)

	// GetPageAnalytics retrieves page analytics
	GetPageAnalytics(ctx context.Context, pageID uint, userID uint) (map[string]interface{}, error)

	// BulkUpdateStatus updates status for multiple pages
	BulkUpdateStatus(ctx context.Context, pageIDs []uint, status models.PageStatus, userID uint) error

	// ProcessScheduledPages processes scheduled pages
	ProcessScheduledPages(ctx context.Context) error

	// GetSEOData retrieves SEO data for a page
	GetSEOData(ctx context.Context, pageID uint) (map[string]interface{}, error)

	// GenerateSitemap generates sitemap for a website
	GenerateSitemap(ctx context.Context, websiteID uint) ([]*models.WebsitePage, error)

	// GenerateRSSFeed generates RSS feed for a website
	GenerateRSSFeed(ctx context.Context, websiteID uint, limit int) ([]*models.WebsitePage, error)

	// ValidatePermissions validates user permissions for a page
	ValidatePermissions(ctx context.Context, pageID uint, userID uint, action string) error
}

// WebsiteBlockDefinitionService defines the interface for website block definition business logic
type WebsiteBlockDefinitionService interface {
	// Create creates a new block definition
	Create(ctx context.Context, definition *models.WebsiteBlockDefinition, userID uint) error

	// GetByID retrieves a block definition by ID
	GetByID(ctx context.Context, id uint) (*models.WebsiteBlockDefinition, error)

	// GetByType retrieves a block definition by type
	GetByType(ctx context.Context, tenantID *uint, blockType string) (*models.WebsiteBlockDefinition, error)

	// GetByCategory retrieves block definitions by category
	GetByCategory(ctx context.Context, tenantID *uint, category string) ([]*models.WebsiteBlockDefinition, error)

	// GetAvailable retrieves available block definitions for a tenant
	GetAvailable(ctx context.Context, tenantID uint) ([]*models.WebsiteBlockDefinition, error)

	// Update updates a block definition
	Update(ctx context.Context, id uint, updates map[string]interface{}, userID uint) error

	// Delete deletes a block definition
	Delete(ctx context.Context, id uint, userID uint) error

	// List retrieves block definitions with filtering
	List(ctx context.Context, filter models.WebsiteBlockDefinitionFilter) ([]*models.WebsiteBlockDefinition, int64, error)

	// Search searches block definitions by query
	Search(ctx context.Context, tenantID *uint, query string, limit int) ([]*models.WebsiteBlockDefinition, error)

	// ValidateDefinition validates block definition data
	ValidateDefinition(ctx context.Context, definition *models.WebsiteBlockDefinition) error

	// ValidateFieldSchema validates field schema
	ValidateFieldSchema(ctx context.Context, schema models.JSONMap) error

	// ValidateStyleSchema validates style schema
	ValidateStyleSchema(ctx context.Context, schema models.JSONMap) error

	// UpdateUsage updates usage count for a block type
	UpdateUsage(ctx context.Context, blockType string, tenantID *uint) error

	// GetMostUsed retrieves most used block definitions
	GetMostUsed(ctx context.Context, tenantID *uint, limit int) ([]*models.WebsiteBlockDefinition, error)

	// GetRecentlyUsed retrieves recently used block definitions
	GetRecentlyUsed(ctx context.Context, tenantID *uint, limit int) ([]*models.WebsiteBlockDefinition, error)

	// GetCategories retrieves available block categories
	GetCategories(ctx context.Context, tenantID *uint) ([]models.BlockCategory, error)

	// GetDependencies retrieves dependencies for block definitions
	GetDependencies(ctx context.Context, definitionIDs []uint) (map[string][]string, error)

	// CloneDefinition clones a block definition
	CloneDefinition(ctx context.Context, sourceID uint, tenantID uint, newBlockType string, userID uint) (*models.WebsiteBlockDefinition, error)

	// ExportDefinition exports a block definition
	ExportDefinition(ctx context.Context, id uint, userID uint) (models.JSONMap, error)

	// ImportDefinition imports a block definition
	ImportDefinition(ctx context.Context, tenantID *uint, data models.JSONMap, userID uint) (*models.WebsiteBlockDefinition, error)

	// GetUsageStats retrieves usage statistics
	GetUsageStats(ctx context.Context, tenantID *uint) (map[string]interface{}, error)

	// ValidateBlockType validates block type uniqueness
	ValidateBlockType(ctx context.Context, tenantID *uint, blockType string, excludeID uint) error

	// GetCompatibleDefinitions retrieves compatible block definitions
	GetCompatibleDefinitions(ctx context.Context, tenantID *uint, requirements map[string]bool) ([]*models.WebsiteBlockDefinition, error)

	// BulkImport imports multiple block definitions
	BulkImport(ctx context.Context, tenantID *uint, definitions []models.JSONMap, userID uint) ([]*models.WebsiteBlockDefinition, error)

	// ValidatePermissions validates user permissions for a block definition
	ValidatePermissions(ctx context.Context, definitionID uint, userID uint, action string) error
}

// WebsitePageBlockService defines the interface for website page block business logic
type WebsitePageBlockService interface {
	// Create creates a new page block
	Create(ctx context.Context, block *models.WebsitePageBlock, userID uint) error

	// GetByID retrieves a page block by ID
	GetByID(ctx context.Context, id uint, userID uint) (*models.WebsitePageBlock, error)

	// GetByPage retrieves all blocks for a page
	GetByPage(ctx context.Context, pageID uint, userID uint) ([]*models.WebsitePageBlock, error)

	// GetByContainer retrieves blocks for a specific container
	GetByContainer(ctx context.Context, pageID uint, containerName string, userID uint) ([]*models.WebsitePageBlock, error)

	// Update updates a page block
	Update(ctx context.Context, id uint, updates map[string]interface{}, userID uint) error

	// Delete deletes a page block
	Delete(ctx context.Context, id uint, userID uint) error

	// MoveBlock moves a block to a different container or position
	MoveBlock(ctx context.Context, blockID uint, parentBlockID *uint, containerName string, orderIndex int, userID uint) error

	// ReorderBlocks reorders blocks within a container
	ReorderBlocks(ctx context.Context, pageID uint, containerName string, blockIDs []uint, userID uint) error

	// DuplicateBlock duplicates a block
	DuplicateBlock(ctx context.Context, sourceID uint, newName string, includeChildren bool, userID uint) (*models.WebsitePageBlock, error)

	// ConvertBlockType converts a block to a different type
	ConvertBlockType(ctx context.Context, blockID uint, newType string, newData models.JSONMap, userID uint) error

	// GetBlockHierarchy retrieves the block hierarchy for a page
	GetBlockHierarchy(ctx context.Context, pageID uint, userID uint) ([]*models.WebsitePageBlock, error)

	// ValidateBlockData validates block data against its definition
	ValidateBlockData(ctx context.Context, blockType string, data models.JSONMap) error

	// ValidateBlockHierarchy validates block hierarchy constraints
	ValidateBlockHierarchy(ctx context.Context, blockID, parentBlockID uint) error

	// UpdateVisibility updates block visibility settings
	UpdateVisibility(ctx context.Context, blockID uint, desktop, tablet, mobile bool, userID uint) error

	// SetAsGlobal sets a block as global
	SetAsGlobal(ctx context.Context, blockID uint, userID uint) error

	// LockBlock locks a block for editing
	LockBlock(ctx context.Context, blockID uint, userID uint) error

	// UnlockBlock unlocks a block
	UnlockBlock(ctx context.Context, blockID uint, userID uint) error

	// RecordImpression records a block impression
	RecordImpression(ctx context.Context, blockID uint) error

	// RecordClick records a block click
	RecordClick(ctx context.Context, blockID uint) error

	// GetBlockAnalytics retrieves block analytics
	GetBlockAnalytics(ctx context.Context, blockID uint, userID uint) (map[string]interface{}, error)

	// GetVariantBlocks retrieves variant blocks for A/B testing
	GetVariantBlocks(ctx context.Context, pageID uint, variantGroup string) ([]*models.WebsitePageBlock, error)

	// SelectVariant selects a variant block for display
	SelectVariant(ctx context.Context, variantBlocks []*models.WebsitePageBlock) *models.WebsitePageBlock

	// RenderBlock renders a block to HTML
	RenderBlock(ctx context.Context, block *models.WebsitePageBlock) (string, error)

	// ValidatePermissions validates user permissions for a block
	ValidatePermissions(ctx context.Context, blockID uint, userID uint, action string) error

	// BulkCreate creates multiple blocks
	BulkCreate(ctx context.Context, blocks []*models.WebsitePageBlock, userID uint) error

	// BulkUpdate updates multiple blocks
	BulkUpdate(ctx context.Context, updates map[uint]map[string]interface{}, userID uint) error
}

// WebsiteSettingService defines the interface for website setting business logic
type WebsiteSettingService interface {
	// CreateSetting creates a new website setting
	CreateSetting(ctx context.Context, tenantID, websiteID uint, req *models.WebsiteSettingCreateRequest) (*models.WebsiteSettingResponse, error)

	// GetSetting retrieves a setting by ID
	GetSetting(ctx context.Context, tenantID, websiteID, settingID uint) (*models.WebsiteSettingResponse, error)

	// GetSettingByKey retrieves a setting by key
	GetSettingByKey(ctx context.Context, tenantID, websiteID uint, key string) (*models.WebsiteSettingResponse, error)

	// UpdateSetting updates a setting
	UpdateSetting(ctx context.Context, tenantID, websiteID, settingID uint, req *models.WebsiteSettingUpdateRequest) (*models.WebsiteSettingResponse, error)

	// DeleteSetting deletes a setting
	DeleteSetting(ctx context.Context, tenantID, websiteID, settingID uint) error

	// ListSettings retrieves settings for a website
	ListSettings(ctx context.Context, tenantID, websiteID uint, filter models.WebsiteSettingFilter) (*models.WebsiteSettingListResponse, error)

	// GetSettingsByCategory retrieves settings by category
	GetSettingsByCategory(ctx context.Context, tenantID, websiteID uint, category models.WebsiteSettingCategory) ([]*models.WebsiteSettingResponse, error)

	// BulkUpdateSettings updates multiple settings
	BulkUpdateSettings(ctx context.Context, tenantID, websiteID uint, req *models.WebsiteSettingBulkUpdateRequest) error

	// GetSettingValue retrieves a setting value by key
	GetSettingValue(ctx context.Context, tenantID, websiteID uint, key string) (string, error)

	// ValidateSettingValue validates setting value based on data type and rules
	ValidateSettingValue(ctx context.Context, setting *models.WebsiteSetting, value string) error

	// GetPublicSettings retrieves public settings for a website
	GetPublicSettings(ctx context.Context, websiteID uint) ([]*models.WebsiteSettingResponse, error)

	// InitializeDefaultSettings creates default settings for a new website
	InitializeDefaultSettings(ctx context.Context, websiteID, tenantID uint) error

	// ExportSettings exports website settings to JSON
	ExportSettings(ctx context.Context, tenantID, websiteID uint) (map[string]interface{}, error)

	// ImportSettings imports website settings from JSON
	ImportSettings(ctx context.Context, tenantID, websiteID uint, settings map[string]interface{}) error

	// ValidateSettingKey validates if setting key is valid and unique
	ValidateSettingKey(ctx context.Context, websiteID uint, key string, excludeID uint) error

	// GetSettingsByDataType retrieves settings by data type
	GetSettingsByDataType(ctx context.Context, tenantID, websiteID uint, dataType models.WebsiteSettingDataType) ([]*models.WebsiteSettingResponse, error)
}

// WebsiteThemeService defines the interface for website theme business logic
type WebsiteThemeService interface {
	// CreateTheme creates a new website theme
	CreateTheme(ctx context.Context, tenantID, websiteID uint, req *models.WebsiteThemeCreateRequest) (*models.WebsiteThemeResponse, error)

	// GetTheme retrieves a theme by ID
	GetTheme(ctx context.Context, tenantID, websiteID, themeID uint) (*models.WebsiteThemeResponse, error)

	// GetThemeByName retrieves a theme by name
	GetThemeByName(ctx context.Context, tenantID, websiteID uint, name string) (*models.WebsiteThemeResponse, error)

	// UpdateTheme updates a theme
	UpdateTheme(ctx context.Context, tenantID, websiteID, themeID uint, req *models.WebsiteThemeUpdateRequest) (*models.WebsiteThemeResponse, error)

	// DeleteTheme soft deletes a theme
	DeleteTheme(ctx context.Context, tenantID, websiteID, themeID uint) error

	// ListThemes retrieves themes for a website
	ListThemes(ctx context.Context, tenantID, websiteID uint, filter models.WebsiteThemeFilter) (*models.WebsiteThemeListResponse, error)

	// ActivateTheme activates a theme
	ActivateTheme(ctx context.Context, tenantID, websiteID, themeID uint) error

	// DeactivateTheme deactivates a theme
	DeactivateTheme(ctx context.Context, tenantID, websiteID, themeID uint) error

	// GetActiveTheme retrieves the active theme for a website
	GetActiveTheme(ctx context.Context, tenantID, websiteID uint) (*models.WebsiteThemeResponse, error)

	// GetDefaultTheme retrieves the default theme for a website
	GetDefaultTheme(ctx context.Context, tenantID, websiteID uint) (*models.WebsiteThemeResponse, error)

	// SetAsDefault sets a theme as default
	SetAsDefault(ctx context.Context, tenantID, websiteID, themeID uint) error

	// CloneTheme clones a theme
	CloneTheme(ctx context.Context, tenantID, websiteID, sourceThemeID uint, req *models.WebsiteThemeCloneRequest) (*models.WebsiteThemeResponse, error)

	// GetThemeCustomizations retrieves theme customizations
	GetThemeCustomizations(ctx context.Context, tenantID, websiteID, themeID uint) (map[string]interface{}, error)

	// UpdateThemeCustomizations updates theme customizations
	UpdateThemeCustomizations(ctx context.Context, tenantID, websiteID, themeID uint, customizations map[string]interface{}) error

	// GetThemeConfig retrieves theme configuration
	GetThemeConfig(ctx context.Context, tenantID, websiteID, themeID uint) (models.JSONMap, error)

	// UpdateThemeConfig updates theme configuration
	UpdateThemeConfig(ctx context.Context, tenantID, websiteID, themeID uint, config models.JSONMap) error

	// ValidateThemeName validates if theme name is valid and unique
	ValidateThemeName(ctx context.Context, websiteID uint, name string, excludeID uint) error

	// GetChildThemes retrieves child themes for a parent theme
	GetChildThemes(ctx context.Context, tenantID, websiteID, parentThemeID uint) ([]*models.WebsiteThemeResponse, error)

	// CreateChildTheme creates a child theme from parent
	CreateChildTheme(ctx context.Context, tenantID, websiteID, parentThemeID uint, req *models.WebsiteThemeCreateRequest) (*models.WebsiteThemeResponse, error)

	// InstallDefaultTheme installs default theme for a new website
	InstallDefaultTheme(ctx context.Context, websiteID, tenantID uint) error

	// GetThemePreview generates theme preview data
	GetThemePreview(ctx context.Context, tenantID, websiteID, themeID uint) (map[string]interface{}, error)

	// ValidateThemeAssets validates theme assets and paths
	ValidateThemeAssets(ctx context.Context, theme *models.WebsiteTheme) error
}

// DomainService defines the interface for domain management
type DomainService interface {
	// ValidateDomain validates domain format and availability
	ValidateDomain(ctx context.Context, domain string, excludeWebsiteID uint) error

	// ValidateSubdomain validates subdomain format and availability
	ValidateSubdomain(ctx context.Context, subdomain string, excludeWebsiteID uint) error

	// GetDomainInfo analyzes domain and returns information
	GetDomainInfo(ctx context.Context, domain, subdomain string) (*models.DomainInfo, error)

	// CheckDomainAvailability checks if domain is available
	CheckDomainAvailability(ctx context.Context, domain string, excludeWebsiteID uint) (bool, error)

	// CheckSubdomainAvailability checks if subdomain is available
	CheckSubdomainAvailability(ctx context.Context, subdomain string, excludeWebsiteID uint) (bool, error)

	// NormalizeDomain normalizes domain name
	NormalizeDomain(domain string) string

	// NormalizeSubdomain normalizes subdomain name
	NormalizeSubdomain(subdomain string) string

	// GetReservedSubdomains returns list of reserved subdomains
	GetReservedSubdomains() []string

	// IsReservedSubdomain checks if subdomain is reserved
	IsReservedSubdomain(subdomain string) bool

	// VerifyDomainOwnership initiates domain ownership verification
	VerifyDomainOwnership(ctx context.Context, websiteID uint, domain string) error

	// CheckDomainVerification checks domain verification status
	CheckDomainVerification(ctx context.Context, websiteID uint, domain string) (bool, error)

	// GetDomainVerificationInstructions returns domain verification instructions
	GetDomainVerificationInstructions(ctx context.Context, websiteID uint, domain string) (map[string]interface{}, error)
}

// WebsiteStats represents website statistics
type WebsiteStats struct {
	TotalWebsites      int64            `json:"total_websites"`
	ActiveWebsites     int64            `json:"active_websites"`
	InactiveWebsites   int64            `json:"inactive_websites"`
	SuspendedWebsites  int64            `json:"suspended_websites"`
	WebsitesByStatus   map[string]int64 `json:"websites_by_status"`
	TotalSettings      int64            `json:"total_settings"`
	TotalThemes        int64            `json:"total_themes"`
	ActiveThemes       int64            `json:"active_themes"`
	CustomDomains      int64            `json:"custom_domains"`
	Subdomains         int64            `json:"subdomains"`
	ThemesByStatus     map[string]int64 `json:"themes_by_status"`
	SettingsByCategory map[string]int64 `json:"settings_by_category"`
	LastUpdated        string           `json:"last_updated"`
}

// WebsiteServiceError represents website service errors
type WebsiteServiceError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Field   string `json:"field,omitempty"`
}

func (e *WebsiteServiceError) Error() string {
	return e.Message
}

// Common error codes
const (
	ErrCodeWebsiteNotFound        = "WEBSITE_NOT_FOUND"
	ErrCodeWebsiteAccessDenied    = "WEBSITE_ACCESS_DENIED"
	ErrCodeDomainAlreadyExists    = "DOMAIN_ALREADY_EXISTS"
	ErrCodeSubdomainAlreadyExists = "SUBDOMAIN_ALREADY_EXISTS"
	ErrCodeInvalidDomain          = "INVALID_DOMAIN"
	ErrCodeInvalidSubdomain       = "INVALID_SUBDOMAIN"
	ErrCodeReservedSubdomain      = "RESERVED_SUBDOMAIN"
	ErrCodeSettingNotFound        = "SETTING_NOT_FOUND"
	ErrCodeSettingKeyExists       = "SETTING_KEY_EXISTS"
	ErrCodeInvalidSettingValue    = "INVALID_SETTING_VALUE"
	ErrCodeThemeNotFound          = "THEME_NOT_FOUND"
	ErrCodeThemeNameExists        = "THEME_NAME_EXISTS"
	ErrCodeActiveThemeRequired    = "ACTIVE_THEME_REQUIRED"
	ErrCodeDefaultThemeRequired   = "DEFAULT_THEME_REQUIRED"
	ErrCodeValidationFailed       = "VALIDATION_FAILED"
	ErrCodeInternalError          = "INTERNAL_ERROR"

	// Template specific error codes
	ErrCodeTemplateNotFound     = "TEMPLATE_NOT_FOUND"
	ErrCodeTemplateSlugExists   = "TEMPLATE_SLUG_EXISTS"
	ErrCodeCategoryNotFound     = "CATEGORY_NOT_FOUND"
	ErrCodeCategorySlugExists   = "CATEGORY_SLUG_EXISTS"
	ErrCodeInvalidTemplateType  = "INVALID_TEMPLATE_TYPE"
	ErrCodeInvalidCategoryLevel = "INVALID_CATEGORY_LEVEL"
)

// WebsiteTemplateService defines the interface for website template business logic
type WebsiteTemplateService interface {
	// CreateTemplate creates a new website template
	CreateTemplate(ctx context.Context, req *dto.CreateTemplateRequest) (*dto.TemplateResponse, error)

	// GetTemplate retrieves a template by ID or slug
	GetTemplate(ctx context.Context, req *dto.GetTemplateRequest) (*dto.TemplateResponse, error)

	// UpdateTemplate updates a template
	UpdateTemplate(ctx context.Context, req *dto.UpdateTemplateRequest) (*dto.TemplateResponse, error)

	// DeleteTemplate soft deletes a template
	DeleteTemplate(ctx context.Context, id uint) error

	// ListTemplates retrieves templates with filtering and pagination
	ListTemplates(ctx context.Context, req *dto.ListTemplatesRequest) (*dto.TemplateListResponse, error)

	// GetFeaturedTemplates retrieves featured templates
	GetFeaturedTemplates(ctx context.Context, limit int) ([]*dto.TemplateResponse, error)

	// GetPopularTemplates retrieves popular templates
	GetPopularTemplates(ctx context.Context, limit int) ([]*dto.TemplateResponse, error)

	// SearchTemplates searches templates by query
	SearchTemplates(ctx context.Context, query string, limit int) ([]*dto.TemplateResponse, error)

	// GetTemplatesByCategory retrieves templates by category
	GetTemplatesByCategory(ctx context.Context, categoryID uint) ([]*dto.TemplateResponse, error)

	// GetTemplatesByType retrieves templates by type
	GetTemplatesByType(ctx context.Context, templateType string) ([]*dto.TemplateResponse, error)

	// GetTemplateStats retrieves template statistics
	GetTemplateStats(ctx context.Context) (*dto.TemplateStatsResponse, error)

	// IncrementDownloadCount increments template download count
	IncrementDownloadCount(ctx context.Context, id uint) error

	// IncrementUsageCount increments template usage count
	IncrementUsageCount(ctx context.Context, id uint) error

	// UpdateTemplateRating updates template rating
	UpdateTemplateRating(ctx context.Context, id uint, rating float64) error

	// PublishTemplate publishes a template
	PublishTemplate(ctx context.Context, id uint) error

	// ArchiveTemplate archives a template
	ArchiveTemplate(ctx context.Context, id uint) error

	// ValidateTemplateSlug validates template slug uniqueness
	ValidateTemplateSlug(ctx context.Context, slug string, excludeID uint) error

	// CloneTemplate creates a copy of an existing template
	CloneTemplate(ctx context.Context, sourceID uint, newSlug, newName string) (*dto.TemplateResponse, error)
}

// TemplateCategoryService defines the interface for template category business logic
type TemplateCategoryService interface {
	// CreateCategory creates a new template category
	CreateCategory(ctx context.Context, req *dto.CreateTemplateCategoryRequest) (*dto.TemplateCategoryResponse, error)

	// GetCategory retrieves a category by ID or slug
	GetCategory(ctx context.Context, req *dto.GetTemplateCategoryRequest) (*dto.TemplateCategoryResponse, error)

	// UpdateCategory updates a category
	UpdateCategory(ctx context.Context, req *dto.UpdateTemplateCategoryRequest) (*dto.TemplateCategoryResponse, error)

	// DeleteCategory soft deletes a category
	DeleteCategory(ctx context.Context, id uint) error

	// ListCategories retrieves categories with filtering and pagination
	ListCategories(ctx context.Context, req *dto.ListTemplateCategoriesRequest) (*dto.TemplateCategoryListResponse, error)

	// GetCategoryTree retrieves the complete category tree
	GetCategoryTree(ctx context.Context) (*dto.TemplateCategoryTreeResponse, error)

	// GetMainCategories retrieves main categories (level 0)
	GetMainCategories(ctx context.Context) ([]*dto.TemplateCategoryResponse, error)

	// GetSubcategories retrieves subcategories for a parent
	GetSubcategories(ctx context.Context, parentID uint) ([]*dto.TemplateCategoryResponse, error)

	// GetFeaturedCategories retrieves featured categories
	GetFeaturedCategories(ctx context.Context) ([]*dto.TemplateCategoryResponse, error)

	// GetCategoryStats retrieves category statistics
	GetCategoryStats(ctx context.Context) (*dto.TemplateCategoryStatsResponse, error)

	// MoveCategoryToParent moves a category to a new parent
	MoveCategoryToParent(ctx context.Context, categoryID uint, newParentID *uint) error

	// ValidateCategorySlug validates category slug uniqueness
	ValidateCategorySlug(ctx context.Context, slug string, excludeID uint) error

	// UpdateTemplateCount updates template count for a category
	UpdateTemplateCount(ctx context.Context, categoryID uint) error
}
