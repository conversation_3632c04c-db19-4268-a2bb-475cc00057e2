package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type websitePageService struct {
	pageRepo    repositories.WebsitePageRepository
	websiteRepo repositories.WebsiteRepository
}

// NewWebsitePageService creates a new website page service
func NewWebsitePageService(
	pageRepo repositories.WebsitePageRepository,
	websiteRepo repositories.WebsiteRepository,
) WebsitePageService {
	return &websitePageService{
		pageRepo:    pageRepo,
		websiteRepo: websiteRepo,
	}
}

func (s *websitePageService) Create(ctx context.Context, page *models.WebsitePage) error {
	// Validate website exists and user has access
	_, err := s.websiteRepo.GetByTenantID(ctx, page.TenantID, page.WebsiteID)
	if err != nil {
		return fmt.Errorf("website not found or access denied: %w", err)
	}

	// Validate page data
	if err := s.ValidatePageData(ctx, page); err != nil {
		return fmt.Errorf("page validation failed: %w", err)
	}

	// Check slug availability
	if err := s.CheckSlugAvailability(ctx, page.WebsiteID, page.Slug, 0); err != nil {
		return err
	}

	// Set defaults
	if page.Status == "" {
		page.Status = models.PageStatusDraft
	}
	if page.Visibility == "" {
		page.Visibility = models.PageVisibilityDraft
	}

	// Ensure only one homepage per website
	if page.IsHomepage {
		if err := s.pageRepo.SetAsHomepage(ctx, page.WebsiteID, 0); err != nil {
			return fmt.Errorf("failed to unset current homepage: %w", err)
		}
	}

	return s.pageRepo.Create(ctx, page)
}

func (s *websitePageService) GetByID(ctx context.Context, id uint, userID uint) (*models.WebsitePage, error) {
	page, err := s.pageRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("page not found: %w", err)
	}

	// Validate access permissions
	if err := s.ValidatePermissions(ctx, id, userID, "read"); err != nil {
		return nil, err
	}

	return page, nil
}

func (s *websitePageService) GetBySlug(ctx context.Context, websiteID uint, slug string) (*models.WebsitePage, error) {
	return s.pageRepo.GetBySlug(ctx, websiteID, slug)
}

func (s *websitePageService) GetPublishedPages(ctx context.Context, websiteID uint) ([]*models.WebsitePage, error) {
	return s.pageRepo.GetPublishedPages(ctx, websiteID)
}

func (s *websitePageService) GetHomepage(ctx context.Context, websiteID uint) (*models.WebsitePage, error) {
	return s.pageRepo.GetHomepage(ctx, websiteID)
}

func (s *websitePageService) Update(ctx context.Context, id uint, updates map[string]interface{}, userID uint) error {
	// Get existing page
	page, err := s.pageRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("page not found: %w", err)
	}

	// Validate permissions
	if err := s.ValidatePermissions(ctx, id, userID, "update"); err != nil {
		return err
	}

	// Validate slug if being updated
	if newSlug, ok := updates["slug"].(string); ok && newSlug != page.Slug {
		if err := s.CheckSlugAvailability(ctx, page.WebsiteID, newSlug, id); err != nil {
			return err
		}
	}

	// Handle homepage status
	if isHomepage, ok := updates["is_homepage"].(bool); ok && isHomepage && !page.IsHomepage {
		if err := s.pageRepo.SetAsHomepage(ctx, page.WebsiteID, id); err != nil {
			return fmt.Errorf("failed to set as homepage: %w", err)
		}
		delete(updates, "is_homepage") // Remove from updates as it's handled separately
	}

	// Set updated timestamp
	updates["updated_by"] = userID

	return s.pageRepo.Update(ctx, id, updates)
}

func (s *websitePageService) Delete(ctx context.Context, id uint, userID uint) error {
	// Get existing page
	page, err := s.pageRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("page not found: %w", err)
	}

	// Validate permissions
	if err := s.ValidatePermissions(ctx, id, userID, "delete"); err != nil {
		return err
	}

	// Cannot delete homepage
	if page.IsHomepage {
		return fmt.Errorf("cannot delete homepage")
	}

	return s.pageRepo.Delete(ctx, id)
}

func (s *websitePageService) List(ctx context.Context, filter models.WebsitePageFilter, userID uint) ([]*models.WebsitePage, int64, error) {
	// Validate website access if specified
	if filter.WebsiteID != nil {
		if err := s.validateWebsiteAccess(ctx, *filter.TenantID, *filter.WebsiteID); err != nil {
			return nil, 0, err
		}
	}

	return s.pageRepo.List(ctx, filter)
}

func (s *websitePageService) ListWithCursor(ctx context.Context, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}, userID uint) ([]*models.WebsitePage, *pagination.CursorResponse, error) {
	// Get website to validate access
	website, err := s.websiteRepo.GetByID(ctx, websiteID)
	if err != nil {
		return nil, nil, fmt.Errorf("website not found: %w", err)
	}

	// Validate access
	if err := s.validateWebsiteAccess(ctx, website.TenantID, websiteID); err != nil {
		return nil, nil, err
	}

	return s.pageRepo.ListWithCursor(ctx, websiteID, req, filters)
}

func (s *websitePageService) Search(ctx context.Context, websiteID uint, query string, limit int, userID uint) ([]*models.WebsitePage, error) {
	// Validate website access
	website, err := s.websiteRepo.GetByID(ctx, websiteID)
	if err != nil {
		return nil, fmt.Errorf("website not found: %w", err)
	}

	if err := s.validateWebsiteAccess(ctx, website.TenantID, websiteID); err != nil {
		return nil, err
	}

	return s.pageRepo.Search(ctx, websiteID, query, limit)
}

func (s *websitePageService) PublishPage(ctx context.Context, id uint, userID uint) error {
	// Get existing page
	page, err := s.pageRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("page not found: %w", err)
	}

	// Validate permissions
	if err := s.ValidatePermissions(ctx, id, userID, "publish"); err != nil {
		return err
	}

	// Validate page can be published
	if page.Title == "" {
		return fmt.Errorf("page title is required for publishing")
	}

	return s.pageRepo.PublishPage(ctx, id)
}

func (s *websitePageService) UnpublishPage(ctx context.Context, id uint, userID uint) error {
	// Validate permissions
	if err := s.ValidatePermissions(ctx, id, userID, "publish"); err != nil {
		return err
	}

	return s.pageRepo.UnpublishPage(ctx, id)
}

func (s *websitePageService) SchedulePage(ctx context.Context, id uint, scheduledAt *time.Time, userID uint) error {
	// Validate permissions
	if err := s.ValidatePermissions(ctx, id, userID, "publish"); err != nil {
		return err
	}

	// Validate scheduled time is in the future
	if scheduledAt != nil && scheduledAt.Before(time.Now()) {
		return fmt.Errorf("scheduled time must be in the future")
	}

	return s.pageRepo.SchedulePage(ctx, id, scheduledAt)
}

func (s *websitePageService) SetAsHomepage(ctx context.Context, websiteID, pageID uint, userID uint) error {
	// Validate permissions
	if err := s.ValidatePermissions(ctx, pageID, userID, "update"); err != nil {
		return err
	}

	// Get page to validate it can be homepage
	page, err := s.pageRepo.GetByID(ctx, pageID)
	if err != nil {
		return fmt.Errorf("page not found: %w", err)
	}

	if page.WebsiteID != websiteID {
		return fmt.Errorf("page does not belong to specified website")
	}

	if page.Status != models.PageStatusPublished {
		return fmt.Errorf("only published pages can be set as homepage")
	}

	return s.pageRepo.SetAsHomepage(ctx, websiteID, pageID)
}

func (s *websitePageService) IncrementViewCount(ctx context.Context, id uint) error {
	return s.pageRepo.IncrementViewCount(ctx, id)
}

func (s *websitePageService) ValidatePageData(ctx context.Context, page *models.WebsitePage) error {
	// Required fields
	if page.Title == "" {
		return fmt.Errorf("page title is required")
	}
	if page.Slug == "" {
		return fmt.Errorf("page slug is required")
	}
	if page.WebsiteID == 0 {
		return fmt.Errorf("website ID is required")
	}
	if page.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}
	if page.CreatedBy == 0 {
		return fmt.Errorf("creator ID is required")
	}

	// Validate slug format
	if !isValidSlug(page.Slug) {
		return fmt.Errorf("invalid slug format")
	}

	// Validate password if page is password protected
	if page.PasswordProtected && page.PagePassword == "" {
		return fmt.Errorf("password is required for password-protected pages")
	}

	// Meta validation removed - now handled by centralized SEO module

	return nil
}

func (s *websitePageService) CheckSlugAvailability(ctx context.Context, websiteID uint, slug string, excludeID uint) error {
	exists, err := s.pageRepo.CheckSlugExists(ctx, websiteID, slug, excludeID)
	if err != nil {
		return fmt.Errorf("failed to check slug availability: %w", err)
	}
	if exists {
		return fmt.Errorf("slug '%s' is already in use", slug)
	}
	return nil
}

func (s *websitePageService) DuplicatePage(ctx context.Context, sourceID uint, newSlug, newTitle string, userID uint) (*models.WebsitePage, error) {
	// Validate permissions
	if err := s.ValidatePermissions(ctx, sourceID, userID, "read"); err != nil {
		return nil, err
	}

	// Get source page
	sourcePage, err := s.pageRepo.GetByID(ctx, sourceID)
	if err != nil {
		return nil, fmt.Errorf("source page not found: %w", err)
	}

	// Check slug availability
	if err := s.CheckSlugAvailability(ctx, sourcePage.WebsiteID, newSlug, 0); err != nil {
		return nil, err
	}

	// Create new page
	newPage := &models.WebsitePage{
		WebsiteID: sourcePage.WebsiteID,
		TenantID:  sourcePage.TenantID,
		Title:     newTitle,
		Slug:      newSlug,
		CreatedBy: userID,
	}

	if err := s.pageRepo.DuplicatePage(ctx, sourceID, newPage); err != nil {
		return nil, fmt.Errorf("failed to duplicate page: %w", err)
	}

	return newPage, nil
}

func (s *websitePageService) GetPageAnalytics(ctx context.Context, pageID uint, userID uint) (map[string]interface{}, error) {
	// Validate permissions
	if err := s.ValidatePermissions(ctx, pageID, userID, "read"); err != nil {
		return nil, err
	}

	return s.pageRepo.GetPageAnalytics(ctx, pageID)
}

func (s *websitePageService) BulkUpdateStatus(ctx context.Context, pageIDs []uint, status models.PageStatus, userID uint) error {
	// Validate permissions for all pages
	for _, pageID := range pageIDs {
		if err := s.ValidatePermissions(ctx, pageID, userID, "update"); err != nil {
			return fmt.Errorf("permission denied for page %d: %w", pageID, err)
		}
	}

	return s.pageRepo.BulkUpdateStatus(ctx, pageIDs, status)
}

func (s *websitePageService) ProcessScheduledPages(ctx context.Context) error {
	return s.pageRepo.ProcessScheduledPages(ctx)
}

func (s *websitePageService) GetSEOData(ctx context.Context, pageID uint) (map[string]interface{}, error) {
	page, err := s.pageRepo.GetByID(ctx, pageID)
	if err != nil {
		return nil, fmt.Errorf("page not found: %w", err)
	}

	seoData := map[string]interface{}{
		"title":               page.GetSEOTitle(),
		"description":         page.GetSEODescription(),
		"canonical_url":       page.CanonicalURL,
		"og_title":            page.GetSEOTitle(),
		"og_description":      page.GetSEODescription(),
		"og_image":            page.FeaturedImage,
		"twitter_title":       page.GetSEOTitle(),
		"twitter_description": page.GetSEODescription(),
		"twitter_image":       page.FeaturedImage,
		"structured_data":     s.generateStructuredData(page),
	}

	return seoData, nil
}

func (s *websitePageService) GenerateSitemap(ctx context.Context, websiteID uint) ([]*models.WebsitePage, error) {
	return s.pageRepo.GetSitemap(ctx, websiteID)
}

func (s *websitePageService) GenerateRSSFeed(ctx context.Context, websiteID uint, limit int) ([]*models.WebsitePage, error) {
	return s.pageRepo.GetRSSFeed(ctx, websiteID, limit)
}

func (s *websitePageService) ValidatePermissions(ctx context.Context, pageID uint, userID uint, action string) error {
	// Get page to check tenant
	_, err := s.pageRepo.GetByID(ctx, pageID)
	if err != nil {
		return fmt.Errorf("page not found: %w", err)
	}

	// TODO: Implement proper RBAC permission checking
	// For now, basic validation - user must belong to same tenant

	return nil
}

// Helper methods

func (s *websitePageService) validateWebsiteAccess(ctx context.Context, tenantID, websiteID uint) error {
	website, err := s.websiteRepo.GetByTenantID(ctx, tenantID, websiteID)
	if err != nil {
		return fmt.Errorf("website not found or access denied: %w", err)
	}
	if website == nil {
		return fmt.Errorf("website not found")
	}
	return nil
}

func (s *websitePageService) generateStructuredData(page *models.WebsitePage) map[string]interface{} {
	return map[string]interface{}{
		"@context":      "https://schema.org",
		"@type":         "WebPage",
		"name":          page.Title,
		"description":   page.Description,
		"url":           "", // Would need full URL construction
		"datePublished": page.PublishedAt,
		"dateModified":  page.UpdatedAt,
	}
}

func isValidSlug(slug string) bool {
	// Basic slug validation - alphanumeric and hyphens only
	if len(slug) == 0 || len(slug) > 100 {
		return false
	}

	for _, char := range slug {
		if !((char >= 'a' && char <= 'z') || (char >= '0' && char <= '9') || char == '-') {
			return false
		}
	}

	// Cannot start or end with hyphen
	return slug[0] != '-' && slug[len(slug)-1] != '-'
}
