package handlers

import (
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// WebsiteHandler handles website-related HTTP requests
type WebsiteHandler struct {
	websiteService services.WebsiteService
	validator      validator.Validator
}

// NewWebsiteHandler creates a new website handler
func NewWebsiteHandler(websiteService services.WebsiteService, validator validator.Validator) *WebsiteHandler {
	return &WebsiteHandler{
		websiteService: websiteService,
		validator:      validator,
	}
}

// CreateWebsite creates a new website
// @Summary      Create a new website
// @Description  Creates a new website with the provided configuration
// @Tags         Websites
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        request body dto.WebsiteCreateRequest true "Website creation data"
// @Success      201 {object} response.Response{data=dto.WebsiteResponse} "Website created successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to create website"
// @Router       /api/cms/v1/websites [post]
func (h *WebsiteHandler) CreateWebsite(c *gin.Context) {
	var req dto.WebsiteCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Validate exclusive fields (domain vs subdomain)
	if err := req.ValidateExclusiveFields(); err != nil {
		response.BadRequest(c.Writer, "Validation failed", err.Error())
		return
	}

	// Get tenant ID from context (set by middleware)
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Convert DTO to models
	serviceReq := &models.WebsiteCreateRequest{
		Name:        req.Name,
		Domain:      req.Domain,
		Subdomain:   req.Subdomain,
		Description: req.Description,
		ActiveTheme: req.ActiveTheme,
		CustomCSS:   req.CustomCSS,
		CustomJS:    req.CustomJS,
		SiteLogo:    req.SiteLogo,
		Favicon:     req.Favicon,
		Timezone:    req.Timezone,
		Language:    req.Language,
		SocialMedia: req.SocialMedia,
	}

	website, err := h.websiteService.CreateWebsite(c.Request.Context(), tenantID.(uint), serviceReq)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	// Convert models response to DTO response
	responseDTO := h.convertToWebsiteResponse(website)

	response.Created(c.Writer, responseDTO)
}

// GetWebsite handles GET /api/v1/websites/:id
func (h *WebsiteHandler) GetWebsite(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	website, err := h.websiteService.GetWebsite(c.Request.Context(), tenantID.(uint), websiteID)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, website)
}

// GetWebsiteWithDetails handles GET /api/v1/websites/:id/details
func (h *WebsiteHandler) GetWebsiteWithDetails(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	website, err := h.websiteService.GetWebsiteWithDetails(c.Request.Context(), tenantID.(uint), websiteID)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, website)
}

// UpdateWebsite handles PUT /api/v1/websites/:id
func (h *WebsiteHandler) UpdateWebsite(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	var req models.WebsiteUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	website, err := h.websiteService.UpdateWebsite(c.Request.Context(), tenantID.(uint), websiteID, &req)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, website)
}

// DeleteWebsite handles DELETE /api/v1/websites/:id
func (h *WebsiteHandler) DeleteWebsite(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	if err := h.websiteService.DeleteWebsite(c.Request.Context(), tenantID.(uint), websiteID); err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, gin.H{"message": "Website deleted successfully"})
}

// ListWebsites handles GET /api/cms/v1/websites/me/list
// ListWebsites lists user's websites with cursor-based pagination (personal endpoint)
// @Summary      List my websites
// @Description  Get paginated list of user's websites with filters using cursor pagination (no RBAC required)
// @Tags         Websites
// @Produce      json
// @Security     Bearer
// @Param        name query string false "Website name filter"
// @Param        domain query string false "Domain filter"
// @Param        status query string false "Status filter" Enums(active,inactive,suspended)
// @Param        language query string false "Language filter"
// @Param        cursor query string false "Pagination cursor"
// @Param        limit query int false "Items per page" default(20)
// @Param        sort_by query string false "Sort by field" Enums(id,name,domain,created_at,updated_at) default(created_at)
// @Param        sort_order query string false "Sort order" Enums(asc,desc) default(desc)
// @Success      200 {object} dto.WebsiteListResponse
// @Failure      400 {object} response.Response "Invalid filter parameters"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to retrieve websites"
// @Router       /api/cms/v1/websites/me/list [get]
func (h *WebsiteHandler) ListWebsites(c *gin.Context) {
	var filter dto.WebsiteFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		response.BadRequest(c.Writer, "Invalid filter parameters", err.Error())
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Create cursor request
	cursorReq := &pagination.CursorRequest{
		Cursor: filter.Cursor,
		Limit:  pagination.ValidateLimit(filter.Limit),
	}

	// Build filters map
	filters := make(map[string]interface{})
	if filter.Name != "" {
		filters["name"] = filter.Name
	}
	if filter.Domain != nil && *filter.Domain != "" {
		filters["domain"] = *filter.Domain
	}
	if filter.Status != nil {
		filters["status"] = *filter.Status
	}
	if filter.Language != "" {
		filters["language"] = filter.Language
	}
	if filter.SortBy != "" {
		filters["sort_by"] = filter.SortBy
	}
	if filter.SortOrder != "" {
		filters["sort_order"] = filter.SortOrder
	}

	websiteResponse, err := h.websiteService.ListWebsitesWithCursor(c.Request.Context(), tenantID.(uint), cursorReq, filters)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, websiteResponse)
}

// ActivateWebsite handles POST /api/v1/websites/:id/activate
func (h *WebsiteHandler) ActivateWebsite(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	if err := h.websiteService.ActivateWebsite(c.Request.Context(), tenantID.(uint), websiteID); err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, gin.H{"message": "Website activated successfully"})
}

// DeactivateWebsite handles POST /api/v1/websites/:id/deactivate
func (h *WebsiteHandler) DeactivateWebsite(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	if err := h.websiteService.DeactivateWebsite(c.Request.Context(), tenantID.(uint), websiteID); err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, gin.H{"message": "Website deactivated successfully"})
}

// SuspendWebsite handles POST /api/v1/websites/:id/suspend
func (h *WebsiteHandler) SuspendWebsite(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	if err := h.websiteService.SuspendWebsite(c.Request.Context(), tenantID.(uint), websiteID); err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, gin.H{"message": "Website suspended successfully"})
}

// GetWebsiteStats handles GET /api/v1/websites/stats
func (h *WebsiteHandler) GetWebsiteStats(c *gin.Context) {
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	stats, err := h.websiteService.GetWebsiteStats(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, stats)
}

// CloneWebsite handles POST /api/v1/websites/:id/clone
func (h *WebsiteHandler) CloneWebsite(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	var req struct {
		Name string `json:"name" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	website, err := h.websiteService.CloneWebsite(c.Request.Context(), tenantID.(uint), websiteID, req.Name)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Created(c.Writer, website)
}

// Helper methods

// parseWebsiteID parses website ID from URL parameter
func (h *WebsiteHandler) parseWebsiteID(c *gin.Context) (uint, error) {
	idStr := c.Param("id")
	if idStr == "" {
		return 0, errors.New("website ID is required")
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return 0, errors.New("invalid website ID format")
	}

	return uint(id), nil
}

// handleServiceError handles service errors and returns appropriate HTTP responses
func (h *WebsiteHandler) handleServiceError(c *gin.Context, err error) {
	var serviceErr *services.WebsiteServiceError
	if errors.As(err, &serviceErr) {
		switch serviceErr.Code {
		case services.ErrCodeWebsiteNotFound:
			response.NotFound(c.Writer, serviceErr.Message)
		case services.ErrCodeWebsiteAccessDenied:
			response.Forbidden(c.Writer, serviceErr.Message)
		case services.ErrCodeDomainAlreadyExists, services.ErrCodeSubdomainAlreadyExists:
			response.Conflict(c.Writer, serviceErr.Message)
		case services.ErrCodeInvalidDomain, services.ErrCodeInvalidSubdomain, services.ErrCodeReservedSubdomain:
			response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
		case services.ErrCodeValidationFailed:
			response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
		default:
			response.InternalServerError(c.Writer, serviceErr.Message)
		}
		return
	}

	// Handle GORM errors
	if errors.Is(err, gorm.ErrRecordNotFound) {
		response.NotFound(c.Writer, "Website not found")
		return
	}

	// Default to internal server error
	response.InternalServerError(c.Writer, "Internal server error")
}

// Conversion functions
func (h *WebsiteHandler) convertToWebsiteResponse(website *models.WebsiteResponse) *dto.WebsiteResponse {
	return &dto.WebsiteResponse{
		ID:          website.ID,
		TenantID:    website.TenantID,
		Name:        website.Name,
		Domain:      website.Domain,
		Subdomain:   website.Subdomain,
		Description: website.Description,
		ActiveTheme: website.ActiveTheme,
		SiteLogo:    website.SiteLogo,
		Favicon:     website.Favicon,
		Timezone:    website.Timezone,
		Language:    website.Language,
		SocialMedia: website.SocialMedia,
		Status:      website.Status,
		CreatedAt:   website.CreatedAt,
		UpdatedAt:   website.UpdatedAt,
	}
}
