package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// WebsiteTemplateHandler handles website template HTTP requests
type WebsiteTemplateHandler struct {
	templateService services.WebsiteTemplateService
	validator       validator.Validator
	logger          utils.Logger
}

// NewWebsiteTemplateHandler creates a new website template handler
func NewWebsiteTemplateHandler(
	templateService services.WebsiteTemplateService,
	validator validator.Validator,
	logger utils.Logger,
) *WebsiteTemplateHandler {
	return &WebsiteTemplateHandler{
		templateService: templateService,
		validator:       validator,
		logger:          logger,
	}
}

// CreateTemplate godoc
// @Summary      Create a new website template
// @Description  Creates a new website template with the provided information
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        template body dto.CreateTemplateRequest true "Template creation request"
// @Success      201 {object} response.Response{data=dto.TemplateResponse} "Template created successfully"
// @Failure      400 {object} response.Response "Invalid request format or validation error"
// @Failure      409 {object} response.Response "Template slug already exists"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates [post]
func (h *WebsiteTemplateHandler) CreateTemplate(c *gin.Context) {
	var req dto.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Create template
	result, err := h.templateService.CreateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"name": req.Name,
			"slug": req.Slug,
		}).Error("Failed to create template")

		if serviceErr, ok := err.(*services.WebsiteServiceError); ok {
			switch serviceErr.Code {
			case services.ErrCodeTemplateSlugExists:
				response.Conflict(c.Writer, serviceErr.Message)
			case services.ErrCodeCategoryNotFound:
				response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
			case services.ErrCodeValidationFailed:
				response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
			default:
				response.InternalServerError(c.Writer, "Failed to create template")
			}
			return
		}

		response.InternalServerError(c.Writer, "Failed to create template")
		return
	}

	response.Created(c.Writer, result)
}

// GetTemplate godoc
// @Summary      Get a website template
// @Description  Retrieves a website template by ID or slug
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        id path int false "Template ID"
// @Param        slug query string false "Template slug"
// @Success      200 {object} response.Response{data=dto.TemplateResponse} "Template retrieved successfully"
// @Failure      400 {object} response.Response "Invalid request parameters"
// @Failure      404 {object} response.Response "Template not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates/{id} [get]
func (h *WebsiteTemplateHandler) GetTemplate(c *gin.Context) {
	var req dto.GetTemplateRequest

	// Get ID from path parameter if provided
	if idParam := c.Param("id"); idParam != "" {
		id, err := strconv.ParseUint(idParam, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid template ID", "id")
			return
		}
		idUint := uint(id)
		req.ID = &idUint
	}

	// Get slug from query parameter if provided
	if slug := c.Query("slug"); slug != "" {
		req.Slug = slug
	}

	// Ensure either ID or slug is provided
	if req.ID == nil && req.Slug == "" {
		response.BadRequest(c.Writer, "Either ID or slug must be provided")
		return
	}

	// Get template
	result, err := h.templateService.GetTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"id":   req.ID,
			"slug": req.Slug,
		}).Error("Failed to get template")

		if serviceErr, ok := err.(*services.WebsiteServiceError); ok {
			switch serviceErr.Code {
			case services.ErrCodeTemplateNotFound:
				response.NotFound(c.Writer, serviceErr.Message)
			default:
				response.InternalServerError(c.Writer, "Failed to get template")
			}
			return
		}

		response.InternalServerError(c.Writer, "Failed to get template")
		return
	}

	response.Success(c.Writer, result)
}

// UpdateTemplate godoc
// @Summary      Update a website template
// @Description  Updates a website template with the provided information
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        id path int true "Template ID"
// @Param        template body dto.UpdateTemplateRequest true "Template update request"
// @Success      200 {object} response.Response{data=dto.TemplateResponse} "Template updated successfully"
// @Failure      400 {object} response.Response "Invalid request format or validation error"
// @Failure      404 {object} response.Response "Template not found"
// @Failure      409 {object} response.Response "Template slug already exists"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates/{id} [put]
func (h *WebsiteTemplateHandler) UpdateTemplate(c *gin.Context) {
	// Get template ID from path
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid template ID", "id")
		return
	}

	var req dto.UpdateTemplateRequest
	req.ID = uint(id)

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Update template
	result, err := h.templateService.UpdateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"id": req.ID,
		}).Error("Failed to update template")

		if serviceErr, ok := err.(*services.WebsiteServiceError); ok {
			switch serviceErr.Code {
			case services.ErrCodeTemplateNotFound:
				response.NotFound(c.Writer, serviceErr.Message)
			case services.ErrCodeTemplateSlugExists:
				response.Conflict(c.Writer, serviceErr.Message)
			case services.ErrCodeCategoryNotFound:
				response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
			case services.ErrCodeValidationFailed:
				response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
			default:
				response.InternalServerError(c.Writer, "Failed to update template")
			}
			return
		}

		response.InternalServerError(c.Writer, "Failed to update template")
		return
	}

	response.Success(c.Writer, result)
}

// DeleteTemplate godoc
// @Summary      Delete a website template
// @Description  Soft deletes a website template by setting its status to deleted
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        id path int true "Template ID"
// @Success      200 {object} response.Response "Template deleted successfully"
// @Failure      400 {object} response.Response "Invalid template ID"
// @Failure      404 {object} response.Response "Template not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates/{id} [delete]
func (h *WebsiteTemplateHandler) DeleteTemplate(c *gin.Context) {
	// Get template ID from path
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid template ID", "id")
		return
	}

	// Delete template
	if err := h.templateService.DeleteTemplate(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"id": id,
		}).Error("Failed to delete template")

		if serviceErr, ok := err.(*services.WebsiteServiceError); ok {
			switch serviceErr.Code {
			case services.ErrCodeTemplateNotFound:
				response.NotFound(c.Writer, serviceErr.Message)
			default:
				response.InternalServerError(c.Writer, "Failed to delete template")
			}
			return
		}

		response.InternalServerError(c.Writer, "Failed to delete template")
		return
	}

	response.Success(c.Writer, gin.H{"message": "Template deleted successfully"})
}

// ListTemplates godoc
// @Summary      List website templates
// @Description  Retrieves a list of website templates with filtering and pagination
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        category_id query int false "Filter by category ID"
// @Param        template_type query string false "Filter by template type" Enums(business,portfolio,blog,ecommerce,landing,personal,nonprofit,education,event,restaurant)
// @Param        industry query string false "Filter by industry"
// @Param        is_premium query bool false "Filter by premium status"
// @Param        is_featured query bool false "Filter by featured status"
// @Param        status query string false "Filter by status" Enums(draft,review,published,archived)
// @Param        search query string false "Search by name, description, or tags"
// @Param        tags query string false "Filter by tags (comma-separated)"
// @Param        sort_by query string false "Sort by field" Enums(name,created_at,updated_at,download_count,usage_count,rating_average)
// @Param        sort_order query string false "Sort order" Enums(asc,desc)
// @Param        page query int false "Page number (default: 1)"
// @Param        limit query int false "Items per page (default: 10, max: 100)"
// @Success      200 {object} response.Response{data=dto.TemplateListResponse} "Templates retrieved successfully"
// @Failure      400 {object} response.Response "Invalid query parameters"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates [get]
func (h *WebsiteTemplateHandler) ListTemplates(c *gin.Context) {
	var req dto.ListTemplatesRequest

	// Bind query parameters
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid query parameters", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// List templates
	result, err := h.templateService.ListTemplates(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list templates")
		response.InternalServerError(c.Writer, "Failed to list templates")
		return
	}

	// Use cursor-based pagination response format
	if result.Pagination != nil {
		response.CursorPaginated(c.Writer, result.Templates, *result.Pagination)
	} else {
		response.Success(c.Writer, result.Templates)
	}
}

// GetFeaturedTemplates godoc
// @Summary      Get featured website templates
// @Description  Retrieves a list of featured website templates
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        limit query int false "Maximum number of templates to return (default: 10)"
// @Success      200 {object} response.Response{data=[]dto.TemplateResponse} "Featured templates retrieved successfully"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates/featured [get]
func (h *WebsiteTemplateHandler) GetFeaturedTemplates(c *gin.Context) {
	limit := 10
	if limitParam := c.Query("limit"); limitParam != "" {
		if l, err := strconv.Atoi(limitParam); err == nil && l > 0 {
			limit = l
		}
	}

	templates, err := h.templateService.GetFeaturedTemplates(c.Request.Context(), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get featured templates")
		response.InternalServerError(c.Writer, "Failed to get featured templates")
		return
	}

	response.Success(c.Writer, templates)
}

// GetPopularTemplates godoc
// @Summary      Get popular website templates
// @Description  Retrieves a list of popular website templates based on usage count
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        limit query int false "Maximum number of templates to return (default: 10)"
// @Success      200 {object} response.Response{data=[]dto.TemplateResponse} "Popular templates retrieved successfully"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates/popular [get]
func (h *WebsiteTemplateHandler) GetPopularTemplates(c *gin.Context) {
	limit := 10
	if limitParam := c.Query("limit"); limitParam != "" {
		if l, err := strconv.Atoi(limitParam); err == nil && l > 0 {
			limit = l
		}
	}

	templates, err := h.templateService.GetPopularTemplates(c.Request.Context(), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular templates")
		response.InternalServerError(c.Writer, "Failed to get popular templates")
		return
	}

	response.Success(c.Writer, templates)
}

// SearchTemplates godoc
// @Summary      Search website templates
// @Description  Searches website templates by name, description, and tags
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        q query string true "Search query"
// @Param        limit query int false "Maximum number of templates to return (default: 20)"
// @Success      200 {object} response.Response{data=[]dto.TemplateResponse} "Search results retrieved successfully"
// @Failure      400 {object} response.Response "Missing search query"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates/search [get]
func (h *WebsiteTemplateHandler) SearchTemplates(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		response.BadRequest(c.Writer, "Search query is required", "q")
		return
	}

	limit := 20
	if limitParam := c.Query("limit"); limitParam != "" {
		if l, err := strconv.Atoi(limitParam); err == nil && l > 0 {
			limit = l
		}
	}

	templates, err := h.templateService.SearchTemplates(c.Request.Context(), query, limit)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"query": query,
		}).Error("Failed to search templates")
		response.InternalServerError(c.Writer, "Failed to search templates")
		return
	}

	response.Success(c.Writer, templates)
}

// GetTemplateStats godoc
// @Summary      Get website template statistics
// @Description  Retrieves statistics about website templates
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.TemplateStatsResponse} "Template statistics retrieved successfully"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates/stats [get]
func (h *WebsiteTemplateHandler) GetTemplateStats(c *gin.Context) {
	stats, err := h.templateService.GetTemplateStats(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template stats")
		response.InternalServerError(c.Writer, "Failed to get template stats")
		return
	}

	response.Success(c.Writer, stats)
}

// IncrementDownloadCount godoc
// @Summary      Increment template download count
// @Description  Increments the download count for a template
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        id path int true "Template ID"
// @Success      200 {object} response.Response "Download count updated successfully"
// @Failure      400 {object} response.Response "Invalid template ID"
// @Failure      404 {object} response.Response "Template not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates/{id}/download [post]
func (h *WebsiteTemplateHandler) IncrementDownloadCount(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid template ID", "id")
		return
	}

	if err := h.templateService.IncrementDownloadCount(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"id": id,
		}).Error("Failed to increment download count")
		response.InternalServerError(c.Writer, "Failed to increment download count")
		return
	}

	response.Success(c.Writer, gin.H{"message": "Download count updated successfully"})
}

// IncrementUsageCount godoc
// @Summary      Increment template usage count
// @Description  Increments the usage count for a template
// @Tags         website-templates
// @Accept       json
// @Produce      json
// @Param        id path int true "Template ID"
// @Success      200 {object} response.Response "Usage count updated successfully"
// @Failure      400 {object} response.Response "Invalid template ID"
// @Failure      404 {object} response.Response "Template not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/templates/{id}/use [post]
func (h *WebsiteTemplateHandler) IncrementUsageCount(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid template ID", "id")
		return
	}

	if err := h.templateService.IncrementUsageCount(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"id": id,
		}).Error("Failed to increment usage count")
		response.InternalServerError(c.Writer, "Failed to increment usage count")
		return
	}

	response.Success(c.Writer, gin.H{"message": "Usage count updated successfully"})
}
