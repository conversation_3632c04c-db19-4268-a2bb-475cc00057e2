package handlers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type WebsitePageHandler struct {
	pageService services.WebsitePageService
}

// NewWebsitePageHandler creates a new website page handler
func NewWebsitePageHandler(pageService services.WebsitePageService) *WebsitePageHandler {
	return &WebsitePageHandler{
		pageService: pageService,
	}
}

// CreatePage creates a new website page
// @Summary Create website page
// @Description Create a new website page
// @Tags Website Pages
// @Accept json
// @Produce json
// @Param request body models.WebsitePageCreateRequest true "Page creation request"
// @Success 201 {object} response.Response{data=models.WebsitePage}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages [post]
func (h *WebsitePageHandler) CreatePage(c *gin.Context) {
	var req models.WebsitePageCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request format")
		return
	}

	// Get user ID from context (would be set by auth middleware)
	userID := getUserID(c)
	tenantID := getTenantID(c)

	// Create page model
	page := &models.WebsitePage{
		WebsiteID:         req.WebsiteID,
		TenantID:          tenantID,
		TemplateID:        req.TemplateID,
		Title:             req.Title,
		Slug:              req.Slug,
		Description:       req.Description,
		CanonicalURL:      req.CanonicalURL,
		PageType:          req.PageType,
		LayoutType:        req.LayoutType,
		IsHomepage:        req.IsHomepage,
		RequiresAuth:      req.RequiresAuth,
		PasswordProtected: req.PasswordProtected,
		PagePassword:      req.PagePassword,
		Visibility:        req.Visibility,
		FeaturedImage:     req.FeaturedImage,
		CustomFields:      req.CustomFields,
		PageSettings:      req.PageSettings,
		CreatedBy:         userID,
	}

	if err := h.pageService.Create(c.Request.Context(), page); err != nil {
		response.InternalServerError(c.Writer, "Failed to create page")
		return
	}

	response.Success(c.Writer, page)
}

// GetPage retrieves a website page by ID
// @Summary Get website page
// @Description Get a website page by ID
// @Tags Website Pages
// @Produce json
// @Param id path int true "Page ID"
// @Success 200 {object} response.Response{data=models.WebsitePage}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/{id} [get]
func (h *WebsitePageHandler) GetPage(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid page ID")
		return
	}

	userID := getUserID(c)

	page, err := h.pageService.GetByID(c.Request.Context(), uint(id), userID)
	if err != nil {
		response.NotFound(c.Writer, "Page not found")
		return
	}

	response.Success(c.Writer, page)
}

// GetPageBySlug retrieves a website page by slug
// @Summary Get website page by slug
// @Description Get a website page by website ID and slug
// @Tags Website Pages
// @Produce json
// @Param website_id query int true "Website ID"
// @Param slug path string true "Page slug"
// @Success 200 {object} response.Response{data=models.WebsitePage}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/slug/{slug} [get]
func (h *WebsitePageHandler) GetPageBySlug(c *gin.Context) {
	slug := c.Param("slug")
	websiteIDStr := c.Query("website_id")

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	page, err := h.pageService.GetBySlug(c.Request.Context(), uint(websiteID), slug)
	if err != nil {
		response.NotFound(c.Writer, "Page not found")
		return
	}

	response.Success(c.Writer, page)
}

// UpdatePage updates a website page
// @Summary Update website page
// @Description Update a website page
// @Tags Website Pages
// @Accept json
// @Produce json
// @Param id path int true "Page ID"
// @Param request body models.WebsitePageUpdateRequest true "Page update request"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/{id} [put]
func (h *WebsitePageHandler) UpdatePage(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid page ID")
		return
	}

	var req models.WebsitePageUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request format")
		return
	}

	userID := getUserID(c)

	// Convert request to updates map
	updates := make(map[string]interface{})
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Slug != nil {
		updates["slug"] = *req.Slug
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	// Meta fields removed - now handled by centralized SEO module
	if req.CanonicalURL != nil {
		updates["canonical_url"] = *req.CanonicalURL
	}
	if req.PageType != nil {
		updates["page_type"] = *req.PageType
	}
	if req.LayoutType != nil {
		updates["layout_type"] = *req.LayoutType
	}
	if req.IsHomepage != nil {
		updates["is_homepage"] = *req.IsHomepage
	}
	if req.RequiresAuth != nil {
		updates["requires_auth"] = *req.RequiresAuth
	}
	if req.PasswordProtected != nil {
		updates["password_protected"] = *req.PasswordProtected
	}
	if req.PagePassword != nil {
		updates["page_password"] = *req.PagePassword
	}
	if req.Visibility != nil {
		updates["visibility"] = *req.Visibility
	}
	if req.FeaturedImage != nil {
		updates["featured_image"] = *req.FeaturedImage
	}
	if req.CustomFields != nil {
		updates["custom_fields"] = *req.CustomFields
	}
	if req.PageSettings != nil {
		updates["page_settings"] = *req.PageSettings
	}

	if err := h.pageService.Update(c.Request.Context(), uint(id), updates, userID); err != nil {
		response.InternalServerError(c.Writer, "Failed to update page")
		return
	}

	response.Success(c.Writer, nil)
}

// DeletePage deletes a website page
// @Summary Delete website page
// @Description Delete a website page
// @Tags Website Pages
// @Produce json
// @Param id path int true "Page ID"
// @Success 200 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/{id} [delete]
func (h *WebsitePageHandler) DeletePage(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid page ID")
		return
	}

	userID := getUserID(c)

	if err := h.pageService.Delete(c.Request.Context(), uint(id), userID); err != nil {
		response.InternalServerError(c.Writer, "Failed to delete page")
		return
	}

	response.Success(c.Writer, nil)
}

// ListPages lists website pages with filtering
// @Summary List website pages
// @Description List website pages with filtering and pagination
// @Tags Website Pages
// @Produce json
// @Param website_id query int false "Website ID"
// @Param status query string false "Page status"
// @Param visibility query string false "Page visibility"
// @Param page_type query string false "Page type"
// @Param search query string false "Search term"
// @Param limit query int false "Limit per page" default(20)
// @Param cursor query string false "Pagination cursor"
// @Success 200 {object} response.Response{data=[]models.WebsitePage,meta=pagination.CursorResponse}
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages [get]
func (h *WebsitePageHandler) ListPages(c *gin.Context) {
	userID := getUserID(c)
	tenantID := getTenantID(c)

	// Parse cursor-based pagination
	if cursor := c.Query("cursor"); cursor != "" {
		h.listPagesWithCursor(c, userID, tenantID)
		return
	}

	// Parse filters
	filter := models.WebsitePageFilter{
		TenantID: &tenantID,
	}

	if websiteIDStr := c.Query("website_id"); websiteIDStr != "" {
		if websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32); err == nil {
			websiteIDUint := uint(websiteID)
			filter.WebsiteID = &websiteIDUint
		}
	}

	if status := c.Query("status"); status != "" {
		pageStatus := models.PageStatus(status)
		filter.Status = &pageStatus
	}

	if visibility := c.Query("visibility"); visibility != "" {
		pageVisibility := models.PageVisibility(visibility)
		filter.Visibility = &pageVisibility
	}

	if pageType := c.Query("page_type"); pageType != "" {
		pageTypeEnum := models.PageType(pageType)
		filter.PageType = &pageTypeEnum
	}

	if search := c.Query("search"); search != "" {
		filter.SearchTerm = search
	}

	pages, total, err := h.pageService.List(c.Request.Context(), filter, userID)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list pages")
		return
	}

	meta := map[string]interface{}{
		"total": total,
	}

	response.Success(c.Writer, map[string]interface{}{"data": pages, "meta": meta})
}

// listPagesWithCursor handles cursor-based pagination
func (h *WebsitePageHandler) listPagesWithCursor(c *gin.Context, userID, tenantID uint) {
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		response.BadRequest(c.Writer, "Website ID is required for cursor pagination")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	// Parse pagination parameters
	req := &pagination.CursorRequest{
		Limit:  20, // default
		Cursor: c.Query("cursor"),
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.ParseUint(limitStr, 10, 32); err == nil && limit <= 100 {
			req.Limit = int(limit)
		}
	}

	// Parse additional filters
	filters := make(map[string]interface{})
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if visibility := c.Query("visibility"); visibility != "" {
		filters["visibility"] = visibility
	}

	pages, cursorResp, err := h.pageService.ListWithCursor(c.Request.Context(), uint(websiteID), req, filters, userID)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list pages")
		return
	}

	response.Success(c.Writer, map[string]interface{}{"data": pages, "meta": cursorResp})
}

// PublishPage publishes a website page
// @Summary Publish website page
// @Description Publish a website page
// @Tags Website Pages
// @Produce json
// @Param id path int true "Page ID"
// @Success 200 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/{id}/publish [post]
func (h *WebsitePageHandler) PublishPage(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid page ID")
		return
	}

	userID := getUserID(c)

	if err := h.pageService.PublishPage(c.Request.Context(), uint(id), userID); err != nil {
		response.InternalServerError(c.Writer, "Failed to publish page")
		return
	}

	response.Success(c.Writer, nil)
}

// UnpublishPage unpublishes a website page
// @Summary Unpublish website page
// @Description Unpublish a website page
// @Tags Website Pages
// @Produce json
// @Param id path int true "Page ID"
// @Success 200 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/{id}/unpublish [post]
func (h *WebsitePageHandler) UnpublishPage(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid page ID")
		return
	}

	userID := getUserID(c)

	if err := h.pageService.UnpublishPage(c.Request.Context(), uint(id), userID); err != nil {
		response.InternalServerError(c.Writer, "Failed to unpublish page")
		return
	}

	response.Success(c.Writer, nil)
}

// SchedulePage schedules a website page for publishing
// @Summary Schedule website page
// @Description Schedule a website page for publishing
// @Tags Website Pages
// @Accept json
// @Produce json
// @Param id path int true "Page ID"
// @Param request body models.WebsitePageScheduleRequest true "Schedule request"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/{id}/schedule [post]
func (h *WebsitePageHandler) SchedulePage(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid page ID")
		return
	}

	var req models.WebsitePageScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request format")
		return
	}

	userID := getUserID(c)

	var scheduledAt *time.Time
	if req.ScheduledAt != "" {
		parsed, err := time.Parse(time.RFC3339, req.ScheduledAt)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid scheduled_at format")
			return
		}
		scheduledAt = &parsed
	}

	if err := h.pageService.SchedulePage(c.Request.Context(), uint(id), scheduledAt, userID); err != nil {
		response.InternalServerError(c.Writer, "Failed to schedule page")
		return
	}

	response.Success(c.Writer, nil)
}

// SetAsHomepage sets a page as homepage
// @Summary Set page as homepage
// @Description Set a page as homepage for a website
// @Tags Website Pages
// @Produce json
// @Param id path int true "Page ID"
// @Param website_id query int true "Website ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/{id}/set-homepage [post]
func (h *WebsitePageHandler) SetAsHomepage(c *gin.Context) {
	pageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid page ID")
		return
	}

	websiteIDStr := c.Query("website_id")
	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	userID := getUserID(c)

	if err := h.pageService.SetAsHomepage(c.Request.Context(), uint(websiteID), uint(pageID), userID); err != nil {
		response.InternalServerError(c.Writer, "Failed to set as homepage")
		return
	}

	response.Success(c.Writer, nil)
}

// DuplicatePage duplicates a website page
// @Summary Duplicate website page
// @Description Create a copy of a website page
// @Tags Website Pages
// @Accept json
// @Produce json
// @Param id path int true "Page ID"
// @Param request body models.WebsitePageDuplicateRequest true "Duplicate request"
// @Success 201 {object} response.Response{data=models.WebsitePage}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/{id}/duplicate [post]
func (h *WebsitePageHandler) DuplicatePage(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid page ID")
		return
	}

	var req models.WebsitePageDuplicateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request format")
		return
	}

	userID := getUserID(c)

	page, err := h.pageService.DuplicatePage(c.Request.Context(), uint(id), req.NewSlug, req.NewTitle, userID)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to duplicate page")
		return
	}

	response.Success(c.Writer, page)
}

// GetPageAnalytics retrieves analytics for a page
// @Summary Get page analytics
// @Description Get analytics data for a website page
// @Tags Website Pages
// @Produce json
// @Param id path int true "Page ID"
// @Success 200 {object} response.Response{data=object}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/website/v1/pages/{id}/analytics [get]
func (h *WebsitePageHandler) GetPageAnalytics(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid page ID")
		return
	}

	userID := getUserID(c)

	analytics, err := h.pageService.GetPageAnalytics(c.Request.Context(), uint(id), userID)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get page analytics")
		return
	}

	response.Success(c.Writer, analytics)
}

// Helper functions
func getUserID(c *gin.Context) uint {
	// This would typically be set by auth middleware
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint); ok {
			return id
		}
	}
	return 1 // Default for development
}

func getTenantID(c *gin.Context) uint {
	// This would typically be set by auth middleware
	if tenantID, exists := c.Get("tenant_id"); exists {
		if id, ok := tenantID.(uint); ok {
			return id
		}
	}
	return 1 // Default for development
}
