package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// TemplateCategoryHandler handles template category HTTP requests
type TemplateCategoryHandler struct {
	categoryService services.TemplateCategoryService
	validator       validator.Validator
	logger          utils.Logger
}

// NewTemplateCategoryHandler creates a new template category handler
func NewTemplateCategoryHandler(
	categoryService services.TemplateCategoryService,
	validator validator.Validator,
	logger utils.Logger,
) *TemplateCategoryHandler {
	return &TemplateCategoryHandler{
		categoryService: categoryService,
		validator:       validator,
		logger:          logger,
	}
}

// CreateCategory godoc
// @Summary      Create a new template category
// @Description  Creates a new template category with the provided information
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Param        category body dto.CreateTemplateCategoryRequest true "Category creation request"
// @Success      201 {object} response.Response{data=dto.TemplateCategoryResponse} "Category created successfully"
// @Failure      400 {object} response.Response "Invalid request format or validation error"
// @Failure      409 {object} response.Response "Category slug already exists"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories [post]
func (h *TemplateCategoryHandler) CreateCategory(c *gin.Context) {
	var req dto.CreateTemplateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Create category
	result, err := h.categoryService.CreateCategory(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"name": req.Name,
			"slug": req.Slug,
		}).Error("Failed to create template category")

		if serviceErr, ok := err.(*services.WebsiteServiceError); ok {
			switch serviceErr.Code {
			case services.ErrCodeCategorySlugExists:
				response.Conflict(c.Writer, serviceErr.Message)
			case services.ErrCodeCategoryNotFound:
				response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
			case services.ErrCodeValidationFailed:
				response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
			default:
				response.InternalServerError(c.Writer, "Failed to create template category")
			}
			return
		}

		response.InternalServerError(c.Writer, "Failed to create template category")
		return
	}

	response.Created(c.Writer, result)
}

// GetCategory godoc
// @Summary      Get a template category
// @Description  Retrieves a template category by ID or slug
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Param        id path int false "Category ID"
// @Param        slug query string false "Category slug"
// @Param        include_parent query bool false "Include parent category"
// @Param        include_children query bool false "Include child categories"
// @Param        include_templates query bool false "Include category templates"
// @Success      200 {object} response.Response{data=dto.TemplateCategoryResponse} "Category retrieved successfully"
// @Failure      400 {object} response.Response "Invalid request parameters"
// @Failure      404 {object} response.Response "Category not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories/{id} [get]
func (h *TemplateCategoryHandler) GetCategory(c *gin.Context) {
	var req dto.GetTemplateCategoryRequest

	// Get ID from path parameter if provided
	if idParam := c.Param("id"); idParam != "" {
		id, err := strconv.ParseUint(idParam, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid category ID", "id")
			return
		}
		idUint := uint(id)
		req.ID = &idUint
	}

	// Get slug from query parameter if provided
	if slug := c.Query("slug"); slug != "" {
		req.Slug = slug
	}

	// Get include flags
	if includeParent := c.Query("include_parent"); includeParent == "true" {
		req.IncludeParent = true
	}
	if includeChildren := c.Query("include_children"); includeChildren == "true" {
		req.IncludeChildren = true
	}
	if includeTemplates := c.Query("include_templates"); includeTemplates == "true" {
		req.IncludeTemplates = true
	}

	// Ensure either ID or slug is provided
	if req.ID == nil && req.Slug == "" {
		response.BadRequest(c.Writer, "Either ID or slug must be provided")
		return
	}

	// Get category
	result, err := h.categoryService.GetCategory(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"id":   req.ID,
			"slug": req.Slug,
		}).Error("Failed to get template category")

		if serviceErr, ok := err.(*services.WebsiteServiceError); ok {
			switch serviceErr.Code {
			case services.ErrCodeCategoryNotFound:
				response.NotFound(c.Writer, serviceErr.Message)
			default:
				response.InternalServerError(c.Writer, "Failed to get template category")
			}
			return
		}

		response.InternalServerError(c.Writer, "Failed to get template category")
		return
	}

	response.Success(c.Writer, result)
}

// UpdateCategory godoc
// @Summary      Update a template category
// @Description  Updates a template category with the provided information
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Param        id path int true "Category ID"
// @Param        category body dto.UpdateTemplateCategoryRequest true "Category update request"
// @Success      200 {object} response.Response{data=dto.TemplateCategoryResponse} "Category updated successfully"
// @Failure      400 {object} response.Response "Invalid request format or validation error"
// @Failure      404 {object} response.Response "Category not found"
// @Failure      409 {object} response.Response "Category slug already exists"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories/{id} [put]
func (h *TemplateCategoryHandler) UpdateCategory(c *gin.Context) {
	// Get category ID from path
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid category ID", "id")
		return
	}

	var req dto.UpdateTemplateCategoryRequest
	req.ID = uint(id)

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Update category
	result, err := h.categoryService.UpdateCategory(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"id": req.ID,
		}).Error("Failed to update template category")

		if serviceErr, ok := err.(*services.WebsiteServiceError); ok {
			switch serviceErr.Code {
			case services.ErrCodeCategoryNotFound:
				response.NotFound(c.Writer, serviceErr.Message)
			case services.ErrCodeCategorySlugExists:
				response.Conflict(c.Writer, serviceErr.Message)
			case services.ErrCodeValidationFailed:
				response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
			default:
				response.InternalServerError(c.Writer, "Failed to update template category")
			}
			return
		}

		response.InternalServerError(c.Writer, "Failed to update template category")
		return
	}

	response.Success(c.Writer, result)
}

// DeleteCategory godoc
// @Summary      Delete a template category
// @Description  Soft deletes a template category by setting its status to deleted
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Param        id path int true "Category ID"
// @Success      200 {object} response.Response "Category deleted successfully"
// @Failure      400 {object} response.Response "Invalid category ID or cannot delete category with templates/subcategories"
// @Failure      404 {object} response.Response "Category not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories/{id} [delete]
func (h *TemplateCategoryHandler) DeleteCategory(c *gin.Context) {
	// Get category ID from path
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid category ID", "id")
		return
	}

	// Delete category
	if err := h.categoryService.DeleteCategory(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"id": id,
		}).Error("Failed to delete template category")

		if serviceErr, ok := err.(*services.WebsiteServiceError); ok {
			switch serviceErr.Code {
			case services.ErrCodeCategoryNotFound:
				response.NotFound(c.Writer, serviceErr.Message)
			case services.ErrCodeValidationFailed:
				response.BadRequest(c.Writer, serviceErr.Message)
			default:
				response.InternalServerError(c.Writer, "Failed to delete template category")
			}
			return
		}

		response.InternalServerError(c.Writer, "Failed to delete template category")
		return
	}

	response.Success(c.Writer, gin.H{"message": "Category deleted successfully"})
}

// ListCategories godoc
// @Summary      List template categories
// @Description  Retrieves a list of template categories with filtering and pagination
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Param        parent_id query int false "Filter by parent category ID"
// @Param        level query int false "Filter by category level"
// @Param        is_active query bool false "Filter by active status"
// @Param        is_featured query bool false "Filter by featured status"
// @Param        status query string false "Filter by status" Enums(active,inactive,deleted)
// @Param        search query string false "Search by name or description"
// @Param        sort_by query string false "Sort by field" Enums(name,created_at,updated_at,sort_order,template_count)
// @Param        sort_order query string false "Sort order" Enums(asc,desc)
// @Param        page query int false "Page number (default: 1)"
// @Param        limit query int false "Items per page (default: 50, max: 100)"
// @Success      200 {object} response.Response{data=dto.TemplateCategoryListResponse} "Categories retrieved successfully"
// @Failure      400 {object} response.Response "Invalid query parameters"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories [get]
func (h *TemplateCategoryHandler) ListCategories(c *gin.Context) {
	var req dto.ListTemplateCategoriesRequest

	// Bind query parameters
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid query parameters", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// List categories
	result, err := h.categoryService.ListCategories(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list template categories")
		response.InternalServerError(c.Writer, "Failed to list template categories")
		return
	}

	response.Success(c.Writer, result)
}

// GetCategoryTree godoc
// @Summary      Get template category tree
// @Description  Retrieves the complete template category tree structure
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.TemplateCategoryTreeResponse} "Category tree retrieved successfully"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories/tree [get]
func (h *TemplateCategoryHandler) GetCategoryTree(c *gin.Context) {
	tree, err := h.categoryService.GetCategoryTree(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get category tree")
		response.InternalServerError(c.Writer, "Failed to get category tree")
		return
	}

	response.Success(c.Writer, tree)
}

// GetMainCategories godoc
// @Summary      Get main template categories
// @Description  Retrieves main template categories (level 0)
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=[]dto.TemplateCategoryResponse} "Main categories retrieved successfully"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories/main [get]
func (h *TemplateCategoryHandler) GetMainCategories(c *gin.Context) {
	categories, err := h.categoryService.GetMainCategories(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get main categories")
		response.InternalServerError(c.Writer, "Failed to get main categories")
		return
	}

	response.Success(c.Writer, categories)
}

// GetSubcategories godoc
// @Summary      Get subcategories
// @Description  Retrieves subcategories for a parent category
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Param        id path int true "Parent category ID"
// @Success      200 {object} response.Response{data=[]dto.TemplateCategoryResponse} "Subcategories retrieved successfully"
// @Failure      400 {object} response.Response "Invalid parent category ID"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories/{id}/subcategories [get]
func (h *TemplateCategoryHandler) GetSubcategories(c *gin.Context) {
	parentID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid parent category ID", "id")
		return
	}

	categories, err := h.categoryService.GetSubcategories(c.Request.Context(), uint(parentID))
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"parent_id": parentID,
		}).Error("Failed to get subcategories")
		response.InternalServerError(c.Writer, "Failed to get subcategories")
		return
	}

	response.Success(c.Writer, categories)
}

// GetFeaturedCategories godoc
// @Summary      Get featured template categories
// @Description  Retrieves featured template categories
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=[]dto.TemplateCategoryResponse} "Featured categories retrieved successfully"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories/featured [get]
func (h *TemplateCategoryHandler) GetFeaturedCategories(c *gin.Context) {
	categories, err := h.categoryService.GetFeaturedCategories(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get featured categories")
		response.InternalServerError(c.Writer, "Failed to get featured categories")
		return
	}

	response.Success(c.Writer, categories)
}

// GetCategoryStats godoc
// @Summary      Get template category statistics
// @Description  Retrieves statistics about template categories
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.TemplateCategoryStatsResponse} "Category statistics retrieved successfully"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories/stats [get]
func (h *TemplateCategoryHandler) GetCategoryStats(c *gin.Context) {
	stats, err := h.categoryService.GetCategoryStats(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get category stats")
		response.InternalServerError(c.Writer, "Failed to get category stats")
		return
	}

	response.Success(c.Writer, stats)
}

// MoveCategoryToParent godoc
// @Summary      Move category to new parent
// @Description  Moves a category to a new parent category
// @Tags         template-categories
// @Accept       json
// @Produce      json
// @Param        id path int true "Category ID"
// @Param        parent_id body int false "New parent category ID (null for root level)"
// @Success      200 {object} response.Response "Category moved successfully"
// @Failure      400 {object} response.Response "Invalid category ID or parent ID"
// @Failure      404 {object} response.Response "Category not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/websites/template-categories/{id}/move [post]
func (h *TemplateCategoryHandler) MoveCategoryToParent(c *gin.Context) {
	// Get category ID from path
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid category ID", "id")
		return
	}

	var req struct {
		ParentID *uint `json:"parent_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Move category
	if err := h.categoryService.MoveCategoryToParent(c.Request.Context(), uint(id), req.ParentID); err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"id":        id,
			"parent_id": req.ParentID,
		}).Error("Failed to move category")

		if serviceErr, ok := err.(*services.WebsiteServiceError); ok {
			switch serviceErr.Code {
			case services.ErrCodeCategoryNotFound:
				response.NotFound(c.Writer, serviceErr.Message)
			case services.ErrCodeValidationFailed:
				response.BadRequest(c.Writer, serviceErr.Message)
			default:
				response.InternalServerError(c.Writer, "Failed to move category")
			}
			return
		}

		response.InternalServerError(c.Writer, "Failed to move category")
		return
	}

	response.Success(c.Writer, gin.H{"message": "Category moved successfully"})
}
