package website

import (
	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// RegisterWebsiteRoutes registers all website routes
func RegisterWebsiteRoutes(
	router *gin.RouterGroup,
	websiteHandler *handlers.WebsiteHandler,
	domainHandler *handlers.DomainHandler,
	templateHandler *handlers.WebsiteTemplateHandler,
	categoryHandler *handlers.TemplateCategoryHandler,
	db *gorm.DB,
	logger utils.Logger,
) {
	// Get JWT service for authentication
	_, jwtService, _, err := auth.GetAuthServiceDependencies(db, logger)
	if err != nil {
		logger.WithError(err).Error("Failed to get auth dependencies for website routes")
		panic("Failed to initialize JWT service for website routes")
	}
	// Website management routes (require authentication and tenant context)
	websites := router.Group("/websites")
	// Only apply JWT middleware if websiteHandler is not nil (actual website management)
	if websiteHandler != nil {
		websites.Use(middleware.JWTAuthMiddleware(*jwtService))
		websites.Use(middleware.RequireAuthentication())
		websites.Use(middleware.TenantContextMiddleware())
		{
			// Website CRUD operations
			websites.POST("", websiteHandler.CreateWebsite)
			websites.GET("/stats", websiteHandler.GetWebsiteStats)

			websites.GET("/:id", websiteHandler.GetWebsite)
			websites.GET("/:id/details", websiteHandler.GetWebsiteWithDetails)
			websites.PUT("/:id", websiteHandler.UpdateWebsite)
			websites.DELETE("/:id", websiteHandler.DeleteWebsite)

			// Personal endpoints - no RBAC needed (authentication only)
			websites.GET("/me/list", websiteHandler.ListWebsites)

			// Website status management
			websites.POST("/:id/activate", websiteHandler.ActivateWebsite)
			websites.POST("/:id/deactivate", websiteHandler.DeactivateWebsite)
			websites.POST("/:id/suspend", websiteHandler.SuspendWebsite)

			// Website operations
			websites.POST("/:id/clone", websiteHandler.CloneWebsite)

			// Domain verification for websites
			websites.POST("/:id/verify-domain", domainHandler.VerifyDomainOwnership)
			websites.GET("/:id/verify-domain/:domain", domainHandler.CheckDomainVerification)
			websites.GET("/:id/domain-verification-instructions/:domain", domainHandler.GetDomainVerificationInstructions)
		}
	}

	// Domain management routes (public utilities)
	domains := router.Group("/domains")
	{
		// Domain validation and availability
		domains.POST("/validate", domainHandler.ValidateDomain)
		domains.GET("/check-availability", domainHandler.CheckDomainAvailability)
		domains.GET("/info", domainHandler.GetDomainInfo)
		domains.GET("/reserved-subdomains", domainHandler.GetReservedSubdomains)
	}

	// Website template routes
	templates := router.Group("/websites/templates")

	// Public template routes (read-only)
	publicTemplates := templates.Group("")
	{
		// Read-only operations (no auth required)
		publicTemplates.GET("/:id", templateHandler.GetTemplate)
		publicTemplates.GET("", templateHandler.ListTemplates)
		publicTemplates.GET("/featured", templateHandler.GetFeaturedTemplates)
		publicTemplates.GET("/popular", templateHandler.GetPopularTemplates)
		publicTemplates.GET("/search", templateHandler.SearchTemplates)
		publicTemplates.GET("/stats", templateHandler.GetTemplateStats)
		publicTemplates.POST("/:id/download", templateHandler.IncrementDownloadCount)
		publicTemplates.POST("/:id/use", templateHandler.IncrementUsageCount)
	}

	// Protected template routes (require authentication)
	protectedTemplates := templates.Group("")
	protectedTemplates.Use(middleware.JWTAuthMiddleware(*jwtService))
	protectedTemplates.Use(middleware.RequireAuthentication())
	protectedTemplates.Use(middleware.TenantContextMiddleware())
	{
		// CRUD operations (require auth)
		protectedTemplates.POST("", templateHandler.CreateTemplate)
		protectedTemplates.PUT("/:id", templateHandler.UpdateTemplate)
		protectedTemplates.DELETE("/:id", templateHandler.DeleteTemplate)
	}

	// Template category routes
	categories := router.Group("/websites/template-categories")

	// Public category routes (read-only)
	publicCategories := categories.Group("")
	{
		// Read-only operations (no auth required)
		publicCategories.GET("", categoryHandler.ListCategories)
		publicCategories.GET("/tree", categoryHandler.GetCategoryTree)
		publicCategories.GET("/main", categoryHandler.GetMainCategories)
		publicCategories.GET("/featured", categoryHandler.GetFeaturedCategories)
		publicCategories.GET("/stats", categoryHandler.GetCategoryStats)
		publicCategories.GET("/:id", categoryHandler.GetCategory)
		publicCategories.GET("/:id/subcategories", categoryHandler.GetSubcategories)
	}

	// Protected category routes (require authentication)
	protectedCategories := categories.Group("")
	protectedCategories.Use(middleware.JWTAuthMiddleware(*jwtService))
	protectedCategories.Use(middleware.RequireAuthentication())
	protectedCategories.Use(middleware.TenantContextMiddleware())
	{
		// CRUD operations (require auth)
		protectedCategories.POST("", categoryHandler.CreateCategory)
		protectedCategories.PUT("/:id", categoryHandler.UpdateCategory)
		protectedCategories.DELETE("/:id", categoryHandler.DeleteCategory)
		protectedCategories.POST("/:id/move", categoryHandler.MoveCategoryToParent)
	}

}

// RegisterWebsiteThemeRoutes registers website theme management routes
func RegisterWebsiteThemeRoutes(router *gin.RouterGroup, db *gorm.DB, logger utils.Logger) {
	// Get JWT service for authentication
	_, jwtService, _, err := auth.GetAuthServiceDependencies(db, logger)
	if err != nil {
		logger.WithError(err).Error("Failed to get auth dependencies for website theme routes")
		panic("Failed to initialize JWT service for website theme routes")
	}

	// Initialize repositories
	pageRepo := mysql.NewWebsitePageRepository(db)
	blockRepo := mysql.NewWebsitePageBlockRepository(db)
	definitionRepo := mysql.NewWebsiteBlockDefinitionRepository(db)
	websiteRepo := mysql.NewWebsiteRepository(db)

	// Initialize services
	pageService := services.NewWebsitePageService(pageRepo, websiteRepo)
	blockService := services.NewWebsitePageBlockService(blockRepo, pageRepo, definitionRepo)
	definitionService := services.NewWebsiteBlockDefinitionService(definitionRepo)

	// Initialize handlers
	pageHandler := handlers.NewWebsitePageHandler(pageService)
	blockHandler := handlers.NewWebsitePageBlockHandler(blockService)
	definitionHandler := handlers.NewWebsiteBlockDefinitionHandler(definitionService)

	// Website theme routes (require authentication and tenant context)
	theme := router.Group("/theme")
	theme.Use(middleware.JWTAuthMiddleware(*jwtService))
	theme.Use(middleware.RequireAuthentication())
	theme.Use(middleware.TenantContextMiddleware())
	{
		// Website pages routes
		pagesGroup := theme.Group("/pages")
		{
			pagesGroup.POST("", pageHandler.CreatePage)
			pagesGroup.GET("", pageHandler.ListPages)
			pagesGroup.GET("/:id", pageHandler.GetPage)
			pagesGroup.PUT("/:id", pageHandler.UpdatePage)
			pagesGroup.DELETE("/:id", pageHandler.DeletePage)

			// Page-specific actions
			pagesGroup.POST("/:id/publish", pageHandler.PublishPage)
			pagesGroup.POST("/:id/unpublish", pageHandler.UnpublishPage)
			pagesGroup.POST("/:id/schedule", pageHandler.SchedulePage)
			pagesGroup.POST("/:id/set-homepage", pageHandler.SetAsHomepage)
			pagesGroup.POST("/:id/duplicate", pageHandler.DuplicatePage)
			pagesGroup.GET("/:id/analytics", pageHandler.GetPageAnalytics)

			// Page by slug
			pagesGroup.GET("/slug/:slug", pageHandler.GetPageBySlug)

			// Page blocks routes
			pagesGroup.POST("/:page_id/blocks", blockHandler.CreateBlock)
			pagesGroup.GET("/:page_id/blocks", blockHandler.ListPageBlocks)
			pagesGroup.PUT("/:page_id/blocks/reorder", blockHandler.ReorderBlocks)
			pagesGroup.GET("/:page_id/blocks/hierarchy", blockHandler.GetBlockHierarchy)
		}

		// Website blocks routes
		blocksGroup := theme.Group("/blocks")
		{
			blocksGroup.GET("/:id", blockHandler.GetBlock)
			blocksGroup.PUT("/:id", blockHandler.UpdateBlock)
			blocksGroup.DELETE("/:id", blockHandler.DeleteBlock)

			// Block-specific actions
			blocksGroup.PUT("/:id/move", blockHandler.MoveBlock)
			blocksGroup.POST("/:id/duplicate", blockHandler.DuplicateBlock)
			blocksGroup.POST("/:id/convert", blockHandler.ConvertBlockType)
			blocksGroup.PUT("/:id/visibility", blockHandler.UpdateBlockVisibility)
			blocksGroup.POST("/:id/lock", blockHandler.LockBlock)
			blocksGroup.POST("/:id/unlock", blockHandler.UnlockBlock)
			blocksGroup.GET("/:id/render", blockHandler.RenderBlock)

			// Block analytics
			blocksGroup.POST("/:id/impression", blockHandler.RecordImpression)
			blocksGroup.POST("/:id/click", blockHandler.RecordClick)
			blocksGroup.GET("/:id/analytics", blockHandler.GetBlockAnalytics)
		}

		// Block definitions routes
		definitionsGroup := theme.Group("/block-definitions")
		{
			definitionsGroup.POST("", definitionHandler.CreateDefinition)
			definitionsGroup.GET("", definitionHandler.ListDefinitions)
			definitionsGroup.GET("/:id", definitionHandler.GetDefinition)
			definitionsGroup.PUT("/:id", definitionHandler.UpdateDefinition)
			definitionsGroup.DELETE("/:id", definitionHandler.DeleteDefinition)

			// Definition-specific actions
			definitionsGroup.POST("/:id/clone", definitionHandler.CloneDefinition)
			definitionsGroup.GET("/:id/export", definitionHandler.ExportDefinition)

			// Definition lookups
			definitionsGroup.GET("/type/:type", definitionHandler.GetDefinitionByType)
			definitionsGroup.GET("/category/:category", definitionHandler.ListDefinitionsByCategory)
			definitionsGroup.GET("/available", definitionHandler.GetAvailableDefinitions)
			definitionsGroup.GET("/search", definitionHandler.SearchDefinitions)
			definitionsGroup.GET("/most-used", definitionHandler.GetMostUsedDefinitions)
			definitionsGroup.GET("/recently-used", definitionHandler.GetRecentlyUsedDefinitions)
			definitionsGroup.GET("/categories", definitionHandler.GetCategories)
			definitionsGroup.GET("/usage-stats", definitionHandler.GetUsageStats)

			// Bulk operations
			definitionsGroup.POST("/import", definitionHandler.ImportDefinition)
			definitionsGroup.POST("/bulk-import", definitionHandler.BulkImportDefinitions)
			definitionsGroup.POST("/compatible", definitionHandler.GetCompatibleDefinitions)
		}
	}
}
