package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type SettingsHandler struct {
	settingsService services.SettingsService
	logger          utils.Logger
}

func NewSettingsHandler(settingsService services.SettingsService, logger utils.Logger) *SettingsHandler {
	return &SettingsHandler{
		settingsService: settingsService,
		logger:          logger,
	}
}

// GetSettings godoc
// @Summary Get settings
// @Description Get settings by scope and filters
// @Tags Settings
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param scope_type query string true "Scope type" Enums(global,tenant,website,user)
// @Param scope_id query int false "Scope ID"
// @Param category query string false "Category filter"
// @Param key query string false "Key filter"
// @Param is_public query bool false "Public settings only"
// @Success 200 {object} response.Response{data=[]models.SettingResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/settings [get]
func (h *SettingsHandler) GetSettings(c *gin.Context) {
	tenantID := middleware.GetTenantID(c)

	filter := models.SettingFilter{
		ScopeType: models.ScopeType(c.Query("scope_type")),
		Category:  c.Query("category"),
		Key:       c.Query("key"),
	}

	if scopeIDStr := c.Query("scope_id"); scopeIDStr != "" {
		if scopeID, err := strconv.ParseUint(scopeIDStr, 10, 32); err == nil {
			id := uint(scopeID)
			filter.ScopeID = &id
		}
	}

	if isPublicStr := c.Query("is_public"); isPublicStr != "" {
		if isPublic, err := strconv.ParseBool(isPublicStr); err == nil {
			filter.IsPublic = &isPublic
		}
	}

	settings, err := h.settingsService.GetSettings(c.Request.Context(), tenantID, filter)
	if err != nil {
		h.logger.Error("Failed to get settings", "error", err)
		response.InternalServerErrorWithContext(c, "Failed to get settings")
		return
	}

	response.SuccessWithContext(c, settings)
}

// GetSetting godoc
// @Summary Get setting by ID
// @Description Get a specific setting by ID
// @Tags Settings
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path int true "Setting ID"
// @Success 200 {object} response.Response{data=models.SettingResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/settings/{id} [get]
func (h *SettingsHandler) GetSetting(c *gin.Context) {
	tenantID := middleware.GetTenantID(c)

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid setting ID")
		return
	}

	setting, err := h.settingsService.GetSettingByID(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		h.logger.Error("Failed to get setting", "error", err, "id", id)
		response.NotFoundWithContext(c, "Setting not found")
		return
	}

	response.SuccessWithContext(c, setting)
}

// CreateSetting godoc
// @Summary Create setting
// @Description Create a new setting
// @Tags Settings
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param request body models.SettingCreateRequest true "Setting create request"
// @Success 201 {object} response.Response{data=models.SettingResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/settings [post]
func (h *SettingsHandler) CreateSetting(c *gin.Context) {
	tenantID := middleware.GetTenantID(c)
	userID := middleware.GetUserID(c)

	var req models.SettingCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request body")
		return
	}

	if err := utils.ValidateStruct(&req); err != nil {
		response.BadRequestWithContext(c, "Validation failed")
		return
	}

	setting, err := h.settingsService.CreateSetting(c.Request.Context(), tenantID, userID, &req)
	if err != nil {
		h.logger.Error("Failed to create setting", "error", err)
		response.InternalServerErrorWithContext(c, "Failed to create setting")
		return
	}

	response.CreatedWithContext(c, setting)
}

// UpdateSetting godoc
// @Summary Update setting
// @Description Update an existing setting
// @Tags Settings
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path int true "Setting ID"
// @Param request body models.SettingUpdateRequest true "Setting update request"
// @Success 200 {object} response.Response{data=models.SettingResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/settings/{id} [put]
func (h *SettingsHandler) UpdateSetting(c *gin.Context) {
	tenantID := middleware.GetTenantID(c)
	userID := middleware.GetUserID(c)

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid setting ID")
		return
	}

	var req models.SettingUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request body")
		return
	}

	if err := utils.ValidateStruct(&req); err != nil {
		response.BadRequestWithContext(c, "Validation failed")
		return
	}

	setting, err := h.settingsService.UpdateSetting(c.Request.Context(), tenantID, userID, uint(id), &req)
	if err != nil {
		h.logger.Error("Failed to update setting", "error", err, "id", id)
		response.InternalServerErrorWithContext(c, "Failed to update setting")
		return
	}

	response.SuccessWithContext(c, setting)
}

// DeleteSetting godoc
// @Summary Delete setting
// @Description Delete a setting
// @Tags Settings
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path int true "Setting ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/settings/{id} [delete]
func (h *SettingsHandler) DeleteSetting(c *gin.Context) {
	tenantID := middleware.GetTenantID(c)
	userID := middleware.GetUserID(c)

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid setting ID")
		return
	}

	err = h.settingsService.DeleteSetting(c.Request.Context(), tenantID, userID, uint(id))
	if err != nil {
		h.logger.Error("Failed to delete setting", "error", err, "id", id)
		response.InternalServerErrorWithContext(c, "Failed to delete setting")
		return
	}

	response.SuccessWithContext(c, nil)
}

// GetSettingsByCategory godoc
// @Summary Get settings by category
// @Description Get all settings in a specific category
// @Tags Settings
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param category path string true "Category name"
// @Param scope_type query string true "Scope type" Enums(global,tenant,website,user)
// @Param scope_id query int false "Scope ID"
// @Success 200 {object} response.Response{data=[]models.SettingResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/settings/category/{category} [get]
func (h *SettingsHandler) GetSettingsByCategory(c *gin.Context) {
	tenantID := middleware.GetTenantID(c)
	category := c.Param("category")

	filter := models.SettingFilter{
		ScopeType: models.ScopeType(c.Query("scope_type")),
		Category:  category,
	}

	if scopeIDStr := c.Query("scope_id"); scopeIDStr != "" {
		if scopeID, err := strconv.ParseUint(scopeIDStr, 10, 32); err == nil {
			id := uint(scopeID)
			filter.ScopeID = &id
		}
	}

	settings, err := h.settingsService.GetSettings(c.Request.Context(), tenantID, filter)
	if err != nil {
		h.logger.Error("Failed to get settings by category", "error", err, "category", category)
		response.InternalServerErrorWithContext(c, "Failed to get settings")
		return
	}

	response.SuccessWithContext(c, settings)
}

// GetSettingValue godoc
// @Summary Get setting value
// @Description Get the value of a specific setting by category and key
// @Tags Settings
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param category path string true "Category name"
// @Param key path string true "Setting key"
// @Param scope_type query string true "Scope type" Enums(global,tenant,website,user)
// @Param scope_id query int false "Scope ID"
// @Success 200 {object} response.Response{data=interface{}}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/settings/{category}/{key}/value [get]
func (h *SettingsHandler) GetSettingValue(c *gin.Context) {
	tenantID := middleware.GetTenantID(c)
	category := c.Param("category")
	key := c.Param("key")
	scopeType := models.ScopeType(c.Query("scope_type"))

	var scopeID *uint
	if scopeIDStr := c.Query("scope_id"); scopeIDStr != "" {
		if id, err := strconv.ParseUint(scopeIDStr, 10, 32); err == nil {
			sid := uint(id)
			scopeID = &sid
		}
	}

	value, err := h.settingsService.GetSettingValue(c.Request.Context(), tenantID, scopeType, scopeID, category, key)
	if err != nil {
		h.logger.Error("Failed to get setting value", "error", err, "category", category, "key", key)
		response.NotFoundWithContext(c, "Setting not found")
		return
	}

	response.SuccessWithContext(c, value)
}

// SetSettingValue godoc
// @Summary Set setting value
// @Description Set the value of a specific setting by category and key
// @Tags Settings
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param category path string true "Category name"
// @Param key path string true "Setting key"
// @Param request body models.SetSettingValueRequest true "Setting value request"
// @Success 200 {object} response.Response{data=interface{}}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/settings/{category}/{key}/value [put]
func (h *SettingsHandler) SetSettingValue(c *gin.Context) {
	tenantID := middleware.GetTenantID(c)
	userID := middleware.GetUserID(c)
	category := c.Param("category")
	key := c.Param("key")

	var req models.SetSettingValueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request body")
		return
	}

	if err := utils.ValidateStruct(&req); err != nil {
		response.BadRequestWithContext(c, "Validation failed")
		return
	}

	err := h.settingsService.SetSettingValue(c.Request.Context(), tenantID, userID, req.ScopeType, req.ScopeID, category, key, req.Value)
	if err != nil {
		h.logger.Error("Failed to set setting value", "error", err, "category", category, "key", key)
		response.InternalServerErrorWithContext(c, "Failed to set setting value")
		return
	}

	response.SuccessWithContext(c, nil)
}

// GetCategories godoc
// @Summary Get setting categories
// @Description Get all available setting categories
// @Tags Settings
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]models.SettingCategory}
// @Router /api/cms/v1/settings/categories [get]
func (h *SettingsHandler) GetCategories(c *gin.Context) {
	categories := models.PredefinedCategories()
	response.SuccessWithContext(c, categories)
}
