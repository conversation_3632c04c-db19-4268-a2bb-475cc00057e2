package ads

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// RegisterRoutes registers all ads module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// Initialize repository manager
	repoManager := mysql.NewRepositoryManager(db)

	// Initialize services
	serviceManager := services.NewServiceManager(repoManager)

	// Initialize validator
	v, err := validator.NewValidator()
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize validator: %v", err))
	}

	// Initialize handlers with validator
	campaignHandler := handlers.NewCampaignHandler(serviceManager, v)
	advertisementHandler := handlers.NewAdvertisementHandler(serviceManager, v)
	analyticsHandler := handlers.NewAnalyticsHandler(serviceManager)
	placementHandler := handlers.NewPlacementHandler(serviceManager)
	scheduleHandler := handlers.NewScheduleHandler(serviceManager)
	targetingHandler := handlers.NewTargetingHandler(serviceManager)

	// Ads routes group
	adsGroup := router.Group("/ads")

	// Protected routes (require tenant context)
	protectedAdsGroup := adsGroup.Group("")
	protectedAdsGroup.Use(middleware.TenantContextMiddleware())

	// Campaign routes
	campaignRoutes := protectedAdsGroup.Group("/campaigns")
	{
		campaignRoutes.POST("", campaignHandler.CreateCampaign)
		campaignRoutes.GET("", campaignHandler.ListCampaigns)
		campaignRoutes.GET("/:id", campaignHandler.GetCampaign)
		campaignRoutes.PUT("/:id", campaignHandler.UpdateCampaign)
		campaignRoutes.DELETE("/:id", campaignHandler.DeleteCampaign)
		campaignRoutes.POST("/:id/activate", campaignHandler.ActivateCampaign)
		campaignRoutes.POST("/:id/pause", campaignHandler.PauseCampaign)
		campaignRoutes.GET("/:id/status", campaignHandler.GetCampaignStatus)
		campaignRoutes.GET("/:id/analytics", analyticsHandler.GetCampaignAnalytics)
		campaignRoutes.GET("/:id/advertisements", advertisementHandler.ListAdvertisementsByCampaign)
	}

	// Advertisement routes
	advertisementRoutes := protectedAdsGroup.Group("/advertisements")
	{
		advertisementRoutes.POST("", advertisementHandler.CreateAdvertisement)
		advertisementRoutes.GET("", advertisementHandler.ListAdvertisements)
		advertisementRoutes.GET("/:id", advertisementHandler.GetAdvertisement)
		advertisementRoutes.PUT("/:id", advertisementHandler.UpdateAdvertisement)
		advertisementRoutes.DELETE("/:id", advertisementHandler.DeleteAdvertisement)
		advertisementRoutes.POST("/:id/activate", advertisementHandler.ActivateAdvertisement)
		advertisementRoutes.POST("/:id/pause", advertisementHandler.PauseAdvertisement)
		advertisementRoutes.GET("/:id/analytics", analyticsHandler.GetAdvertisementAnalytics)
		advertisementRoutes.GET("/:id/targeting-rules", targetingHandler.GetRulesByAdvertisement)
	}

	// Schedule routes
	scheduleRoutes := protectedAdsGroup.Group("/schedules")
	{
		scheduleRoutes.POST("", scheduleHandler.CreateSchedule)
		scheduleRoutes.GET("", scheduleHandler.ListSchedules)
		scheduleRoutes.GET("/:id", scheduleHandler.GetSchedule)
		scheduleRoutes.PUT("/:id", scheduleHandler.UpdateSchedule)
		scheduleRoutes.DELETE("/:id", scheduleHandler.DeleteSchedule)
		scheduleRoutes.GET("/active", scheduleHandler.GetActiveSchedules)
		scheduleRoutes.GET("/:id/status", scheduleHandler.CheckScheduleStatus)
	}

	// Placement routes
	placementRoutes := protectedAdsGroup.Group("/placements")
	{
		placementRoutes.POST("", placementHandler.CreatePlacement)
		placementRoutes.GET("", placementHandler.ListPlacements)
		placementRoutes.GET("/:id", placementHandler.GetPlacement)
		placementRoutes.PUT("/:id", placementHandler.UpdatePlacement)
		placementRoutes.DELETE("/:id", placementHandler.DeletePlacement)
		placementRoutes.GET("/page/:page_type", placementHandler.GetPlacementsByPage)
		placementRoutes.GET("/optimal", placementHandler.GetOptimalPlacements)
		placementRoutes.POST("/validate", placementHandler.ValidatePlacement)
	}

	// Targeting rules routes
	targetingRoutes := protectedAdsGroup.Group("/targeting-rules")
	{
		targetingRoutes.POST("", targetingHandler.CreateTargetingRule)
		targetingRoutes.GET("", targetingHandler.ListTargetingRules)
		targetingRoutes.GET("/:id", targetingHandler.GetTargetingRule)
		targetingRoutes.PUT("/:id", targetingHandler.UpdateTargetingRule)
		targetingRoutes.DELETE("/:id", targetingHandler.DeleteTargetingRule)
		targetingRoutes.POST("/evaluate", targetingHandler.EvaluateRules)
		targetingRoutes.POST("/validate", targetingHandler.ValidateRule)
	}

	// Analytics routes
	analyticsRoutes := protectedAdsGroup.Group("/analytics")
	{
		analyticsRoutes.GET("/dashboard", analyticsHandler.GetDashboard)
		analyticsRoutes.GET("/export", analyticsHandler.ExportAnalytics)
		analyticsRoutes.POST("/aggregate", analyticsHandler.AggregateAnalytics)
	}

	// Tracking routes (public, no auth required for impression/click tracking)
	trackingGroup := router.Group("/ads/track")
	{
		trackingGroup.POST("/impression", analyticsHandler.RecordImpression)
		trackingGroup.POST("/click", analyticsHandler.RecordClick)
	}

	// Ad serving routes (public, for displaying ads)
	servingGroup := router.Group("/ads")
	{
		servingGroup.GET("/serve", advertisementHandler.ServeAd)
	}
}
