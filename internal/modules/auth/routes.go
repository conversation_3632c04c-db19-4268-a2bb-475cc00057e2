package auth

import (
"time"

"github.com/gin-gonic/gin"
govalidator "github.com/go-playground/validator/v10"
"github.com/tranthanhloi/wn-api-v3/internal/config"
"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/handlers"
"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/middleware"
	authrepos "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	authmysql "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	notificationrepos "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	notificationservices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"

	tenantrepos "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	tenantservices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	usermysql "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories/mysql"
	websitemysql "github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories/mysql"
	websiteservices "github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/cache"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all auth module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger) {
	// Load config for JWT service
	cfg, err := config.Load()
	if err != nil {
		logger.WithError(err).Error("Failed to load config")
		return
	}

	// Initialize repositories
	authUserRepo := authmysql.NewUserRepository(db, logger)
	sessionRepo := authmysql.NewSessionRepository(db)
	loginAttemptRepo := authmysql.NewLoginAttemptRepository(db)

	// Initialize user repositories for email verification
	userRepo := usermysql.NewUserRepository(db, logger)
	tokenBlacklistRepo := authmysql.NewTokenBlacklistRepository(db)
	tenantMembershipRepo := usermysql.NewTenantMembershipRepository(db, logger)
	passwordResetRepo := authmysql.NewPasswordResetRepository(db)
	invitationRepo := usermysql.NewUserInvitationRepository(db, logger)
	oauthProviderRepo := authmysql.NewOAuthProviderRepository(db)
	oauthConnectionRepo := authmysql.NewOAuthConnectionRepository(db)

	// Initialize tenant repositories for auth service
	tenantRepo := tenantrepos.NewMySQLTenantRepository(db)
	planRepo := tenantrepos.NewMySQLTenantPlanRepository(db)

	// Initialize services
	tokenBlacklistService := services.NewTokenBlacklistService(tokenBlacklistRepo, nil) // No cache for now
	jwtService, err := services.NewJWTService(cfg, tokenBlacklistService, tenantMembershipRepo)
	if err != nil {
		logger.WithError(err).Error("Failed to create JWT service")
		return
	}
	passwordService := services.NewPasswordService(&services.PasswordConfig{
		MinLength:          8,
		RequireUppercase:   true,
		RequireLowercase:   true,
		RequireNumbers:     true,
		RequireSpecialChar: false,
		BcryptCost:         12,
	})
	rateLimitService := services.NewRateLimitingService(5, 15*time.Minute, 60*time.Minute, loginAttemptRepo, logger)
	tenantService := tenantservices.NewTenantService(tenantRepo, planRepo)

	// Initialize notification services
	notificationRepo := notificationrepos.NewNotificationRepository(db)
	templateRepo := notificationrepos.NewTemplateRepository(db)
	recipientRepo := notificationrepos.NewRecipientRepository(db)
	logRepo := notificationrepos.NewLogRepository(db)
	templateService := notificationservices.NewTemplateService(templateRepo)
	emailProvider := notificationservices.CreateEmailProvider(&cfg.Notification, logger)
	socketProvider := notificationservices.CreateSocketProvider(&cfg.Notification, logger)
	smsProvider := notificationservices.CreateSMSProvider(&cfg.Notification, logger)
	pushProvider := notificationservices.CreatePushProvider(&cfg.Notification, logger)
	deliveryService := notificationservices.NewDeliveryService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		templateService,
		emailProvider,
		socketProvider,
		smsProvider,
		pushProvider,
		logger,
	)
	notificationService := notificationservices.NewNotificationService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		deliveryService,
	)

	// Create website service for domain resolution
	websiteRepo := websitemysql.NewWebsiteRepository(db)
	baseDomain := utils.GetEnv("BASE_DOMAIN", "example.com")
	domainService := websiteservices.NewDomainService(websiteRepo, baseDomain)
	playgroundValidator := govalidator.New()
	websiteService := websiteservices.NewWebsiteService(
		websiteRepo,
		domainService,
		playgroundValidator,
	)

	// Create email verification service
	tokenRepo := authrepos.NewEmailVerificationTokenRepository(db)
	emailVerificationService := services.NewEmailVerificationService(
		tokenRepo,
		userRepo,
		notificationService,
		templateRepo,
		websiteService,
		tenantService,
		tenantMembershipRepo,
		&cfg.Auth,
		logger,
	)

	authService := services.NewAuthService(
		authUserRepo,
		sessionRepo,
		loginAttemptRepo,
		jwtService,
		passwordService,
		rateLimitService,
		logger,
		&services.AuthConfig{
			RequireEmailVerification: true, // Enable email verification
			AllowRegistration:        true,
			MaxLoginAttempts:         5,
			LockoutDuration:          15 * time.Minute,
			SessionTimeout:           24 * time.Hour,
			RefreshTokenTTL:          7 * 24 * time.Hour,
			TwoFactorIssuer:          "Blog API",
		},
		tenantService,            // tenantService
		invitationRepo,           // invitationRepo
		tenantMembershipRepo,     // membershipRepo
		notificationService,      // notificationService
		emailVerificationService, // emailVerificationService
	)

	// Create password reset service
	passwordResetService := services.NewPasswordResetService(
		passwordResetRepo,
		userRepo,
		passwordService,
		notificationService,
		templateRepo,
		websiteService,
		logger,
	)

	// Create cache service (using Redis or in-memory cache)
	// TODO: Replace with actual cache implementation
	cacheService, err := cache.NewMemoryCache(nil)
	if err != nil {
		logger.WithError(err).Error("Failed to create cache service")
		return
	}

	// Create OAuth service
	oauthService := services.NewOAuthService(
		oauthProviderRepo,
		oauthConnectionRepo,
		userRepo,
		cacheService,
		logger,
		cfg,
		jwtService,
		sessionRepo,
	)

	// Initialize handlers - use v directly instead of RequestValidator
	authHandler := handlers.NewAuthHandler(authService, jwtService, passwordService, tenantMembershipRepo, v, logger)
	emailHandler := handlers.NewEmailHandler(authService, emailVerificationService, passwordResetService, v, logger)
	twoFactorHandler := handlers.NewTwoFactorHandler(authService, v, logger)
	emailTestHandler := handlers.NewEmailTestHandler(notificationService, logger)
	oauthHandler := handlers.NewOAuthHandler(oauthService, logger)

	// Initialize session manager for middleware
	sessionManager := httpmiddleware.NewSessionManager(sessionRepo, logger)

	// Auth routes with rate limiting and security middleware
	authRoutes := router.Group("/auth")
	authRoutes.Use(httpmiddleware.EnhancedCORSMiddleware())
	authRoutes.Use(httpmiddleware.SecurityHeadersMiddleware())
	authRoutes.Use(httpmiddleware.AuthRateLimitingMiddleware(logger))
	authRoutes.Use(httpmiddleware.ValidateJSONContentType())
	authRoutes.Use(httpmiddleware.TenantContextMiddleware())
	authRoutes.Use(httpmiddleware.RequestLoggingMiddleware(logger))

	// Public auth endpoints (no authentication required)
	publicRoutes := authRoutes.Group("")
	publicRoutes.Use(middleware.UserJourneyTracingMiddleware())
	publicRoutes.Use(httpmiddleware.PreventBruteForceMiddleware(rateLimitService))
	{
		publicRoutes.POST("/login", authHandler.Login)
		publicRoutes.POST("/register", authHandler.Register)
		publicRoutes.POST("/refresh", authHandler.RefreshToken)
		publicRoutes.POST("/verify-email", emailHandler.VerifyEmail)
		publicRoutes.POST("/resend-verification", emailHandler.ResendVerificationEmail)
		publicRoutes.GET("/verification-status", emailHandler.GetVerificationStatus)
		publicRoutes.POST("/forgot-password", emailHandler.ForgotPassword)
		publicRoutes.POST("/reset-password", emailHandler.ResetPassword)

		// OAuth endpoints
		publicRoutes.POST("/oauth/auth-url", oauthHandler.GetOAuthAuthURL)
		publicRoutes.POST("/oauth/login", oauthHandler.LoginWithOAuth)
		publicRoutes.POST("/oauth/google/auth-url", oauthHandler.GetGoogleAuthURL)
		publicRoutes.POST("/oauth/google/login", oauthHandler.LoginWithGoogle)
		publicRoutes.POST("/oauth/facebook/auth-url", oauthHandler.GetFacebookAuthURL)
		publicRoutes.POST("/oauth/facebook/login", oauthHandler.LoginWithFacebook)

		// Email test endpoints (for development/testing)
		publicRoutes.POST("/test-email", emailTestHandler.SendTestEmail)
		publicRoutes.POST("/test-smtp", emailTestHandler.TestSMTPConnection)
	}

	// Protected auth endpoints (require authentication)
	protectedRoutes := authRoutes.Group("")
	protectedRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	protectedRoutes.Use(sessionManager.SessionValidationMiddleware())
	protectedRoutes.Use(sessionManager.SessionExtensionMiddleware(24 * time.Hour))
	protectedRoutes.Use(sessionManager.DeviceTrackingMiddleware())
	{
		protectedRoutes.POST("/logout", authHandler.Logout)
		protectedRoutes.POST("/logout-all", authHandler.LogoutAllDevices)
		protectedRoutes.GET("/profile", authHandler.GetProfile)
		protectedRoutes.GET("/sessions", authHandler.GetActiveSessions)
		protectedRoutes.DELETE("/sessions/:sessionId", authHandler.RevokeSession)
		protectedRoutes.GET("/verification-token-stats", emailHandler.GetVerificationTokenStats)

		// Multi-tenant authentication routes
		protectedRoutes.POST("/switch-tenant", authHandler.SwitchTenant)
		protectedRoutes.POST("/refresh-with-tenant", authHandler.RefreshTokenWithTenant)

		// OAuth management routes (require authentication)
		protectedRoutes.GET("/oauth/connections", oauthHandler.GetOAuthConnections)
		protectedRoutes.POST("/oauth/:provider/disconnect", oauthHandler.DisconnectOAuth)
	}

	// 2FA routes - moved to separate group under /2fa (not /auth/2fa)
	twoFactorRoutes := router.Group("/2fa")
	twoFactorRoutes.Use(httpmiddleware.EnhancedCORSMiddleware())
	twoFactorRoutes.Use(httpmiddleware.SecurityHeadersMiddleware())
	twoFactorRoutes.Use(httpmiddleware.AuthRateLimitingMiddleware(logger))
	twoFactorRoutes.Use(httpmiddleware.ValidateJSONContentType())
	twoFactorRoutes.Use(httpmiddleware.TenantContextMiddleware())
	twoFactorRoutes.Use(httpmiddleware.RequestLoggingMiddleware(logger))
	twoFactorRoutes.Use(middleware.UserJourneyTracingMiddleware())
	{
		// Public 2FA endpoint
		twoFactorRoutes.POST("/complete-login", twoFactorHandler.CompleteLogin)

		// Protected 2FA endpoints
		protectedTwoFactorRoutes := twoFactorRoutes.Group("")
		protectedTwoFactorRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
		protectedTwoFactorRoutes.Use(sessionManager.SessionValidationMiddleware())
		protectedTwoFactorRoutes.Use(sessionManager.SessionExtensionMiddleware(24 * time.Hour))
		protectedTwoFactorRoutes.Use(sessionManager.DeviceTrackingMiddleware())
		{
			protectedTwoFactorRoutes.POST("/enable", twoFactorHandler.EnableTwoFactor)
			protectedTwoFactorRoutes.POST("/complete-setup", twoFactorHandler.CompleteTwoFactorSetup)
			protectedTwoFactorRoutes.POST("/disable", twoFactorHandler.DisableTwoFactor)
			protectedTwoFactorRoutes.POST("/verify", twoFactorHandler.VerifyTwoFactor)
		}
	}

	// Admin routes (require admin role)
	adminRoutes := authRoutes.Group("/admin")
	adminRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	adminRoutes.Use(httpmiddleware.RequireAdminRole())
	adminRoutes.Use(sessionManager.SessionValidationMiddleware())
	{
		// TODO: Add admin-specific auth routes
		// e.g., user management, session management, etc.
	}

	// Health check for auth module
	authRoutes.GET("/health", func(c *gin.Context) {
		response.Success(c.Writer, gin.H{
			"module":  "auth",
			"status":  "healthy",
			"version": "1.0.0",
			"checks": gin.H{
				"database":     db != nil,
				"jwt_service":  jwtService != nil,
				"auth_service": authService != nil,
			},
		})
	})
}

// GetAuthServiceDependencies returns the auth service dependencies for external use
func GetAuthServiceDependencies(db *gorm.DB, logger utils.Logger) (*services.AuthService, *services.JWTService, *services.EmailVerificationService, error) {
	// Load config for JWT service
	cfg, err := config.Load()
	if err != nil {
		return nil, nil, nil, err
	}

	// Initialize repositories
	authUserRepo := authmysql.NewUserRepository(db, logger)
	sessionRepo := authmysql.NewSessionRepository(db)
	loginAttemptRepo := authmysql.NewLoginAttemptRepository(db)

	// Initialize user repositories for email verification
	userRepo := usermysql.NewUserRepository(db, logger)
	tokenBlacklistRepo := authmysql.NewTokenBlacklistRepository(db)
	tenantMembershipRepo := usermysql.NewTenantMembershipRepository(db, logger)
	invitationRepo := usermysql.NewUserInvitationRepository(db, logger)

	// Initialize tenant repositories for auth service
	tenantRepo := tenantrepos.NewMySQLTenantRepository(db)
	planRepo := tenantrepos.NewMySQLTenantPlanRepository(db)

	// Initialize services
	tokenBlacklistService := services.NewTokenBlacklistService(tokenBlacklistRepo, nil)
	jwtService, err := services.NewJWTService(cfg, tokenBlacklistService, tenantMembershipRepo)
	if err != nil {
		return nil, nil, nil, err
	}
	passwordService := services.NewPasswordService(&services.PasswordConfig{
		MinLength:          8,
		RequireUppercase:   true,
		RequireLowercase:   true,
		RequireNumbers:     true,
		RequireSpecialChar: false,
		BcryptCost:         12,
	})
	rateLimitService := services.NewRateLimitingService(5, 15*time.Minute, 60*time.Minute, loginAttemptRepo, logger)
	tenantService := tenantservices.NewTenantService(tenantRepo, planRepo)

	// Initialize notification services
	notificationRepo := notificationrepos.NewNotificationRepository(db)
	templateRepo := notificationrepos.NewTemplateRepository(db)
	recipientRepo := notificationrepos.NewRecipientRepository(db)
	logRepo := notificationrepos.NewLogRepository(db)
	templateService := notificationservices.NewTemplateService(templateRepo)
	emailProvider := notificationservices.CreateEmailProvider(&cfg.Notification, logger)
	socketProvider := notificationservices.CreateSocketProvider(&cfg.Notification, logger)
	smsProvider := notificationservices.CreateSMSProvider(&cfg.Notification, logger)
	pushProvider := notificationservices.CreatePushProvider(&cfg.Notification, logger)
	deliveryService := notificationservices.NewDeliveryService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		templateService,
		emailProvider,
		socketProvider,
		smsProvider,
		pushProvider,
		logger,
	)
	notificationService := notificationservices.NewNotificationService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		deliveryService,
	)

	// Create website service for domain resolution
	websiteRepo := websitemysql.NewWebsiteRepository(db)
	baseDomain := utils.GetEnv("BASE_DOMAIN", "example.com")
	domainService := websiteservices.NewDomainService(websiteRepo, baseDomain)
	playgroundValidator := govalidator.New()
	websiteService := websiteservices.NewWebsiteService(
		websiteRepo,
		domainService,
		playgroundValidator,
	)

	// Create email verification service
	tokenRepo := authrepos.NewEmailVerificationTokenRepository(db)
	emailVerificationService := services.NewEmailVerificationService(
		tokenRepo,
		userRepo,
		notificationService,
		templateRepo,
		websiteService,
		tenantService,
		tenantMembershipRepo,
		&cfg.Auth,
		logger,
	)

	authService := services.NewAuthService(
		authUserRepo,
		sessionRepo,
		loginAttemptRepo,
		jwtService,
		passwordService,
		rateLimitService,
		logger,
		&services.AuthConfig{
			RequireEmailVerification: true, // Enable email verification
			AllowRegistration:        true,
			MaxLoginAttempts:         5,
			LockoutDuration:          15 * time.Minute,
			SessionTimeout:           24 * time.Hour,
			RefreshTokenTTL:          7 * 24 * time.Hour,
			TwoFactorIssuer:          "Blog API",
		},
		tenantService,            // tenantService
		invitationRepo,           // invitationRepo
		tenantMembershipRepo,     // membershipRepo
		notificationService,      // notificationService
		emailVerificationService, // emailVerificationService
	)

	return &authService, &jwtService, &emailVerificationService, nil
}
