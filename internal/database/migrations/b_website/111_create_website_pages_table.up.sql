CREATE TABLE IF NOT EXISTS website_pages (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    template_id INT <PERSON>SIGNED NULL,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT NULL,
    canonical_url VARCHAR(500) NULL,
    page_type ENUM('static', 'dynamic', 'template') NOT NULL DEFAULT 'static',
    layout_type VARCHAR(100) NOT NULL DEFAULT 'default',
    is_homepage BOOLEAN NOT NULL DEFAULT FALSE,
    is_published BOOLEAN NOT NULL DEFAULT FALSE,
    requires_auth BOOLEAN NOT NULL DEFAULT FALSE,
    password_protected BOOLEAN NOT NULL DEFAULT FALSE,
    page_password VARCHAR(255) NULL,
    visibility ENUM('public', 'private', 'password', 'draft') NOT NULL DEFAULT 'draft',
    featured_image VARCHAR(500) NULL,
    status ENUM('draft', 'review', 'published', 'archived', 'deleted') NOT NULL DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    scheduled_at TIMESTAMP NULL,
    created_by INT UNSIGNED NOT NULL,
    updated_by INT UNSIGNED NULL,
    view_count INT UNSIGNED NOT NULL DEFAULT 0,
    last_viewed_at TIMESTAMP NULL,
    custom_fields JSON DEFAULT (JSON_OBJECT()),
    page_settings JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_website_pages_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_website_pages_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_website_pages_template_id FOREIGN KEY (template_id) REFERENCES website_templates(id) ON DELETE SET NULL,
    
    -- Unique constraints
    UNIQUE KEY uk_website_pages_tenant_website_slug (tenant_id, website_id, slug),
    UNIQUE KEY uk_website_pages_tenant_website_homepage (tenant_id, website_id, is_homepage),
    
    -- Indexes for performance
    INDEX idx_website_pages_tenant_id (tenant_id),
    INDEX idx_website_pages_website_id (website_id),
    INDEX idx_website_pages_status (status),
    INDEX idx_website_pages_published (is_published, published_at),
    INDEX idx_website_pages_visibility (visibility),
    INDEX idx_website_pages_page_type (page_type),
    INDEX idx_website_pages_created_by (created_by),
    INDEX idx_website_pages_scheduled (scheduled_at),
    
    -- Check constraints
    CONSTRAINT chk_website_pages_slug_format CHECK (slug REGEXP '^[a-z0-9-]+$')
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;