-- Create blog_categories table
CREATE TABLE IF NOT EXISTS blog_categories (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    slug VARCHAR(255) NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    parent_id INT UNSIGNED NULL,
    lft INT UNSIGNED NOT NULL DEFAULT 1,
    rgt INT UNSIGNED NOT NULL DEFAULT 2,
    level INT UNSIGNED NOT NULL DEFAULT 0,
    id_path VARCHAR(255) NULL,
    color VARCHAR(7) DEFAULT '#000000',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
    
    CONSTRAINT fk_blog_categories_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_categories_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_categories_parent FOREIGN KEY (parent_id) REFERENCES blog_categories(id) ON DELETE CASCADE,
    UNIQUE KEY uk_blog_categories_tenant_website_slug (tenant_id, website_id, slug),
    INDEX idx_blog_categories_tenant_id (tenant_id),
    INDEX idx_blog_categories_website_id (website_id),
    INDEX idx_blog_categories_tenant_website (tenant_id, website_id),
    INDEX idx_blog_categories_website_status (website_id, status),
    INDEX idx_blog_categories_lft_rgt (lft, rgt),
    INDEX idx_blog_categories_parent (parent_id),
    INDEX idx_blog_categories_level (level),
    INDEX idx_blog_categories_id_path (id_path)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;