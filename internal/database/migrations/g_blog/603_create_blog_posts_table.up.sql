-- Create blog_posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    slug VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content LONGTEXT NOT NULL,
    excerpt TEXT,
    author_id INT UNSIGNED NOT NULL,
    category_id INT UNSIGNED NULL,
    type ENUM('post', 'page', 'announcement') NOT NULL DEFAULT 'post',
    is_featured BOOLEAN DEFAULT FALSE,
    is_sticky BOOLEAN DEFAULT FALSE,
    allow_comments BOOLEAN DEFAULT TRUE,
    password VARCHAR(255) NULL,
    featured_image VARCHAR(500) NULL,
    view_count INT UNSIGNED DEFAULT 0,
    comment_count INT UNSIGNED DEFAULT 0,
    scheduled_at TIMESTAMP NULL,
    published_at TIMESTAMP NULL,
    seo_metadata JSON DEFAULT (JSON_OBJECT()),
    related_post_ids JSON DEFAULT (JSON_ARRAY()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('draft', 'review', 'published', 'scheduled', 'archived', 'rejected', 'deleted') NOT NULL DEFAULT 'draft',
    
    CONSTRAINT fk_blog_posts_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_posts_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_posts_author FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE RESTRICT,
    CONSTRAINT fk_blog_posts_category FOREIGN KEY (category_id) REFERENCES blog_categories(id) ON DELETE SET NULL,
    UNIQUE KEY uk_blog_posts_tenant_website_slug (tenant_id, website_id, slug),
    INDEX idx_blog_posts_tenant_id (tenant_id),
    INDEX idx_blog_posts_tenant_website (tenant_id, website_id),
    INDEX idx_blog_posts_tenant_status (tenant_id, status),
    INDEX idx_blog_posts_tenant_published (tenant_id, published_at),
    INDEX idx_blog_posts_tenant_author (tenant_id, author_id),
    INDEX idx_blog_posts_website_status (website_id, status),
    INDEX idx_blog_posts_published_at (published_at),
    INDEX idx_blog_posts_category (category_id),
    INDEX idx_blog_posts_author (author_id),
    INDEX idx_blog_posts_featured_status (is_featured, status),
    INDEX idx_blog_posts_type (type),
    INDEX idx_blog_posts_scheduled (scheduled_at),
    FULLTEXT idx_blog_posts_search (title, content, excerpt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;