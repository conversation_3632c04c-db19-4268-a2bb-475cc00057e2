-- Create blog_post_revisions table
CREATE TABLE IF NOT EXISTS blog_post_revisions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    revision_number INT UNSIGNED NOT NULL,
    title VARCHAR(255),
    content LONGTEXT,
    excerpt TEXT,
    changes_summary TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_blog_post_revisions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_revisions_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_revisions_post FOREI<PERSON><PERSON> (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_revisions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    UNIQUE KEY uk_blog_post_revisions_tenant_post_number (tenant_id, post_id, revision_number),
    INDEX idx_blog_post_revisions_tenant_id (tenant_id),
    INDEX idx_blog_post_revisions_website_id (website_id),
    INDEX idx_blog_post_revisions_tenant_website (tenant_id, website_id),
    INDEX idx_blog_post_revisions_tenant_post (tenant_id, post_id),
    INDEX idx_blog_post_revisions_tenant_user (tenant_id, user_id),
    INDEX idx_blog_post_revisions_post (post_id),
    INDEX idx_blog_post_revisions_user (user_id),
    INDEX idx_blog_post_revisions_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;