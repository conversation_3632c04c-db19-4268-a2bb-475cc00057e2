-- Migration: 621_remove_meta_columns_from_blog_post_revisions
-- Description: Remove meta_title and meta_description columns from blog_post_revisions table
-- Author: System
-- Date: 2025-01-29
-- Reason: Consolidating SEO metadata into centralized seo_meta table

-- Remove meta columns from blog_post_revisions table
ALTER TABLE blog_post_revisions 
DROP COLUMN meta_title,
DROP COLUMN meta_description;
