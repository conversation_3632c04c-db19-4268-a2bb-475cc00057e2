CREATE TABLE IF NOT EXISTS trello_workspace_members (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    workspace_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    role <PERSON><PERSON><PERSON>('owner', 'admin', 'member', 'observer', 'guest') NOT NULL DEFAULT 'member',
    invited_by INT UNSIGNED NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'pending', 'suspended', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_workspace_members_tenant_id FOREIG<PERSON>EY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_workspace_members_website_id FOREIGN <PERSON>E<PERSON> (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_workspace_members_workspace_id FOREIGN KEY (workspace_id) REFERENCES trello_workspaces(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_workspace_members_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_workspace_members_invited_by FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_workspace_members_workspace_user (workspace_id, user_id),
    
    -- Indexes
    INDEX idx_trello_workspace_members_tenant_id (tenant_id),
    INDEX idx_trello_workspace_members_website_id (website_id),
    INDEX idx_trello_workspace_members_workspace_id (workspace_id),
    INDEX idx_trello_workspace_members_user_id (user_id),
    INDEX idx_trello_workspace_members_tenant_website (tenant_id, website_id),
    INDEX idx_trello_workspace_members_tenant_workspace (tenant_id, workspace_id),
    INDEX idx_trello_workspace_members_tenant_user (tenant_id, user_id),
    INDEX idx_trello_workspace_members_tenant_status (tenant_id, status),
    INDEX idx_trello_workspace_members_invited_by (invited_by),
    INDEX idx_trello_workspace_members_role (role),
    INDEX idx_trello_workspace_members_joined_at (joined_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;