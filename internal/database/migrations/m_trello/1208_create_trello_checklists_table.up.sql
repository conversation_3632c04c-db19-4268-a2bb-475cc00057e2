CREATE TABLE IF NOT EXISTS trello_checklists (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    card_id INT UNSIGNED NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    position DECIMAL(10,5) NOT NULL,
    check_items_count INT UNSIGNED NOT NULL DEFAULT 0,
    check_items_checked_count INT UNSIGNED NOT NULL DEFAULT 0,
    created_by INT UNSIGNED NOT NULL,
    status ENUM('active', 'inactive', 'archived', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_checklists_tenant_id FOREIG<PERSON>EY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_checklists_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_checklists_card_id FOREIGN KEY (card_id) REFERENCES trello_cards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_checklists_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_checklists_card_position (card_id, position),
    
    -- Indexes
    INDEX idx_trello_checklists_tenant_id (tenant_id),
    INDEX idx_trello_checklists_website_id (website_id),
    INDEX idx_trello_checklists_card_id (card_id),
    INDEX idx_trello_checklists_tenant_website (tenant_id, website_id),
    INDEX idx_trello_checklists_tenant_card (tenant_id, card_id),
    INDEX idx_trello_checklists_tenant_status (tenant_id, status),
    INDEX idx_trello_checklists_created_by (created_by),
    INDEX idx_trello_checklists_position (position),
    INDEX idx_trello_checklists_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;