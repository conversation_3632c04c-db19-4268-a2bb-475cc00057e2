CREATE TABLE IF NOT EXISTS trello_card_members (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    card_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    assigned_by INT UNSIGNED NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_card_members_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_members_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_members_card_id FOREIGN KEY (card_id) REFERENCES trello_cards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_members_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_members_assigned_by FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_card_members_card_user (card_id, user_id),
    
    -- Indexes
    INDEX idx_trello_card_members_tenant_id (tenant_id),
    INDEX idx_trello_card_members_website_id (website_id),
    INDEX idx_trello_card_members_card_id (card_id),
    INDEX idx_trello_card_members_user_id (user_id),
    INDEX idx_trello_card_members_tenant_website (tenant_id, website_id),
    INDEX idx_trello_card_members_tenant_card (tenant_id, card_id),
    INDEX idx_trello_card_members_tenant_user (tenant_id, user_id),
    INDEX idx_trello_card_members_assigned_by (assigned_by),
    INDEX idx_trello_card_members_assigned_at (assigned_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;